<template>
  <el-dialog
    v-model="dialogVisible"
    title="查看数据"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="monitor-device-dialog"
  >
    <el-tabs v-model="activeTab" type="card">
      <!-- 曲线分析 Tab -->
      <el-tab-pane label="曲线分析" name="curve">
        <div class="curve-content">
          <!-- 监测指标选择 -->
          <div class="indicator-selector">
            <span class="label">监测指标:</span>
            <el-select 
              v-model="selectedIndicator" 
              placeholder="请选择监测指标" 
              class="indicator-select"
              @change="handleIndicatorChange"
            >
              <el-option 
                v-for="item in indicators" 
                :key="item.id" 
                :label="item.monitorIndexName" 
                :value="item.id"
              />
            </el-select>
          </div>

          <!-- 时间范围选择 -->
          <div class="time-range">
            <el-radio-group v-model="selectedTimeRange" @change="handleTimeRangeChange">
              <el-radio-button label="24h">近24小时</el-radio-button>
              <el-radio-button label="7d">近7天</el-radio-button>
              <el-radio-button label="30d">近30天</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
            
            <div v-if="selectedTimeRange === 'custom'" class="custom-time">
              <el-date-picker
                v-model="customTimeRange"
                type="datetimerange"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="fetchCurveData"
              />
            </div>
          </div>

          <!-- 图表容器 -->
          <div ref="chartContainer" class="chart-container" style="height: 400px;"></div>
        </div>
      </el-tab-pane>

      <!-- 数据列表 Tab -->
      <el-tab-pane label="数据列表" name="table">
        <div class="table-content">
          <!-- 时间范围选择 -->
          <div class="time-range">
            <el-radio-group v-model="tableTimeRange" @change="handleTableTimeRangeChange">
              <el-radio-button label="24h">近24小时</el-radio-button>
              <el-radio-button label="7d">近7天</el-radio-button>
              <el-radio-button label="30d">近30天</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
            
            <div v-if="tableTimeRange === 'custom'" class="custom-time">
              <el-date-picker
                v-model="customTableTimeRange"
                type="datetimerange"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="fetchTableData"
              />
            </div>
          </div>

          <!-- 导出按钮 -->
          <div class="table-header">
            <el-button type="primary" @click="handleExport">导出</el-button>
          </div>

          <!-- 数据表格 -->
          <el-table :data="tableData" style="width: 100%" height="400">
            <el-table-column prop="monitorIndexName" label="监测指标" min-width="120" />
            <el-table-column prop="monitorValue" label="监测值" min-width="100">
              <template #default="{ row }">
                {{ formatMonitorValue(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="monitorTime" label="监测时间" min-width="180" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="tablePage.current"
              v-model:page-size="tablePage.size"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.total"
              @size-change="handleTableSizeChange"
              @current-change="handleTableCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import * as echarts from 'echarts'

// API 导入
import { getMonitorIndicators as getGasMonitorIndicators, getMonitorCurve as getGasMonitorCurve, getMonitorCurvePageData as getGasMonitorCurvePageData } from '@/api/gas'
import { getMonitorIndicatorsList as getDrainageMonitorIndicators, getMonitorCurve as getDrainageMonitorCurve, getMonitorRecordPage as getDrainageMonitorRecordPage } from '@/api/drainage'
import { getMonitorIndicatorsList as getHeatingMonitorIndicators, getHeatingAlarmMonitorCurve as getHeatingMonitorCurve } from '@/api/heating'
import { getMonitorIndicatorsList as getBridgeMonitorIndicators, getEnvironmentMonitorCurve as getBridgeMonitorCurve, getEnvironmentMonitorRecordPage as getBridgeMonitorRecordPage } from '@/api/bridge'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 响应式数据
const activeTab = ref('curve')
const selectedIndicator = ref('')
const indicators = ref([])
const selectedTimeRange = ref('24h')
const customTimeRange = ref([])
const tableTimeRange = ref('24h')
const customTableTimeRange = ref([])
const chartContainer = ref(null)
let chartInstance = null

// 表格数据
const tableData = ref([])
const tablePage = ref({
  current: 1,
  size: 10,
  total: 0
})

// 获取时间范围
const getTimeRange = (range) => {
  const now = moment()
  let startTime, endTime

  switch (range) {
    case '24h':
      startTime = now.clone().subtract(24, 'hours')
      endTime = now
      break
    case '7d':
      startTime = now.clone().subtract(7, 'days')
      endTime = now
      break
    case '30d':
      startTime = now.clone().subtract(30, 'days')
      endTime = now
      break
    case 'custom':
      if (customTimeRange.value && customTimeRange.value.length === 2) {
        startTime = moment(customTimeRange.value[0])
        endTime = moment(customTimeRange.value[1])
      } else {
        return getTimeRange('24h')
      }
      break
    default:
      return getTimeRange('24h')
  }

  return {
    startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
    endTime: endTime.format('YYYY-MM-DD HH:mm:ss')
  }
}

// 获取监测指标
const fetchIndicators = async () => {
  if (!props.deviceData?.id) return

  try {
    let res
    const relatedBusiness = props.deviceData.relatedBusiness

    switch (relatedBusiness) {
      case 7000501: // 燃气
        res = await getGasMonitorIndicators(props.deviceData.id)
        break
      case 7000502: // 排水
        res = await getDrainageMonitorIndicators({ deviceId: props.deviceData.id })
        break
      case 7000503: // 供热
        res = await getHeatingMonitorIndicators({ deviceId: props.deviceData.id })
        break
      case 7000504: // 桥梁
        res = await getBridgeMonitorIndicators(props.deviceData.id)
        break
    }

    if (res && res.code === 200 && res.data) {
      indicators.value = res.data
      if (indicators.value.length > 0) {
        selectedIndicator.value = indicators.value[0].id
        await fetchCurveData()
      }
    }
  } catch (error) {
    console.error('获取监测指标失败:', error)
    ElMessage.error('获取监测指标失败')
  }
}

// 获取曲线数据
const fetchCurveData = async () => {
  if (!props.deviceData?.id || !selectedIndicator.value) return

  try {
    const timeRange = getTimeRange(selectedTimeRange.value)
    const params = {
      deviceId: props.deviceData.id,
      ...timeRange
    }

    let res
    const relatedBusiness = props.deviceData.relatedBusiness

    switch (relatedBusiness) {
      case 7000501: // 燃气
        res = await getGasMonitorCurve(params)
        break
      case 7000502: // 排水
        res = await getDrainageMonitorCurve(params)
        break
      case 7000503: // 供热
        res = await getHeatingMonitorCurve(params)
        break
      case 7000504: // 桥梁
        res = await getBridgeMonitorCurve(params)
        break
    }

    if (res && res.code === 200 && res.data) {
      renderChart(res.data)
    }
  } catch (error) {
    console.error('获取曲线数据失败:', error)
    ElMessage.error('获取曲线数据失败')
  }
}

// 渲染图表
const renderChart = (data) => {
  if (!chartContainer.value || !selectedIndicator.value) return

  const currentIndicator = indicators.value.find(item => item.id === selectedIndicator.value)
  if (!currentIndicator) return

  const { monitorField, type, measureRangeLow, measureRangeUp, monitorIndexName, measureUnit } = currentIndicator

  // 处理数据
  const chartData = data.filter(item => item[monitorField] !== null && item[monitorField] !== undefined)
    .map(item => ({
      time: item.monitorTime,
      value: item[monitorField]
    }))
    .sort((a, b) => new Date(a.time) - new Date(b.time))

  const times = chartData.map(item => item.time)
  const values = chartData.map(item => item.value)

  // 销毁之前的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(chartContainer.value)

  let option

  if (type === 0) {
    // 状态型指标（0/1）
    option = {
      title: {
        text: `${monitorIndexName}监测曲线`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const param = params[0]
          const status = param.value === 1 ? '正常' : '异常'
          return `${param.axisValue}<br/>${monitorIndexName}: ${status}`
        }
      },
      xAxis: {
        type: 'category',
        data: times,
        axisLabel: {
          formatter: (value) => {
            return moment(value).format('MM-DD HH:mm')
          }
        }
      },
      yAxis: {
        type: 'value',
        min: -0.5,
        max: 1.5,
        axisLabel: {
          formatter: (value) => {
            if (value === 0) return '异常'
            if (value === 1) return '正常'
            return ''
          }
        }
      },
      series: [{
        name: monitorIndexName,
        type: 'line',
        data: values,
        itemStyle: {
          color: (params) => params.value === 1 ? '#409EFF' : '#F56C6C'
        },
        lineStyle: {
          color: (params) => {
            // 根据数据点的值设置线条颜色
            return '#409EFF'
          }
        },
        step: 'end'
      }]
    }
  } else {
    // 数值型指标
    const series = [{
      name: monitorIndexName,
      type: 'line',
      data: values,
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      }
    }]

    // 添加上下限虚线
    if (measureRangeLow !== null && measureRangeLow !== undefined) {
      series.push({
        name: '下限',
        type: 'line',
        data: new Array(times.length).fill(parseFloat(measureRangeLow)),
        lineStyle: {
          type: 'dashed',
          color: '#E6A23C'
        },
        symbol: 'none'
      })
    }

    if (measureRangeUp !== null && measureRangeUp !== undefined) {
      series.push({
        name: '上限',
        type: 'line',
        data: new Array(times.length).fill(parseFloat(measureRangeUp)),
        lineStyle: {
          type: 'dashed',
          color: '#F56C6C'
        },
        symbol: 'none'
      })
    }

    option = {
      title: {
        text: `${monitorIndexName}监测曲线`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let result = `${params[0].axisValue}<br/>`
          params.forEach(param => {
            result += `${param.seriesName}: ${param.value}${measureUnit || ''}<br/>`
          })
          return result
        }
      },
      legend: {
        top: 30,
        data: series.map(s => s.name)
      },
      xAxis: {
        type: 'category',
        data: times,
        axisLabel: {
          formatter: (value) => {
            return moment(value).format('MM-DD HH:mm')
          }
        }
      },
      yAxis: {
        type: 'value',
        name: measureUnit || '',
        axisLabel: {
          formatter: (value) => `${value}${measureUnit || ''}`
        }
      },
      series
    }
  }

  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
}

// 获取表格数据
const fetchTableData = async () => {
  if (!props.deviceData?.id) return

  try {
    const timeRange = getTimeRange(tableTimeRange.value)
    const relatedBusiness = props.deviceData.relatedBusiness

    let res
    switch (relatedBusiness) {
      case 7000501: // 燃气
        res = await getGasMonitorCurvePageData(tablePage.value.current, tablePage.value.size, {
          deviceId: props.deviceData.id,
          ...timeRange
        })
        break
      case 7000502: // 排水
        res = await getDrainageMonitorRecordPage(tablePage.value.current, tablePage.value.size, {
          deviceId: props.deviceData.id,
          ...timeRange
        })
        break
             case 7000503: // 供热
         // 供热暂时使用报警监测曲线数据
         res = await getHeatingMonitorCurve({
           deviceId: props.deviceData.id,
           ...timeRange
         })
         // 手动构造分页格式
         if (res && res.data) {
           res.data = {
             records: res.data,
             total: res.data.length
           }
         }
         break
      case 7000504: // 桥梁
        res = await getBridgeMonitorRecordPage(tablePage.value.current, tablePage.value.size, {
          deviceId: props.deviceData.id,
          ...timeRange
        })
        break
    }

    if (res && res.code === 200 && res.data) {
      const records = res.data.records || res.data || []
      
      // 转换数据格式
      const transformedData = []
      indicators.value.forEach(indicator => {
        records.forEach(record => {
          const value = record[indicator.monitorField]
          if (value !== null && value !== undefined) {
            transformedData.push({
              monitorIndexName: indicator.monitorIndexName,
              monitorValue: value,
              monitorTime: record.monitorTime,
              monitorField: indicator.monitorField,
              type: indicator.type,
              measureUnit: indicator.measureUnit
            })
          }
        })
      })

      tableData.value = transformedData.sort((a, b) => new Date(b.monitorTime) - new Date(a.monitorTime))
      tablePage.value.total = res.data.total || transformedData.length
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
    ElMessage.error('获取表格数据失败')
  }
}

// 格式化监测值
const formatMonitorValue = (row) => {
  if (row.type === 0) {
    return row.monitorValue === 1 ? '正常' : '异常'
  }
  return `${row.monitorValue}${row.measureUnit || ''}`
}

// 事件处理
const handleIndicatorChange = () => {
  fetchCurveData()
}

const handleTimeRangeChange = () => {
  fetchCurveData()
}

const handleTableTimeRangeChange = () => {
  tablePage.value.current = 1
  fetchTableData()
}

const handleTableSizeChange = (size) => {
  tablePage.value.size = size
  tablePage.value.current = 1
  fetchTableData()
}

const handleTableCurrentChange = (current) => {
  tablePage.value.current = current
  fetchTableData()
}

const handleExport = () => {
  ElMessage.info('导出功能待实现')
}

const handleClose = () => {
  dialogVisible.value = false
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听设备数据变化
watch(() => props.deviceData, () => {
  if (props.visible && props.deviceData?.id) {
    fetchIndicators()
    fetchTableData()
  }
}, { deep: true })

// 监听弹窗可见性变化
watch(() => props.visible, (visible) => {
  if (visible && props.deviceData?.id) {
    nextTick(() => {
      fetchIndicators()
      fetchTableData()
    })
  }
})
</script>

<style scoped>
.monitor-device-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.curve-content, .table-content {
  margin-top: 10px;
}

.indicator-selector {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.label {
  font-weight: 500;
  margin-right: 8px;
  white-space: nowrap;
}

.indicator-select {
  width: 200px;
}

.time-range {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.custom-time {
  margin-left: 10px;
}

.chart-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-tabs__content) {
  min-height: 500px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}
</style> 