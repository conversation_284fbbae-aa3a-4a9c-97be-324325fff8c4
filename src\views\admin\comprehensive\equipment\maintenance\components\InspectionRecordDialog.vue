<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="inspection-record-dialog"
  >
    <div class="dialog-content">
      <!-- 巡检任务信息 -->
      <div class="section-title">巡检任务</div>
      <el-form
        ref="taskFormRef"
        :model="taskData"
        label-width="120px"
        :disabled="true"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="任务编号">
              <el-input v-model="taskData.taskCode" placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务名称">
              <el-input v-model="taskData.taskName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属专项">
              <el-input v-model="taskData.relatedBusinessName" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="巡检时间">
              <el-input v-model="taskData.inspectionTime" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="巡检人员">
              <el-input v-model="taskData.taskUserName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务状态">
              <el-input v-model="taskData.recordStatusName" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="巡检内容">
              <el-input
                v-model="taskData.taskContent"
                type="textarea"
                :rows="3"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 巡查设备 -->
      <div class="section-title">巡查设备</div>
      <div class="device-table">
        <el-table
          :data="deviceList"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          empty-text="暂无设备数据"
        >
          <el-table-column prop="deviceTypeName" label="设备类型" min-width="120" />
          <el-table-column prop="deviceName" label="设备名称" min-width="120" />
          <el-table-column prop="address" label="位置" min-width="150" />
          <el-table-column label="操作" fixed="right" min-width="100">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleDeviceLocation(row)">定位</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 巡检记录 -->
      <div class="section-title">巡检记录</div>
      <el-form
        ref="recordFormRef"
        :model="recordData"
        :rules="recordRules"
        label-width="120px"
        :disabled="mode === 'view'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="巡检完成时间" prop="completeTime">
              <el-date-picker
                v-model="recordData.completeTime"
                type="datetime"
                placeholder="请选择完成时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存在隐患" prop="existDanger">
              <el-radio-group v-model="recordData.existDanger">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="检查结果" prop="inspectionResult">
              <el-input
                v-model="recordData.inspectionResult"
                type="textarea"
                :rows="4"
                placeholder="请输入检查结果"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="现场照片" prop="picUrls">
              <div class="image-preview" v-if="mode === 'view' && imageList.length > 0">
                <el-image
                  v-for="(image, index) in imageList"
                  :key="index"
                  :src="image"
                  :preview-src-list="imageList"
                  :initial-index="index"
                  class="preview-image"
                  fit="cover"
                />
              </div>
              <el-upload
                v-else
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleFileChange"
                :file-list="fileList"
                list-type="picture-card"
                :limit="9"
                :disabled="mode === 'view'"
                multiple
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传格式：jpg、jpeg、png，最多支持上传3个，每个大小不能超过2MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode === 'report'">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  getInspectionRecordDetail, 
  reportInspectionRecord 
} from '@/api/comprehensive'
import { uploadFile } from '@/api/upload'
import { misPosition } from '@/hooks/gishooks'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'view', // 'view', 'report'
    validator: (value) => ['view', 'report'].includes(value)
  },
  recordId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const taskFormRef = ref(null)
const recordFormRef = ref(null)
const fileList = ref([])
const imageList = ref([])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'report' ? '巡检填报' : '查看'
})

// 任务数据
const taskData = reactive({
  taskCode: '',
  taskName: '',
  relatedBusinessName: '',
  inspectionTime: '',
  taskUserName: '',
  recordStatusName: '',
  taskContent: ''
})

// 巡检记录数据
const recordData = reactive({
  recordId: '',
  completeTime: '',
  existDanger: false,
  inspectionResult: '',
  picUrls: ''
})

// 设备列表
const deviceList = ref([])

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表单验证规则
const recordRules = {
  completeTime: [
    { required: true, message: '请选择完成时间', trigger: 'change' }
  ],
  existDanger: [
    { required: true, message: '请选择是否存在隐患', trigger: 'change' }
  ],
  inspectionResult: [
    { required: true, message: '请输入检查结果', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.recordId, (newVal) => {
  if (newVal && props.visible) {
    fetchRecordDetail()
  }
}, { immediate: true })

// 获取巡检记录详情
const fetchRecordDetail = async () => {
  if (!props.recordId) return
  
  try {
    const response = await getInspectionRecordDetail(props.recordId)
    if (response.code === 200) {
      const data = response.data
      
      // 设置任务数据
      Object.assign(taskData, {
        taskCode: data.taskCode || '',
        taskName: data.taskName || '',
        relatedBusinessName: data.relatedBusinessName || '',
        inspectionTime: data.inspectionTime || '',
        taskUserName: data.taskUserName || '',
        recordStatusName: data.recordStatusName || '',
        taskContent: data.taskContent || ''
      })
      
      // 设置记录数据
      Object.assign(recordData, {
        recordId: data.id,
        completeTime: data.completeTime || '',
        existDanger: data.existDanger || false,
        inspectionResult: data.inspectionResult || '',
        picUrls: data.picUrls || ''
      })
      
      // 设置设备列表
      deviceList.value = data.deviceList || []
      
      // 处理图片显示
      if (data.picUrls) {
        imageList.value = data.picUrls.split(',').filter(url => url.trim())
        fileList.value = imageList.value.map((url, index) => ({
          name: `image_${index}`,
          url: url,
          uid: Date.now() + index
        }))
      } else {
        imageList.value = []
        fileList.value = []
      }
    }
  } catch (error) {
    console.error('获取巡检记录详情失败:', error)
    ElMessage.error('获取巡检记录详情失败')
  }
}

// 处理设备定位
const handleDeviceLocation = (row) => {
  if (row.longitude && row.latitude) {
    misPosition.value = {
      longitude: parseFloat(row.longitude),
      latitude: parseFloat(row.latitude)
    }
  } else {
    ElMessage.warning('设备缺少定位信息')
  }
}

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return
  }

  // 检查文件类型
  const isImage = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.raw.type)
  if (!isImage) {
    ElMessage.error('只能上传 jpg、jpeg、png 格式的图片!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = recordData.picUrls ? recordData.picUrls.split(',') : []
      urls.push(response.data.url)
      recordData.picUrls = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  // 重置任务数据
  Object.keys(taskData).forEach(key => {
    taskData[key] = ''
  })
  
  // 重置记录数据
  Object.assign(recordData, {
    recordId: '',
    completeTime: '',
    existDanger: false,
    inspectionResult: '',
    picUrls: ''
  })
  
  // 重置其他数据
  deviceList.value = []
  fileList.value = []
  imageList.value = []
  
  // 重置表单验证
  if (recordFormRef.value) {
    recordFormRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!recordFormRef.value) return

  try {
    await recordFormRef.value.validate()

    const submitData = {
      recordId: recordData.recordId,
      completeTime: recordData.completeTime,
      existDanger: recordData.existDanger,
      inspectionResult: recordData.inspectionResult,
      picUrls: recordData.picUrls
    }

    const response = await reportInspectionRecord(submitData)
    if (response.code === 200) {
      ElMessage.success('填报成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.msg || '填报失败')
    }
  } catch (error) {
    console.error('表单验证失败或提交失败:', error)
  }
}
</script>

<style scoped>
.inspection-record-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.dialog-content {
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #0277FD;
}

.device-table {
  margin-bottom: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input),
:deep(.el-date-editor) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-image {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  cursor: pointer;
}
</style> 