<template>
  <PanelBox title="管网风险" class="left-top-panel">
    <div class="panel-content">
      <div class="content-wrapper">
        <div class="risk-chart">
          <div class="chart-container" ref="chartRef"></div>
          <div class="center-text">
            <div class="risk-total">{{ totalRisk }}</div>
            <div class="unit">KM</div>
          </div>
        </div>
        <div class="risk-list">
          <div class="risk-item" v-for="(item, index) in riskItems" :key="index">
            <div class="risk-indicator" :style="{ background: item.color }"></div>
            <div class="risk-name">{{ item.name }}</div>
            <div class="risk-value">{{ item.value }} <span class="unit-text">KM</span></div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getPipelineRiskPipelineStatistics } from '@/api/gas'

// 综合态势总览左上面板组件

// 定义数据源
const chartRef = ref(null)
let chartInstance = null

// 定义风险数据
const riskData = ref({
  bigRiskLength: 0,
  largerRiskLength: 0,
  generalRiskLength: 0,
  lowRiskLength: 0
})

// 风险等级配置
const riskConfig = [
  { name: '重大风险', key: 'bigRiskLength', color: '#EC1616' },
  { name: '较大风险', key: 'largerRiskLength', color: '#FA9700' },
  { name: '一般风险', key: 'generalRiskLength', color: '#FFE100' },
  { name: '低风险', key: 'lowRiskLength', color: '#23CAFF' }
]

// 计算当前展示数据
const riskItems = computed(() => {
  return riskConfig.map(item => ({
    name: item.name,
    value: riskData.value[item.key],
    color: item.color
  }))
})

// 计算总风险值
const totalRisk = computed(() => {
  const sum = riskItems.value.reduce((sum, item) => sum + item.value, 0)
  return sum.toFixed(2)
})

// 获取风险数据
const fetchRiskData = async () => {
  try {
    const res = await getPipelineRiskPipelineStatistics()
    if (res.code === 200) {
      riskData.value = res.data
      updateChart()
    }
  } catch (error) {
    console.error('获取管网风险数据失败:', error)
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
  
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return

  const data = riskItems.value
  const colorList = data.map(item => item.color)
  const valueList = data.map(item => item.value)

  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['75%', '85%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      silent: true,
      data: valueList.map((value, index) => ({
        value,
        name: data[index].name,
        itemStyle: {
          color: colorList[index]
        }
      }))
    }]
  }
  
  chartInstance.setOption(option)
}

onMounted(async () => {
  await nextTick()
  initChart()
  await fetchRiskData()
})
</script>

<style scoped>
.left-top-panel {
  height: 280px; /* 默认高度为280px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 100%;
}

.risk-chart {
  width: 192px;
  height: 192px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 192px;
  height: 192px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.risk-total {
  font-size: 16px;
  font-weight: bold;
  color: #22CBFF;
  margin-bottom: 4px;
}

.unit {
  font-size: 12px;
  color: #85A5C3;
}

.risk-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  align-items: center;
  width: 225px;
  height: 34px;
  background: linear-gradient(270deg, rgba(48,71,104,0.5) 0%, #304768 50%, rgba(48,71,104,0.5) 100%);
  border: 1px solid;
  opacity: 1;
  border-image: linear-gradient(270deg, rgba(171, 204, 255, 0), rgba(171, 204, 255, 0.5), rgba(171, 204, 255, 0)) 1 1;
  padding: 0 15px;
}

.risk-indicator {
  width: 9px;
  height: 8px;
  transform: skew(-20deg);
  margin-right: 8px;
}

.risk-name {
  width: 70px;
  color: #D3E5FF;
  font-size: 14px;
}

.risk-value {
  color: #ffffff;
  font-size: 14px;
  width: 150px;
  text-align: right;
  margin-left: auto;
}

.unit-text {
  color: #85A5C3;
  font-size: 12px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-top-panel {
    height: 280px;
  }
}

@media screen and (max-width: 1919px) {
  .left-top-panel {
    height: 260px;
  }
}

@media screen and (min-width: 2561px) {
  .left-top-panel {
    height: 310px;
  }
}
</style>