<template>
  <el-select
    :model-value="modelValue"
    class="common-select"
    :placeholder="placeholder"
    @update:model-value="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// 定义props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    required: true
  },
  options: {
    type: Array,
    required: true,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择'
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'change'])

// 处理选择变化
const handleChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
.common-select {
  width: 100px;
}
:deep(.el-select__wrapper) {
    align-items: center;
    background-color: transparent;
    border-radius: var(--el-border-radius-base);
    box-shadow: 0 0 0 0 var(--el-border-color) inset;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    gap: 6px;
    line-height: 24px;
    min-height: 32px;
    padding: 4px 12px;
    position: relative;
    text-align: left;
    transform: translateZ(0);
    transition: var(--el-transition-duration);
}
:deep(.el-select__placeholder) {
  color: #999;
}
</style>