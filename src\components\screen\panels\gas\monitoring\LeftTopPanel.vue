<template>
  <PanelBox title="监测设备" class="gas-monitoring-left-top-panel">
    <div class="panel-content">
      <div class="device-stats">
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer blue-dot"></div>
            <div class="dot-inner blue-dot"></div>
          </div>
          <div class="stat-label">设备总数</div>
          <div class="stat-value blue-value">{{ deviceData.total }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer yellow-dot"></div>
            <div class="dot-inner yellow-dot"></div>
          </div>
          <div class="stat-label">离线</div>
          <div class="stat-value yellow-value">{{ deviceData.offlineNum }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer green-dot"></div>
            <div class="dot-inner green-dot"></div>
          </div>
          <div class="stat-label">设备在线率</div>
          <div class="stat-value green-value">{{ deviceData.onlineRate }}</div>
        </div>
      </div>
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, reactive } from 'vue';
import PanelBox from '@/components/screen/PanelBox.vue';
import * as echarts from 'echarts';
import { getMonitorAnalysisDeviceStatistics } from '@/api/gas';

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 设备数据
const deviceData = reactive({
  total: 0,
  offlineNum: 0,
  onlineRate: '0%',
  categories: [],
  online: [],
  offline: []
});

// 获取设备监控数据
const fetchDeviceData = async () => {
  try {
    const response = await getMonitorAnalysisDeviceStatistics();
    if (response.code === 200 && response.data) {
      const { totalCount, offlineCount, onlineRate, deviceTypeStatistics } = response.data;

      deviceData.total = totalCount;
      deviceData.offlineNum = offlineCount;
      deviceData.onlineRate = onlineRate.toFixed(2) + '%';

      // 处理设备类型统计数据
      deviceData.categories = deviceTypeStatistics.map(item => item.deviceTypeName);
      deviceData.online = deviceTypeStatistics.map(item => item.onlineCount || 0);
      deviceData.offline = deviceTypeStatistics.map(item => {
        // 如果offlineCount为null，则用总数减去在线数
        if (item.offlineCount === null) {
          return (item.totalCount || 0) - (item.onlineCount || 0);
        }
        return item.offlineCount || 0;
      });

      // 更新图表
      updateChart();
    }
  } catch (error) {
    console.error('获取设备监控数据失败:', error);
  }
};

// 初始化图表
const initChart = async () => {
  // 使用nextTick确保DOM已完全渲染
  await nextTick();

  if (!chartRef.value) {
    console.error('图表容器未找到');
    return;
  }

  try {
    // 如果已有实例，先销毁
    if (chartInstance) {
      chartInstance.dispose();
    }

    // 初始化图表
    chartInstance = echarts.init(chartRef.value);
    updateChart();

    // 窗口大小改变时自动调整图表大小
    window.addEventListener('resize', resizeChart);
  } catch (error) {
    console.error('初始化图表失败:', error);
  }
};

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.error('图表实例不存在');
    return;
  }

  const option = {
    grid: {
      top: '30px',
      left: '3%',
      right: '5%',
      bottom: '28%',
      containLabel: true
    },
    legend: {
      orient: 'horizontal',
      right: 10,
      top: 10,
      itemWidth: 12,
      itemHeight: 4,
      itemGap: 16,
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      },
      data: [
        {
          name: '在线',
          icon: 'rect'
        },
        {
          name: '离线',
          icon: 'rect'
        }
      ]
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
    },
    xAxis: {
      type: 'category',
      data: deviceData.categories,
      axisLine: {
        lineStyle: {
          color: '#5F5F60'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400,
        padding: [0, 0, 0, 0]
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      }
    },
    series: [
      {
        name: '在线',
        type: 'bar',
        barWidth: 10,
        barGap: '30%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#40CDFF' },
            { offset: 1, color: 'rgba(0,59,89,0.01)' }
          ])
        },
        data: deviceData.online
      },
      {
        name: '离线',
        type: 'bar',
        barWidth: 10,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#FFD11C' },
            { offset: 1, color: 'rgba(89,60,0,0.01)' }
          ])
        },
        data: deviceData.offline
      }
    ]
  };

  try {
    chartInstance.setOption(option);
  } catch (error) {
    console.error('设置图表选项失败:', error);
  }
};

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};



onMounted(() => {
  // 在组件挂载后初始化图表
  initChart();
  fetchDeviceData();
});

onBeforeUnmount(() => {
  if (chartInstance) {
    window.removeEventListener('resize', resizeChart);
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped>
.gas-monitoring-left-top-panel {
  height: 280px;
  /* 默认高度为280px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.device-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
}

.stat-item {
  display: flex;
  gap: 6px;
  align-items: center;
}

.dot-wrapper {
  position: relative;
  width: 9px;
  height: 9px;
}

.dot-outer {
  position: absolute;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.dot-inner {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
}

.blue-dot.dot-outer {
  background: rgba(5, 90, 219, 0.4);
}

.blue-dot.dot-inner {
  background: #055ADB;
}

.yellow-dot.dot-outer {
  background: rgba(255, 209, 28, 0.4);
}

.yellow-dot.dot-inner {
  background: #FFD11C;
}

.green-dot.dot-outer {
  background: rgba(63, 216, 124, 0.4);
}

.green-dot.dot-inner {
  background: #3FD87C;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
}

.blue-value {
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #3CF3FF;
}

.yellow-value {
  color: #FFD11C;
}

.green-value {
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #3CF3FF;
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 220px;
  /* 确保图表容器有最小高度 */
  height: 220px;
  /* 设置一个固定高度 */
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-left-top-panel {
    height: 280px;
  }

  .chart-container {
    min-height: 220px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-left-top-panel {
    height: 260px;
  }

  .chart-container {
    min-height: 200px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-left-top-panel {
    height: 310px;
  }

  .chart-container {
    min-height: 220px;
  }
}
</style>