<template>
  <div class="bridge-point-container">
    <div class="content-wrapper">
      <!-- 左侧桥梁列表 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3 class="panel-title">桥梁名称</h3>
          <el-input
            v-model="bridgeSearchText"
            placeholder="桥梁名称"
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="bridge-list">
          <div
            v-for="bridge in filteredBridgeList"
            :key="bridge.id"
            :class="['bridge-item', { active: selectedBridgeId === bridge.id }]"
            @click="handleBridgeSelect(bridge)"
          >
            {{ bridge.bridgeName }}
          </div>
        </div>
      </div>

      <!-- 右侧方案列表 -->
      <div class="right-panel">
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-form">
            <div class="form-item">
              <span class="label">方案名称:</span>
              <el-input
                v-model="searchForm.schemeName"
                class="form-input"
                placeholder="输入方案名称"
              />
            </div>
            <div class="form-item">
              <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
              <el-button class="reset-btn" @click="handleReset">重置</el-button>
            </div>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="table-header">
          <div class="button-group">
            <el-button type="primary" class="operation-btn" @click="handleAdd">+ 上传方案</el-button>
          </div>
        </div>

        <!-- 表格区域 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          :row-class-name="tableRowClassName"
          @row-click="handleRowClick"
          :max-height="tableMaxHeight"
          empty-text="暂无数据"
          v-loading="loading"
        >
          <el-table-column label="序号" min-width="60">
            <template #default="{ $index }">
              {{ (currentPage - 1) * pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="schemeName" label="方案名称" min-width="150" />
          <el-table-column label="附件" min-width="200">
            <template #default="{ row }">
              <div class="file-links" v-if="row.fileList && row.fileList.length > 0">
                <el-link
                  v-for="(file, index) in row.fileList.slice(0, 2)"
                  :key="index"
                  type="primary"
                  @click="handleFileDownload(file)"
                  class="file-link"
                >
                  {{ file.name }}
                </el-link>
                <span v-if="row.fileList.length > 2" class="more-files">
                  等{{ row.fileList.length }}个文件
                </span>
              </div>
              <span v-else class="no-file">无附件</span>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" min-width="150" />
          <el-table-column prop="remark" label="备注" min-width="150" />
          <el-table-column label="操作" fixed="right" min-width="250">
            <template #default="{ row }">
              <div class="operation-btns">
                <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
                <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
                <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
                <el-button type="primary" link @click.stop="handleDownload(row)">下载</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 对话框区域 -->
    <PointSchemeDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      :selected-bridge-id="selectedBridgeId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElInput, ElLink, ElIcon } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import {
  getPointSchemePage,
  deletePointScheme,
  getPointSchemeDetail,
  getBridgeBasicInfoList
} from '@/api/bridge'
import moment from 'moment'
import PointSchemeDialog from './components/PointSchemeDialog.vue'

// 桥梁相关
const bridgeList = ref([])
const selectedBridgeId = ref('')
const selectedBridgeName = ref('')
const bridgeSearchText = ref('')

// 搜索表单
const searchForm = ref({
  schemeName: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 计算属性：过滤后的桥梁列表
const filteredBridgeList = computed(() => {
  if (!bridgeSearchText.value) {
    return bridgeList.value
  }
  return bridgeList.value.filter(bridge =>
    bridge.bridgeName.toLowerCase().includes(bridgeSearchText.value.toLowerCase())
  )
})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList({})
    if (res && res.data) {
      bridgeList.value = res.data
      // 默认选中第一个桥梁
      if (bridgeList.value.length > 0) {
        const firstBridge = bridgeList.value[0]
        selectedBridgeId.value = firstBridge.id
        selectedBridgeName.value = firstBridge.bridgeName
        // 获取对应的方案列表
        fetchSchemeData()
      }
    }
  } catch (error) {
    console.error('获取桥梁列表失败:', error)
    ElMessage.error('获取桥梁列表失败')
  }
}

// 获取方案数据
const fetchSchemeData = async () => {
  if (!selectedBridgeId.value) {
    tableData.value = []
    total.value = 0
    return
  }

  try {
    loading.value = true
    const params = {
      bridgeId: selectedBridgeId.value,
      schemeName: searchForm.value.schemeName
    }

    const res = await getPointSchemePage(currentPage.value, pageSize.value, params)

    if (res && res.code === 200) {
      const records = res.data.records || []
      // 处理文件URL，解析为文件列表
      tableData.value = records.map(item => {
        let fileList = []
        if (item.fileUrl) {
          try {
            fileList = JSON.parse(item.fileUrl)
          } catch (error) {
            // 如果解析失败，可能是单个文件URL
            if (item.fileUrl.trim()) {
              fileList = [{
                name: '方案文件',
                url: item.fileUrl
              }]
            }
          }
        }
        return {
          ...item,
          fileList,
          updateTime: item.updateTime ? moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss') : ''
        }
      })
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取方案数据失败:', error)
    ElMessage.error('获取方案数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理桥梁选择
const handleBridgeSelect = (bridge) => {
  if (selectedBridgeId.value !== bridge.id) {
    selectedBridgeId.value = bridge.id
    selectedBridgeName.value = bridge.bridgeName
    currentPage.value = 1
    fetchSchemeData()
  }
}

// 监听选中桥梁变化
watch(() => selectedBridgeId.value, () => {
  if (selectedBridgeId.value) {
    currentPage.value = 1
    fetchSchemeData()
  }
})

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchSchemeData()
}

// 处理重置
const handleReset = () => {
  searchForm.value = {
    schemeName: ''
  }
  currentPage.value = 1
  fetchSchemeData()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchSchemeData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchSchemeData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  if (!selectedBridgeId.value) {
    ElMessage.warning('请先选择桥梁')
    return
  }
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getPointSchemeDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取方案详情失败')
    }
  } catch (error) {
    console.error('获取方案详情失败:', error)
    ElMessage.error('获取方案详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getPointSchemeDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取方案详情失败')
    }
  } catch (error) {
    console.error('获取方案详情失败:', error)
    ElMessage.error('获取方案详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该方案吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deletePointScheme(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchSchemeData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除方案失败:', error)
      ElMessage.error('删除方案失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理下载（下载所有文件）
const handleDownload = (row) => {
  if (!row.fileList || row.fileList.length === 0) {
    ElMessage.warning('该方案没有可下载的文件')
    return
  }

  // 如果只有一个文件，直接下载
  if (row.fileList.length === 1) {
    handleFileDownload(row.fileList[0])
    return
  }

  // 多个文件，批量下载
  row.fileList.forEach((file, index) => {
    setTimeout(() => {
      handleFileDownload(file)
    }, index * 200) // 每200ms下载一个文件，避免浏览器阻止
  })
}

// 处理单个文件下载
const handleFileDownload = (file) => {
  if (!file.url) {
    ElMessage.error('文件地址不存在')
    return
  }

  // 创建隐藏的下载链接
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.name || '下载文件'
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchSchemeData()
}

const tableMaxHeight = ref(500)

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight
    const container = document.querySelector('.right-panel')
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100)
      return
    }
    const containerRect = container.getBoundingClientRect()
    const containerTop = containerRect.top
    const searchSection = container.querySelector('.search-section')
    const searchHeight = searchSection ? searchSection.offsetHeight : 60
    const headerSection = container.querySelector('.table-header')
    const headerHeight = headerSection ? headerSection.offsetHeight : 60
    const paginationReservedHeight = 80
    const bottomReserved = 30
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved
    const minHeight = 300
    const absoluteMaxHeight = 600
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight)
    tableMaxHeight.value = maxHeight
  })
}

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer)
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight()
  }, 100)
}

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchBridgeList()
    setTimeout(() => {
      calculateTableMaxHeight()
    }, 100)
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (handleResize.timer) {
    clearTimeout(handleResize.timer)
  }
})
</script>

<style scoped>
.bridge-point-container {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  height: 100%;
  min-height: 0;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  border-right: 1px solid #E4E7ED;
  display: flex;
  flex-direction: column;
  background-color: #FAFAFA;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #E4E7ED;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 12px 0;
}

.search-input {
  width: 100%;
}

.bridge-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.bridge-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #F0F0F0;
  color: #333;
  font-size: 14px;
  transition: all 0.2s;
}

.bridge-item:hover {
  background-color: #E6F7FF;
  color: #0277FD;
}

.bridge-item.active {
  background-color: #0277FD;
  color: white;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-width: 0;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 文件相关样式 */
.file-links {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-link {
  font-size: 12px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-files {
  font-size: 12px;
  color: #999;
}

.no-file {
  color: #999;
  font-size: 12px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .left-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }
  
  .left-panel {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #E4E7ED;
  }
  
  .bridge-list {
    flex-direction: row;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .bridge-item {
    display: inline-block;
    min-width: 120px;
    border-right: 1px solid #F0F0F0;
    border-bottom: none;
  }
}
</style> 