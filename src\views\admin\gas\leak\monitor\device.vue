<template>
  <div class="gas-device-container">
    <!-- 顶部统计区域 -->
    <div class="stats-section">
      <div class="info-cards">
        <div class="cards-row">
          <div 
            class="info-card" 
            :style="{ background: infoCards[0].bgColor }"
          >
            <div class="card-icon">
              <i :class="infoCards[0].icon"></i>
            </div>
            <div class="card-content">
              <div class="card-title">{{ infoCards[0].title }}</div>
              <div class="card-value">{{ infoCards[0].value }}</div>
            </div>
          </div>
          <div 
            class="info-card" 
            :style="{ background: infoCards[1].bgColor }"
          >
            <div class="card-icon">
              <i :class="infoCards[1].icon"></i>
            </div>
            <div class="card-content">
              <div class="card-title">{{ infoCards[1].title }}</div>
              <div class="card-value">{{ infoCards[1].value }}</div>
            </div>
          </div>
        </div>
        <div class="cards-row">
          <div 
            class="info-card" 
            :style="{ background: infoCards[2].bgColor }"
          >
            <div class="card-icon">
              <i :class="infoCards[2].icon"></i>
            </div>
            <div class="card-content">
              <div class="card-title">{{ infoCards[2].title }}</div>
              <div class="card-value">{{ infoCards[2].value }}</div>
            </div>
          </div>
          <div 
            class="info-card" 
            :style="{ background: infoCards[3].bgColor }"
          >
            <div class="card-icon">
              <i :class="infoCards[3].icon"></i>
            </div>
            <div class="card-content">
              <div class="card-title">{{ infoCards[3].title }}</div>
              <div class="card-value">{{ infoCards[3].value }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-container">
        <GasDeviceChart :chartData="chartData" />
      </div>
    </div>

    <!-- 中间搜索区域 -->
    <div class="search-section">
      <GasDeviceSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :max-height="tableMaxHeight"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :scrollbar-always-on="true"
      :fit="true"
      v-loading="loading"
      empty-text="暂无数据"
    >
      <el-table-column type="index" label="序号" min-width="60" />
      <el-table-column prop="indexCode" label="设备编码" min-width="250" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.indexCode" class="text-ellipsis">
            {{ scope.row.indexCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="设备名称" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.deviceName" class="text-ellipsis">
            {{ scope.row.deviceName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="设备类型" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <span :title="DEVICE_TYPE_MAP[scope.row.deviceType] || scope.row.deviceTypeName" class="text-ellipsis">
            {{ DEVICE_TYPE_MAP[scope.row.deviceType] || scope.row.deviceTypeName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="安装位置" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.address" class="text-ellipsis">
            {{ scope.row.address }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="monitorTime" label="安装时间" min-width="170" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.monitorTime" class="text-ellipsis">
            {{ scope.row.monitorTime }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="ownershipUnitName" label="权属单位" min-width="120" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.ownershipUnitName" class="text-ellipsis">
            {{ scope.row.ownershipUnitName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="状态" min-width="100">
        <template #default="scope">
          <el-tag :type="scope.row.onlineStatus === 1 ? 'success' : 'danger'" size="small">
            {{ DEVICE_ONLINE_STATUS_MAP[scope.row.onlineStatus] || scope.row.onlineStatusDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="230" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row, 'curve')">监测曲线</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row, 'record')">运行记录</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
    
    <!-- 监测曲线弹窗 -->
    <MonitorCurveDialog
      v-model:visible="curveDialogVisible"
      :device-data="selectedDevice"
    />
    
    <!-- 运行记录弹窗 -->
    <OperationRecordDialog
      v-model:visible="recordDialogVisible"
      :device-data="selectedDevice"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onUnmounted } from 'vue';
import GasDeviceSearch from './components/GasDeviceSearch.vue';
import GasDeviceChart from './components/GasDeviceChart.vue';
import MonitorCurveDialog from './components/MonitorCurveDialog.vue';
import OperationRecordDialog from './components/OperationRecordDialog.vue';
import { ElMessage } from 'element-plus';
import { 
  getDeviceStatistics, 
  getDeviceStatisticsByType, 
  getDeviceRealTimeMonitoring,
} from '@/api/gas';
import { DEVICE_TYPE_MAP, DEVICE_ONLINE_STATUS_MAP } from '@/constants/gas';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 加载状态
const loading = ref(false);

// 信息卡数据
const infoCards = ref([
  {
    title: '全部设备',
    value: '0',
    bgColor: '#4699FF',
    icon: 'el-icon-document'
  },
  {
    title: '正常',
    value: '0',
    bgColor: '#44D7B6',
    icon: 'el-icon-check'
  },
  {
    title: '离线',
    value: '0',
    bgColor: '#9D9D9D',
    icon: 'el-icon-remove'
  },
  {
    title: '报警',
    value: '0',
    bgColor: '#FF6B6B',
    icon: 'el-icon-warning'
  }
]);

// 图表数据
const chartData = ref({
  categories: [],
  series: [
    {
      name: '全部',
      data: [],
      color: '#4278FF'
    },
    {
      name: '在线',
      data: [],
      color: '#73D938'
    },
    {
      name: '离线',
      data: [],
      color: '#F5C236'
    },
    {
      name: '报警',
      data: [],
      color: '#F53636'
    }
  ]
});

// 获取设备统计数据
const fetchDeviceStatistics = async () => {
  try {
    const res = await getDeviceStatistics();
    if (res && res.code === 200) {
      const data = res.data;
      // 更新信息卡数据
      infoCards.value[0].value = data.totalDeviceCount.toString();
      infoCards.value[1].value = data.normalDeviceCount.toString();
      infoCards.value[2].value = data.offlineDeviceCount.toString();
      infoCards.value[3].value = data.alarmDeviceCount.toString();
    }
  } catch (error) {
    console.error('获取设备统计数据失败', error);
  }
};

// 获取设备类型统计数据
const fetchDeviceStatisticsByType = async () => {
  try {
    const res = await getDeviceStatisticsByType();
    if (res && res.code === 200) {
      const data = res.data;
      
      // 过滤不需要的数据
      const filteredData = data.filter(item => item.type && item.typeName);
      
      // 更新图表数据
      chartData.value.categories = filteredData.map(item => item.typeName);
      chartData.value.series[0].data = filteredData.map(item => item.totalDeviceCount);
      chartData.value.series[1].data = filteredData.map(item => item.normalDeviceCount);
      chartData.value.series[2].data = filteredData.map(item => item.offlineDeviceCount);
      chartData.value.series[3].data = filteredData.map(item => item.alarmDeviceCount);
    }
  } catch (error) {
    console.error('获取设备类型统计数据失败', error);
  }
};

// 表格数据
const tableData = ref([]);

// 更新时间
const updateTime = ref(new Date());

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 查询参数
const queryParams = ref({
  deviceType: '',
  keyWord: '',
  managementUnit: '',
  managementUnitName: '',
  onlineStatus: '',
  town: ''
});

// 表格最大高度相关
const tableMaxHeight = ref(500);

// 获取设备列表数据
const fetchDeviceList = async () => {
  loading.value = true;
  try {
    const params = { ...queryParams.value };
    const res = await getDeviceRealTimeMonitoring(pagination.currentPage, pagination.pageSize, params);
    if (res && res.code === 200) {
      tableData.value = res.data.records;
      pagination.total = res.data.total;
      updateTime.value = new Date();
      nextTick(() => {
        calculateTableMaxHeight();
      });
    }
  } catch (error) {
    console.error('获取设备列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 日期格式化
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string') return date;
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 处理查询
const handleSearch = (formData) => {
  queryParams.value = { ...formData };
  pagination.currentPage = 1;
  fetchDeviceList();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {
    deviceType: '',
    keyWord: '',
    managementUnit: '',
    managementUnitName: '',
    onlineStatus: '',
    town: ''
  };
  pagination.currentPage = 1;
  fetchDeviceList();
};

// 弹窗控制
const curveDialogVisible = ref(false);
const recordDialogVisible = ref(false);
const selectedDevice = ref(null);

// 处理监测曲线
const handleCurve = (row) => {
  selectedDevice.value = row;
  curveDialogVisible.value = true;
};

// 处理运行记录
const handleRecord = (row) => {
  selectedDevice.value = row;
  recordDialogVisible.value = true;
};

// 处理详情 - 修改为根据点击类型打开不同弹窗
const handleDetail = (row, type) => {
  if (type === 'curve') {
    handleCurve(row);
  } else if (type === 'record') {
    handleRecord(row);
  }
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理页码变化
const handleCurrentChange = (val) => {
  pagination.currentPage = val;
  fetchDeviceList();
};

// 处理每页条数变化
const handleSizeChange = (val) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  fetchDeviceList();
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.gas-device-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const statsSection = container.querySelector('.stats-section');
    const statsHeight = statsSection ? statsSection.offsetHeight : 0;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - statsHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 生命周期钩子
onMounted(() => {
  fetchDeviceStatistics();
  fetchDeviceStatisticsByType();
  fetchDeviceList();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.gas-device-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #FFFFFF;
}

/* 顶部统计区域样式 */
.stats-section {
  width: 100%;
  display: flex;
  gap: 16px;
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  margin-bottom: 8px;
}

.info-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 45%;
}

.cards-row {
  display: flex;
  gap: 12px;
  width: 100%;
  height: 82px;
}

.info-card {
  flex: 1;
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
}

.card-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon i {
  font-size: 24px;
  color: white;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: white;
  margin-bottom: 4px;
}

.card-value {
  font-family: DINAlternate-Bold, DINAlternate;
  font-weight: bold;
  font-size: 24px;
  color: white;
  line-height: 1.2;
}

.chart-container {
  width: 55%;
  height: 176px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
}

/* 中间搜索区域样式 */
.search-section {
  width: 100%;
  background: white;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 8px;
}

/* 表格样式 - 使用最大高度限制 */

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>