<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="static-monitor-dialog"
  >
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 监测曲线 -->
      <el-tab-pane label="监测曲线" name="curve">
        <div class="curve-container">
          <!-- 时间范围选择 -->
          <div class="time-range-selector">
            <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
              <el-radio-button label="24h">最近24小时</el-radio-button>
              <el-radio-button label="7d">最近7天</el-radio-button>
              <el-radio-button label="30d">最近30天</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 监测指标选择 -->
          <div class="indicator-selector" v-if="monitorIndicators.length > 0">
            <span class="label">监测指标:</span>
            <el-select v-model="selectedIndicator" @change="handleIndicatorChange" placeholder="请选择监测指标">
              <el-option
                v-for="item in monitorIndicators"
                :key="item.monitorField"
                :label="`${item.monitorIndexName}(${item.measureUnit})`"
                :value="item.monitorField"
              />
            </el-select>
          </div>

          <!-- 图表区域 -->
          <div class="chart-container">
            <div ref="chartRef" class="chart" style="width: 100%; height: 400px;"></div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 运行记录 -->
      <el-tab-pane label="运行记录" name="records">
        <div class="records-container">
          <!-- 时间筛选 -->
          <div class="time-filter">
            <el-date-picker
              v-model="recordTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleRecordTimeChange"
            />
            <el-button type="primary" @click="fetchRecords">查询</el-button>
          </div>

          <!-- 记录类型选择 -->
          <el-tabs v-model="recordType" @tab-change="handleRecordTypeChange">
            <!-- 历史数据 -->
            <el-tab-pane label="历史数据" name="online">
              <el-table :data="onlineRecords" style="width: 100%" :max-height="tableMaxHeight" empty-text="暂无数据" v-loading="tableLoading">
                <el-table-column label="序号" width="60">
                  <template #default="{ $index }">
                    {{ (onlineCurrentPage - 1) * onlinePageSize + $index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column prop="monitorTime" label="监测时间" width="180" />
                <el-table-column label="监测指标" min-width="200">
                  <template #default="{ row }">
                    {{ formatMonitorIndicators(row) }}
                  </template>
                </el-table-column>
                <el-table-column label="监测值" min-width="200">
                  <template #default="{ row }">
                    {{ formatMonitorValues(row) }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="onlineCurrentPage"
                  v-model:page-size="onlinePageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="onlineTotal"
                  @size-change="handleOnlineSizeChange"
                  @current-change="handleOnlineCurrentChange"
                />
              </div>
            </el-tab-pane>

            <!-- 离线记录 -->
            <el-tab-pane label="离线记录" name="offline">
              <el-table :data="offlineRecords" style="width: 100%" :max-height="tableMaxHeight" empty-text="暂无数据" v-loading="tableLoading">
                <el-table-column label="序号" width="60">
                  <template #default="{ $index }">
                    {{ (offlineCurrentPage - 1) * offlinePageSize + $index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column prop="deviceName" label="设备名称" min-width="120" />
                <el-table-column label="离线时间" min-width="150">
                  <template #default="{ row }">
                    {{ row.offlineTime }}
                  </template>
                </el-table-column>
                <el-table-column label="恢复时间" min-width="150">
                  <template #default="{ row }">
                    {{ row.recoveryTime }}
                  </template>
                </el-table-column>
                <el-table-column prop="offlineDuration" label="离线时长(分钟)" min-width="120" />
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="offlineCurrentPage"
                  v-model:page-size="offlinePageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="offlineTotal"
                  @size-change="handleOfflineSizeChange"
                  @current-change="handleOfflineCurrentChange"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import moment from 'moment';
import {
  getEnvironmentMonitorIndicators,
  getEnvironmentMonitorCurve,
  getEnvironmentMonitorRecordPage,
  getEnvironmentOfflineRecords
} from '@/api/bridge';
import { STATIC_MONITOR_FIELD_MAP, STATIC_MONITOR_UNIT_MAP } from '@/constants/bridge';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return `${props.deviceInfo.deviceName || '设备'} - 监测详情`;
});

// 活动标签页
const activeTab = ref('curve');
const recordType = ref('online');

// 监测指标数据
const monitorIndicators = ref([]);
const selectedIndicator = ref('');
const currentIndicatorInfo = ref(null);

// 时间范围
const timeRange = ref('24h');
const recordTimeRange = ref([]);

// 图表相关
const chartRef = ref(null);
const chartLoading = ref(false);
let chartInstance = null;

// 在线记录数据
const onlineRecords = ref([]);
const onlineCurrentPage = ref(1);
const onlinePageSize = ref(10);
const onlineTotal = ref(0);

// 离线记录数据
const offlineRecords = ref([]);
const offlineCurrentPage = ref(1);
const offlinePageSize = ref(10);
const offlineTotal = ref(0);

const tableLoading = ref(false);
const tableMaxHeight = ref(300);

// 获取监测指标
const fetchMonitorIndicators = async () => {
  if (!props.deviceInfo.id) return;
  
  try {
    const res = await getEnvironmentMonitorIndicators(props.deviceInfo.id, 2); // type=2 代表静力响应监测
    if (res && res.code === 200 && res.data && Array.isArray(res.data)) {
      monitorIndicators.value = res.data;
      if (res.data.length > 0) {
        selectedIndicator.value = res.data[0].monitorField;
        currentIndicatorInfo.value = res.data[0];
        fetchCurveData();
      }
    }
  } catch (error) {
    console.error('获取监测指标失败:', error);
    ElMessage.error('获取监测指标失败');
  }
};

// 格式化日期为 YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
  const pad = (num) => String(num).padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};

// 获取时间范围
const getTimeRange = (type) => {
  const endTime = new Date();
  let startTime = new Date();

  switch (type || timeRange.value) {
    case '7d':
      startTime.setDate(endTime.getDate() - 7);
      break;
    case '30d':
      startTime.setDate(endTime.getDate() - 30);
      break;
    default: // 24h
      startTime.setDate(endTime.getDate() - 1);
      break;
  }

  return {
    startTime: formatDate(startTime),
    endTime: formatDate(endTime)
  };
};

// 生成默认时间数据
const generateDefaultTimeData = (timeRangeData) => {
  const startTime = new Date(timeRangeData.startTime);
  const endTime = new Date(timeRangeData.endTime);
  const timeData = [];

  // 根据时间范围确定间隔
  let interval = 60 * 60 * 1000; // 默认1小时间隔
  if (timeRange.value === '24h') {
    interval = 60 * 60 * 1000; // 1小时
  } else if (timeRange.value === '7d') {
    interval = 6 * 60 * 60 * 1000; // 6小时
  } else if (timeRange.value === '30d') {
    interval = 24 * 60 * 60 * 1000; // 1天
  }

  let currentTime = new Date(startTime);
  while (currentTime <= endTime) {
    timeData.push(formatDate(currentTime));
    currentTime = new Date(currentTime.getTime() + interval);
  }

  return timeData;
};

// 获取曲线数据
const fetchCurveData = async (timeRangeParam) => {
  if (!props.deviceInfo.id || !selectedIndicator.value) return;
  
  try {
    chartLoading.value = true;
    const timeRangeData = timeRangeParam || getTimeRange();
    const params = {
      deviceId: props.deviceInfo.id,
      startTime: timeRangeData.startTime,
      endTime: timeRangeData.endTime,
      type: 2 // type=2 代表静力响应监测
    };
    
    const res = await getEnvironmentMonitorCurve(params);
    if (res && res.code === 200) {
      renderChart(res.data || []);
    }
  } catch (error) {
    console.error('获取监测曲线数据失败:', error);
    ElMessage.error('获取监测曲线数据失败');
  } finally {
    chartLoading.value = false;
  }
};

// 渲染图表
const renderChart = (data) => {
  if (!chartRef.value || !Array.isArray(data) || !currentIndicatorInfo.value) return;

  // 根据选中的指标字段过滤数据
  const fieldName = selectedIndicator.value;
  let validData = data.filter(item =>
    item &&
    item.monitorTime &&
    item[fieldName] !== undefined &&
    item[fieldName] !== null &&
    item[fieldName] !== -999
  );

  // 如果没有有效数据，生成默认的零值数据
  if (validData.length === 0) {
    console.warn('没有有效的监测数据，生成默认数据');
    const timeRangeData = getTimeRange(timeRange.value);
    const defaultTimeData = generateDefaultTimeData(timeRangeData);

    validData = defaultTimeData.map(time => ({
      monitorTime: time,
      [fieldName]: 0
    }));
  }

  // 使用nextTick确保DOM已经更新
  nextTick(() => {
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value);
    }

    // 处理数据
    const xData = validData.map(item => item.monitorTime);
    const indicatorType = currentIndicatorInfo.value.type;
    const unit = currentIndicatorInfo.value.measureUnit || '';
    const indicatorName = currentIndicatorInfo.value.monitorIndexName;
    const minRange = parseFloat(currentIndicatorInfo.value.measureRangeLow);
    const maxRange = parseFloat(currentIndicatorInfo.value.measureRangeUp);

    let option = {};

    if (indicatorType === 1) {
      // 数值型指标
      const yData = validData.map(item => {
        const value = parseFloat(item[fieldName]);
        return isNaN(value) ? null : value;
      });

      const series = [{
        name: '监测值',
        data: yData,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#40C3FA'
        },
        lineStyle: {
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(64,195,250,0.2)'
            }, {
              offset: 1,
              color: 'rgba(64,195,250,0)'
            }]
          }
        }
      }];

      // 添加上限线和下限线
      if (!isNaN(maxRange)) {
        series.push({
          name: '上限值',
          data: new Array(xData.length).fill(maxRange),
          type: 'line',
          lineStyle: {
            color: '#FF0000',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          silent: true
        });
      }

      if (!isNaN(minRange)) {
        series.push({
          name: '下限值',
          data: new Array(xData.length).fill(minRange),
          type: 'line',
          lineStyle: {
            color: '#FF0000',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          silent: true
        });
      }

      option = {
        title: {
          // text: `${indicatorName}监测曲线`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = `${params[0].name}<br/>`;
            params.forEach(item => {
              if (item.seriesName === '监测值') {
                result += `${item.marker}${item.seriesName}：${item.value}${unit}<br/>`;
              } else {
                result += `${item.marker}${item.seriesName}：${item.value}${unit}<br/>`;
              }
            });
            return result;
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        grid: {
          top: 50,
          left: '5%',
          right: '8%',
          bottom: 90,
          containLabel: true
        },
        dataZoom: [
          {
            type: 'slider',
            start: 20,
            end: 80,
            height: 30,
            bottom: 10,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            dataBackground: {
              areaStyle: {
                color: 'rgba(64,195,250,0.3)'
              },
              lineStyle: {
                opacity: 0.8,
                color: 'rgb(64,195,250)'
              }
            },
            selectedDataBackground: {
              areaStyle: {
                color: 'rgba(64,195,250,0.8)'
              },
              lineStyle: {
                color: 'rgb(64,195,250)'
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 12
            },
            borderColor: '#ddd',
            fillerColor: 'rgba(64,195,250,0.2)',
            labelFormatter: (value, valueStr) => {
              if (xData && xData[value]) {
                const timeStr = xData[value];
                if (timeRange.value === '24h') {
                  return timeStr.split(' ')[1].substring(0, 5); // HH:mm
                } else {
                  const date = new Date(timeStr);
                  const month = String(date.getMonth() + 1).padStart(2, '0');
                  const day = String(date.getDate()).padStart(2, '0');
                  const hour = String(date.getHours()).padStart(2, '0');
                  const minute = String(date.getMinutes()).padStart(2, '0');
                  return `${month}/${day} ${hour}:${minute}`;
                }
              }
              return valueStr;
            }
          },
          {
            type: 'inside',
            start: 20,
            end: 80
          }
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: 30,
            formatter: (value) => {
              if (timeRange.value === '24h') {
                return value.split(' ')[1]; // 只显示时间部分
              } else {
                // 7天和30天显示月/日 时:分格式
                const date = new Date(value);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                return `${month}/${day} ${hour}:${minute}`;
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: `监测值(${unit})`,
          nameTextStyle: {
            padding: [0, 30, 0, 0]
          },
          min: (() => {
            if (!isNaN(minRange)) {
              const validYData = yData.filter(v => v !== null && v !== undefined && !isNaN(v));
              if (validYData.length > 0) {
                const dataMin = Math.min(...validYData);
                const rangeBuffer = !isNaN(maxRange) ? (maxRange - minRange) * 0.1 : 5;
                return Math.min(minRange - rangeBuffer, dataMin - 5);
              } else {
                return minRange - 5;
              }
            }
            return 'dataMin';
          })(),
          max: (() => {
            if (!isNaN(maxRange)) {
              const validYData = yData.filter(v => v !== null && v !== undefined && !isNaN(v));
              if (validYData.length > 0) {
                const dataMax = Math.max(...validYData);
                const rangeBuffer = !isNaN(minRange) ? (maxRange - minRange) * 0.1 : 5;
                return Math.max(maxRange + rangeBuffer, dataMax + 5);
              } else {
                return maxRange + 5;
              }
            }
            return 'dataMax';
          })(),
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series
      };
    } else {
      // 状态型指标 (type === 0)
      const statusData = validData.map(item => {
        const value = parseInt(item[fieldName]);
        return isNaN(value) ? null : value;
      });

      // 分别处理正常和异常状态的数据
      const normalData = statusData.map(value => value === 1 ? 1 : null);
      const abnormalData = statusData.map(value => value === 0 ? 1 : null);

      // 特殊处理：当只有一条数据且状态值为0（异常）时，显示红色点
      let abnormalSeries = {
        name: '异常',
        data: abnormalData,
        type: 'line',
        step: 'end',
        lineStyle: {
          color: '#FF0000',
          width: 4
        },
        symbol: 'none'
      };

      // 如果只有一条数据且为异常状态，显示为点
      if (validData.length === 1 && statusData[0] === 0) {
        abnormalSeries = {
          name: '异常',
          data: abnormalData,
          type: 'scatter',
          symbolSize: 8,
          itemStyle: {
            color: '#FF0000'
          }
        };
      }

      option = {
        title: {
          text: `${indicatorName}监测曲线`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const dataIndex = params[0].dataIndex;
            const status = statusData[dataIndex];
            const statusText = status === 0 ? '正常' : status === 1 ? '异常' : '未知';
            return `${params[0].name}<br/>状态：${statusText}`;
          }
        },
        legend: {
          data: ['正常', '异常']
        },
        grid: {
          top: 50,
          left: '5%',
          right: '8%',
          bottom: 90,
          containLabel: true
        },
        dataZoom: [
          {
            type: 'slider',
            start: 20,
            end: 80,
            height: 30,
            bottom: 10
          },
          {
            type: 'inside',
            start: 20,
            end: 80
          }
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: 30,
            formatter: (value) => {
              if (timeRange.value === '24h') {
                return value.split(' ')[1];
              } else {
                const date = new Date(value);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                return `${month}/${day} ${hour}:${minute}`;
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '状态',
          min: 0,
          max: 1,
          interval: 1,
          axisLabel: {
            formatter: (value) => {
              return value === 0 ? '正常' : '异常';
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '正常',
            data: normalData,
            type: 'line',
            step: 'end',
            lineStyle: {
              color: '#00FF00',
              width: 4
            },
            symbol: 'none'
          },
          abnormalSeries
        ]
      };
    }

    chartInstance.setOption(option);
  });
};

// 获取记录数据
const fetchRecords = () => {
  if (recordType.value === 'online') {
    fetchOnlineRecords();
  } else {
    fetchOfflineRecords();
  }
};

// 获取在线记录
const fetchOnlineRecords = async () => {
  if (!props.deviceInfo.id) return;
  
  try {
    tableLoading.value = true;
    const params = {
      deviceId: props.deviceInfo.id,
      type: 2
    };
    
    if (recordTimeRange.value && recordTimeRange.value.length === 2) {
      params.startTime = recordTimeRange.value[0];
      params.endTime = recordTimeRange.value[1];
    }
    
    const res = await getEnvironmentMonitorRecordPage(onlineCurrentPage.value, onlinePageSize.value, params);
    if (res && res.code === 200) {
      onlineRecords.value = res.data.records || [];
      onlineTotal.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    ElMessage.error('获取历史记录失败');
  } finally {
    tableLoading.value = false;
  }
};

// 获取离线记录
const fetchOfflineRecords = async () => {
  if (!props.deviceInfo.id) return;
  
  try {
    tableLoading.value = true;
    const params = {
      deviceId: props.deviceInfo.id,
      type: 2
    };
    
    if (recordTimeRange.value && recordTimeRange.value.length === 2) {
      params.startTime = recordTimeRange.value[0];
      params.endTime = recordTimeRange.value[1];
    }
    
    const res = await getEnvironmentOfflineRecords(offlineCurrentPage.value, offlinePageSize.value, params);
    if (res && res.code === 200) {
      offlineRecords.value = res.data.records || [];
      offlineTotal.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取离线记录失败:', error);
    ElMessage.error('获取离线记录失败');
  } finally {
    tableLoading.value = false;
  }
};

// 格式化监测指标名称
const formatMonitorIndicators = (row) => {
  const indicators = [];
  Object.keys(STATIC_MONITOR_FIELD_MAP).forEach(field => {
    if (row[field] !== null && row[field] !== undefined) {
      indicators.push(STATIC_MONITOR_FIELD_MAP[field]);
    }
  });
  return indicators.join('，');
};

// 格式化监测值
const formatMonitorValues = (row) => {
  const values = [];
  Object.keys(STATIC_MONITOR_FIELD_MAP).forEach(field => {
    if (row[field] !== null && row[field] !== undefined) {
      const unit = STATIC_MONITOR_UNIT_MAP[field] || '';
      values.push(`${row[field]}${unit}`);
    }
  });
  return values.join('，');
};

// 格式化时间
const formatTime = (timeObj) => {
  if (!timeObj || !timeObj.time) return '-';
  return moment(timeObj.time).format('YYYY-MM-DD HH:mm:ss');
};

// 事件处理
const handleTabChange = (tab) => {
  if (tab === 'curve' && monitorIndicators.value.length > 0) {
    nextTick(() => {
      if (chartInstance) {
        chartInstance.resize();
      }
    });
  } else if (tab === 'records') {
    fetchRecords();
  }
};

const handleTimeRangeChange = () => {
  fetchCurveData();
};

const handleIndicatorChange = () => {
  // 更新当前指标信息
  const indicator = monitorIndicators.value.find(item => item.monitorField === selectedIndicator.value);
  if (indicator) {
    currentIndicatorInfo.value = indicator;
  }
  fetchCurveData();
};


const handleRecordTimeChange = () => {
  fetchRecords();
};

const handleRecordTypeChange = (type) => {
  if (type === 'online') {
    onlineCurrentPage.value = 1;
    fetchOnlineRecords();
  } else {
    offlineCurrentPage.value = 1;
    fetchOfflineRecords();
  }
};

// 分页事件
const handleOnlineSizeChange = (size) => {
  onlinePageSize.value = size;
  onlineCurrentPage.value = 1;
  fetchOnlineRecords();
};

const handleOnlineCurrentChange = (page) => {
  onlineCurrentPage.value = page;
  fetchOnlineRecords();
};

const handleOfflineSizeChange = (size) => {
  offlinePageSize.value = size;
  offlineCurrentPage.value = 1;
  fetchOfflineRecords();
};

const handleOfflineCurrentChange = (page) => {
  offlineCurrentPage.value = page;
  fetchOfflineRecords();
};

const handleClose = () => {
  dialogVisible.value = false;
  
  // 清理图表
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  
  // 重置数据
  activeTab.value = 'curve';
  recordType.value = 'online';
  selectedIndicator.value = '';
  timeRange.value = '24h';
  recordTimeRange.value = [];
  onlineRecords.value = [];
  offlineRecords.value = [];
  onlineCurrentPage.value = 1;
  offlineCurrentPage.value = 1;
};

// 监听设备变化
watch(() => props.deviceInfo, (newVal) => {
  if (newVal && newVal.id && props.visible) {
    fetchMonitorIndicators();
  }
}, { deep: true });

// 监听对话框显示
watch(() => props.visible, (newVal) => {
  if (newVal && props.deviceInfo.id) {
    fetchMonitorIndicators();
    calculateTableMaxHeight();
  }
});

// 处理图表大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
  calculateTableMaxHeight();
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const dialog = document.querySelector('.static-monitor-dialog .el-dialog__body');
    if (!dialog) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    
    const timeFilter = dialog.querySelector('.time-filter');
    const tabsHeader = dialog.querySelector('.records-container .el-tabs__header');
    const paginationContainer = dialog.querySelector('.pagination-container');

    const timeFilterHeight = timeFilter ? timeFilter.offsetHeight : 0;
    const tabsHeaderHeight = tabsHeader ? tabsHeader.offsetHeight : 0;
    const paginationHeight = paginationContainer ? paginationContainer.offsetHeight : 0;
    
    const dialogPadding = 20 * 2; // top and bottom padding
    const margins = 16 + 16 + 16; // margins between sections

    const otherElementsHeight = timeFilterHeight + tabsHeaderHeight + paginationHeight + dialogPadding + margins;
    
    tableMaxHeight.value = dialog.clientHeight - otherElementsHeight;
  });
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize);
  calculateTableMaxHeight();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 监听时间范围变化
watch(timeRange, () => {
  fetchCurveData();
});

// 监听选中指标变化
watch(selectedIndicator, () => {
  handleIndicatorChange();
});
</script>

<style scoped>
.static-monitor-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  height: 600px;
  overflow: hidden;
}

.curve-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.time-range-selector {
  margin-bottom: 16px;
  text-align: center;
}

.indicator-selector {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.indicator-selector .label {
  margin-right: 8px;
  font-weight: 500;
  min-width: 60px;
}

.chart-container {
  flex: 1;
  overflow: hidden;
}

.records-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.time-filter {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

:deep(.el-radio-button__inner) {
  border-radius: 2px;
}

:deep(.el-tabs__header) {
  margin-bottom: 16px;
}

:deep(.el-tabs__content) {
  height: calc(100% - 56px);
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-table) {
  flex: 1;
  min-height: 0;
}
</style>
