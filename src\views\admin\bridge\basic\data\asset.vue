<template>
  <div class="asset-container">
    <!-- 查询条件 -->
    <div class="asset-search">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="结构类型">
          <el-select v-model="searchForm.structureType" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in structureTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="技术状况">
          <el-select v-model="searchForm.techStatus" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in techStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="养护类型">
          <el-select v-model="searchForm.maintainType" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in maintainTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="养护单位">
          <el-select v-model="searchForm.manageUnitCode" placeholder="全部" clearable style="width: 150px">
            <el-option
              v-for="item in maintenanceUnits"
              :key="item.id"
              :label="item.enterpriseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入桥梁名称或编码"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      ref="dataTable"
      v-loading="loading"
      :data="tableData"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      style="width: 100%"
      @row-click="handleRowClick"
      :max-height="tableMaxHeight"
      empty-text="暂无数据"
    >
      <el-table-column label="序号" width="60" align="center">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      
      <el-table-column prop="bridgeCode" label="桥梁编码" width="140" align="center" />
      
      <el-table-column prop="bridgeName" label="桥梁名称" width="150" align="center" show-overflow-tooltip />
      
      <el-table-column prop="structureTypeName" label="结构类型" width="120" align="center" />
      
      <el-table-column prop="techStatusName" label="技术状况" width="120" align="center">
        <template #default="{ row }">
          <div :class="getTechStatusClass(row.techStatus)">
            {{ row.techStatusName }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="maintainTypeName" label="养护类型" width="120" align="center" />
      
      <el-table-column prop="manageUnitName" label="养护单位" min-width="150" align="center" show-overflow-tooltip />
      
      <el-table-column prop="endDate" label="竣工日期" width="120" align="center" />
      
      <el-table-column prop="industryTypeName" label="行业属性" width="120" align="center" />
      
      <el-table-column label="位置" min-width="200" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ `${row.startLocation || ''} - ${row.endLocation || ''}` }}
        </template>
      </el-table-column>
      
      <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatDateTime(row.updateTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleView(row)">详情</el-button>
            <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹窗组件 -->
    <BridgeFormDialog
      v-model="dialogVisible"
      :mode="dialogMode"
      :bridge-id="currentBridgeId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElTable, ElTableColumn, ElButton, ElPagination, ElForm, ElFormItem, ElSelect, ElOption, ElInput } from 'element-plus'
import BridgeFormDialog from '@/components/bridge/BridgeFormDialog.vue'
import {
  getBridgeBasicInfoPage,
  deleteBridgeBasicInfo,
  getMaintenanceEnterpriseList
} from '@/api/bridge'
import {
  STRUCTURE_TYPE_OPTIONS,
  TECHNICAL_CONDITION_TYPE_OPTIONS,
  MAINTENANCE_CATEGORY_TYPE_OPTIONS,
  STRUCTURE_TYPE_MAP,
  TECHNICAL_CONDITION_TYPE_MAP,
  MAINTENANCE_CATEGORY_TYPE_MAP,
  INDUSTRY_ATTRIBUTE_TYPE_MAP
} from '@/constants/bridge'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const dialogMode = ref('add')
const currentBridgeId = ref('')

// 搜索表单
const searchForm = reactive({
  structureType: '',
  techStatus: '',
  maintainType: '',
  manageUnitCode: '',
  keyword: ''
})

// 分页信息
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 下拉选项数据
const structureTypeOptions = ref(STRUCTURE_TYPE_OPTIONS)
const techStatusOptions = ref(TECHNICAL_CONDITION_TYPE_OPTIONS)
const maintainTypeOptions = ref(MAINTENANCE_CATEGORY_TYPE_OPTIONS)
const maintenanceUnits = ref([])

// 获取技术状况样式类
const getTechStatusClass = (techStatus) => {
  const map = {
    '4002101': 'tech-status-good',    // 完好
    '4002102': 'tech-status-good',    // 良好
    '4002103': 'tech-status-normal',  // 合格
    '4002104': 'tech-status-bad',     // 不合格
    '4002105': 'tech-status-danger'   // 危险
  }
  return ['tech-status-tag', map[techStatus] || 'tech-status-normal']
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  // 如果是时间戳对象格式
  if (typeof dateTime === 'object' && dateTime.time) {
    const date = new Date(dateTime.time)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 如果是标准日期字符串
  if (typeof dateTime === 'string') {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  return ''
}

// 表头样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row)
}

// 加载表格数据
const loadTableData = async () => {
  try {
    loading.value = true
    
    const params = {
      ...searchForm,
      bridgeName: searchForm.keyword,
      bridgeCode: searchForm.keyword
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getBridgeBasicInfoPage(currentPage.value, pageSize.value, params)
    
    if (response.code === 200) {
      const data = response.data
      tableData.value = data.records || []
      total.value = data.total || 0
      
      // 处理数据，添加名称映射
      tableData.value.forEach(item => {
        item.structureTypeName = item.structureTypeName || STRUCTURE_TYPE_MAP[item.structureType] || ''
        item.techStatusName = item.techStatusName || TECHNICAL_CONDITION_TYPE_MAP[item.techStatus] || ''
        item.maintainTypeName = item.maintainTypeName || MAINTENANCE_CATEGORY_TYPE_MAP[item.maintainType] || ''
        item.industryTypeName = item.industryTypeName || INDUSTRY_ATTRIBUTE_TYPE_MAP[item.industryType] || ''
      })
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载养护单位列表
const loadMaintenanceUnits = async () => {
  try {
    const response = await getMaintenanceEnterpriseList({ 'enterpriseType': '5001001' })
    if (response.code === 200) {
      maintenanceUnits.value = response.data || []
    }
  } catch (error) {
    console.error('获取养护单位列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    structureType: '',
    techStatus: '',
    maintainType: '',
    manageUnitCode: '',
    keyword: ''
  })
  currentPage.value = 1
  loadTableData()
}

// 新增
const handleAdd = () => {
  dialogMode.value = 'add'
  currentBridgeId.value = ''
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  dialogMode.value = 'view'
  currentBridgeId.value = row.id
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogMode.value = 'edit'
  currentBridgeId.value = row.id
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除桥梁"${row.bridgeName}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deleteBridgeBasicInfo(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadTableData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 定位
const handleLocation = (row) => {
  if (row.geomText) {
    // TODO: 实现地图定位功能
    ElMessage.info('定位功能开发中...')
  } else {
    ElMessage.warning('该桥梁暂无位置信息')
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadTableData()
}

// 当前页改变
const handleCurrentChange = (current) => {
  currentPage.value = current
  loadTableData()
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  loadTableData()
}

const tableMaxHeight = ref(500)

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight
    const container = document.querySelector('.asset-container')
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100)
      return
    }
    const containerRect = container.getBoundingClientRect()
    const containerTop = containerRect.top
    const searchSection = container.querySelector('.asset-search')
    const searchHeight = searchSection ? searchSection.offsetHeight : 60
    const headerSection = container.querySelector('.table-header')
    const headerHeight = headerSection ? headerSection.offsetHeight : 60
    const paginationReservedHeight = 80
    const bottomReserved = 30
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved
    const minHeight = 300
    const absoluteMaxHeight = 600
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight)
    tableMaxHeight.value = maxHeight
  })
}

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer)
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight()
  }, 100)
}

// 组件挂载时执行
onMounted(() => {
  loadMaintenanceUnits()
  loadTableData()
  setTimeout(() => {
    calculateTableMaxHeight()
  }, 100)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (handleResize.timer) {
    clearTimeout(handleResize.timer)
  }
})
</script>

<style scoped>
.asset-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.asset-search {
  width: 100%;
  margin-bottom: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 技术状况标签样式 */
.tech-status-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 0 auto;
}

.tech-status-good {
  background: rgba(0, 200, 83, 0.1);
  border: 1px solid #00C853;
  color: #00C853;
}

.tech-status-normal {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #FFC107;
  color: #FFC107;
}

.tech-status-bad {
  background: rgba(255, 87, 34, 0.1);
  border: 1px solid #FF5722;
  color: #FF5722;
}

.tech-status-danger {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #F44336;
  color: #F44336;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 