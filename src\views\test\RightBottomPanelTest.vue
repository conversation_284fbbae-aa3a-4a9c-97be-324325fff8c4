<template>
  <div class="test-container">
    <h2>RightBottomPanel 对数刻度柱状图测试</h2>
    
    <!-- 原始数据展示 -->
    <div class="data-section">
      <h3>测试数据</h3>
      <div class="data-grid">
        <div class="data-row">
          <span class="category">燃气:</span>
          <span>未处置: {{ testData.gas.unhandled }}</span>
          <span>处置中: {{ testData.gas.handling }}</span>
          <span>已处置: {{ testData.gas.handled }}</span>
        </div>
        <div class="data-row">
          <span class="category">排水:</span>
          <span>未处置: {{ testData.drainage.unhandled }}</span>
          <span>处置中: {{ testData.drainage.handling }}</span>
          <span>已处置: {{ testData.drainage.handled }}</span>
        </div>
        <div class="data-row">
          <span class="category">供热:</span>
          <span>未处置: {{ testData.heating.unhandled }}</span>
          <span>处置中: {{ testData.heating.handling }}</span>
          <span>已处置: {{ testData.heating.handled }}</span>
        </div>
        <div class="data-row">
          <span class="category">桥梁:</span>
          <span>未处置: {{ testData.bridge.unhandled }}</span>
          <span>处置中: {{ testData.bridge.handling }}</span>
          <span>已处置: {{ testData.bridge.handled }}</span>
        </div>
      </div>
    </div>

    <!-- 测试不同数据场景 -->
    <div class="test-buttons">
      <button @click="setScenario1">场景1: 正常数据</button>
      <button @click="setScenario2">场景2: 极端差距 (1929 vs 1 vs 35)</button>
      <button @click="setScenario3">场景3: 包含0值</button>
      <button @click="setScenario4">场景4: 均匀分布</button>
    </div>

    <!-- 图表展示区域 -->
    <div class="chart-demo">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-color" style="background: #1B50FF;"></span>
          <span class="legend-text">未处置</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: #FFD512;"></span>
          <span class="legend-text">处置中</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: #41C4A3;"></span>
          <span class="legend-text">已处置</span>
        </div>
      </div>
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 测试数据
const testData = reactive({
  gas: { unhandled: 1929, handling: 1, handled: 35 },
  drainage: { unhandled: 500, handling: 50, handled: 200 },
  heating: { unhandled: 300, handling: 30, handled: 150 },
  bridge: { unhandled: 100, handling: 10, handled: 50 }
})

// 当前数据
const currentData = ref({
  categories: ['燃气', '排水', '供热', '桥梁'],
  series: [
    { name: '未处置', data: [0, 0, 0, 0] },
    { name: '处置中', data: [0, 0, 0, 0] },
    { name: '已处置', data: [0, 0, 0, 0] }
  ]
})

// 存储原始数据用于tooltip显示
const originalData = ref({
  categories: ['燃气', '排水', '供热', '桥梁'],
  series: [
    { name: '未处置', data: [0, 0, 0, 0] },
    { name: '处置中', data: [0, 0, 0, 0] },
    { name: '已处置', data: [0, 0, 0, 0] }
  ]
})

// 获取真实数值的辅助函数
const getRealValue = (seriesName, categoryIndex) => {
  const seriesIndex = originalData.value.series.findIndex(s => s.name === seriesName)
  if (seriesIndex !== -1 && categoryIndex !== -1) {
    return originalData.value.series[seriesIndex].data[categoryIndex]
  }
  return 0
}

// 处理图表数据
const processChartData = () => {
  const categories = ['燃气', '排水', '供热', '桥梁']
  const unhandledData = [testData.gas.unhandled, testData.drainage.unhandled, testData.heating.unhandled, testData.bridge.unhandled]
  const handlingData = [testData.gas.handling, testData.drainage.handling, testData.heating.handling, testData.bridge.handling]
  const handledData = [testData.gas.handled, testData.drainage.handled, testData.heating.handled, testData.bridge.handled]

  // 保存原始数据
  originalData.value = {
    categories,
    series: [
      { name: '未处置', data: [...unhandledData] },
      { name: '处置中', data: [...handlingData] },
      { name: '已处置', data: [...handledData] }
    ]
  }

  // 处理显示数据（0值不显示，使用null）
  currentData.value = {
    categories,
    series: [
      { name: '未处置', data: unhandledData.map(val => val === 0 ? null : val) },
      { name: '处置中', data: handlingData.map(val => val === 0 ? null : val) },
      { name: '已处置', data: handledData.map(val => val === 0 ? null : val) }
    ]
  }

  updateChartData()
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  if (chartInstance) {
    chartInstance.dispose()
  }

  setTimeout(() => {
    chartInstance = echarts.init(chartRef.value)
    updateChartData()
    window.addEventListener('resize', resizeChart)
  }, 100)
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance) return

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        return [point[0] + 10, point[1] - 50]
      },
      formatter: function (params) {
        const category = params[0].name
        let html = `<div style="color: #fff;">测试数据 - ${category}</div>`

        params.forEach((item, index) => {
          const color = {
            '未处置': '#1B50FF',
            '处置中': '#FFD512',
            '已处置': '#41C4A3'
          }[item.seriesName]

          const categoryIndex = currentData.value.categories.indexOf(category)
          const realValue = getRealValue(item.seriesName, categoryIndex)

          html += `<div style="display: flex; align-items: center; margin-top: 5px;">
                    <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; margin-right: 5px;"></span>
                    <span>${item.seriesName}: ${realValue}个</span>
                  </div>`
        })

        return html
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentData.value.categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      }
    },
    yAxis: {
      type: 'log',
      logBase: 10,
      name: '单位（个）',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400,
        padding: [0, 0, 0, 0]
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400,
        formatter: function(value) {
          // 对数刻度下的标签格式化，只显示整数
          return Math.round(value)
        }
      },
      min: function(value) {
        // 动态设置最小值，从1开始
        return Math.max(1, value.min * 0.8)
      },
      max: function(value) {
        return value.max * 1.2
      }
    },
    series: [
      {
        name: '未处置',
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(27, 80, 255, 0.9)' },
            { offset: 1, color: 'rgba(27, 80, 255, 0.1)' }
          ]),
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.value.series[0].data
      },
      {
        name: '处置中',
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 213, 18, 0.9)' },
            { offset: 1, color: 'rgba(255, 213, 18, 0.1)' }
          ]),
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.value.series[1].data
      },
      {
        name: '已处置',
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(65, 196, 163, 0.9)' },
            { offset: 1, color: 'rgba(65, 196, 163, 0.1)' }
          ]),
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.value.series[2].data
      }
    ]
  }

  chartInstance.setOption(option)
}

// 响应窗口大小变化
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 测试场景
const setScenario1 = async () => {
  testData.gas = { unhandled: 270, handling: 90, handled: 180 }
  testData.drainage = { unhandled: 150, handling: 50, handled: 120 }
  testData.heating = { unhandled: 80, handling: 30, handled: 90 }
  testData.bridge = { unhandled: 40, handling: 15, handled: 35 }
  await nextTick()
  processChartData()
}

const setScenario2 = async () => {
  testData.gas = { unhandled: 1929, handling: 1, handled: 35 }
  testData.drainage = { unhandled: 800, handling: 2, handled: 50 }
  testData.heating = { unhandled: 500, handling: 1, handled: 25 }
  testData.bridge = { unhandled: 200, handling: 3, handled: 15 }
  await nextTick()
  processChartData()
}

const setScenario3 = async () => {
  testData.gas = { unhandled: 100, handling: 0, handled: 50 }
  testData.drainage = { unhandled: 0, handling: 20, handled: 80 }
  testData.heating = { unhandled: 30, handling: 0, handled: 0 }
  testData.bridge = { unhandled: 0, handling: 0, handled: 100 }
  await nextTick()
  processChartData()
}

const setScenario4 = async () => {
  testData.gas = { unhandled: 100, handling: 100, handled: 100 }
  testData.drainage = { unhandled: 100, handling: 100, handled: 100 }
  testData.heating = { unhandled: 100, handling: 100, handled: 100 }
  testData.bridge = { unhandled: 100, handling: 100, handled: 100 }
  await nextTick()
  processChartData()
}

onMounted(() => {
  setTimeout(() => {
    initChart()
    processChartData()
  }, 300)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.test-container {
  padding: 20px;
  background: #1a1a1a;
  color: #fff;
  min-height: 100vh;
}

.data-section {
  margin-bottom: 20px;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.data-row {
  display: flex;
  gap: 20px;
  align-items: center;
}

.category {
  font-weight: bold;
  min-width: 50px;
}

.test-buttons {
  margin-bottom: 30px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-buttons button {
  padding: 8px 16px;
  background: #3AA1FF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-buttons button:hover {
  background: #66B8FF;
}

.chart-demo {
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-radius: 8px;
}

.chart-legend {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-container {
  width: 100%;
  height: 400px;
}
</style>
