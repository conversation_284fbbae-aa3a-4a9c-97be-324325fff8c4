<template>
  <div class="bridge-home">
    <!-- 顶部卡片统计 -->
    <div class="top-section">
      <!-- 左侧管网信息 -->
      <div class="network-cards">
        <!-- 桥梁总数 -->
        <div class="network-card" style="background: #F1F8FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/zongshu.png" alt="桥梁总数">
          </div>
          <div class="content">
            <div class="title">桥梁总数</div>
            <div class="data">
              <span class="value">{{ bridgeOverview.bridgeCount || 0 }}</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/rani.png" alt="桥梁总数图表">
          </div>
        </div>

        <!-- 特大桥 -->
        <div class="network-card" style="background: #FFF3F1;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/teda.png" alt="特大桥">
          </div>
          <div class="content">
            <div class="title">总里程</div>
            <div class="data">
              <span class="value">{{ bridgeOverview.totalLength || 0 }}</span>
              <span class="unit">km</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/gaoya.png" alt="总里程图表">
          </div>
        </div>

        <!-- 大桥 -->
        <div class="network-card" style="background: #FFF8F0;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/daqiao.png" alt="大桥">
          </div>
          <div class="content">
            <div class="title">总面积</div>
            <div class="data">
              <span class="value">{{ bridgeOverview.totalArea || 0 }}</span>
              <span class="unit">㎡</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/zhongya.png" alt="总面积图表">
          </div>
        </div>

        <!-- 中小桥 -->
        <div class="network-card" style="background: #F1F5FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/bridge/zhongxiao.png" alt="中小桥">
          </div>
          <div class="content">
            <div class="title">监测总数</div>
            <div class="data">
              <span class="value">{{ bridgeOverview.monitorTotalCount || 0 }}</span>
              <span class="unit">个</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/diya.png" alt="监测总数图表">
          </div>
        </div>
      </div>

      <!-- 右侧报警信息 -->
      <div class="alarm-info">
        <div class="alarm-row">
          <div class="alarm-title">今日报警</div>
          <div class="alarm-value alarm-today">{{ alarmCount.todayCount || 0 }}</div>
        </div>
        <div class="alarm-row">
          <div class="alarm-title">本月报警</div>
          <div class="alarm-value alarm-month">{{ alarmCount.monthCount || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- 第二行：待处理报警和桥梁报警排名 -->
    <div class="second-row">
      <!-- 左侧：待处理报警区域 -->
      <div class="pending-alarm-section">
        <div class="section-header">
          <div class="section-title">待处理报警</div>
        </div>

        <!-- 报警分级统计 -->
        <div class="alarm-levels">
          <div class="level-card level-one">
            <div class="level-name">一级报警</div>
            <div class="level-value">{{ unhandleAlarmStats.level1count || 0 }}</div>
          </div>
          <div class="level-card level-two">
            <div class="level-name">二级报警</div>
            <div class="level-value">{{ unhandleAlarmStats.level2count || 0 }}</div>
          </div>
          <div class="level-card level-three">
            <div class="level-name">三级报警</div>
            <div class="level-value">{{ unhandleAlarmStats.level3count || 0 }}</div>
          </div>
        </div>

        <!-- 报警列表 -->
        <div class="alarm-list">
          <div class="alarm-item" v-for="(item, index) in alarmListData" :key="item.alarmId || index">
            <div class="alarm-info-detail">
              <div class="alarm-title">{{ item.deviceName || '设备报警信息' }}</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>{{ item.address || '位置信息' }}</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>{{ formatTime(item.alarmTime) }}</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag" :class="getAlarmLevelClass(item.alarmLevelName)">
              {{ item.alarmLevelName || '未知' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：近30日桥梁报警排名 -->
      <div class="bridge-alarm-ranking-section">
        <div class="section-header">
          <div class="section-title">近30日桥梁报警排名</div>
        </div>

        <div class="bridge-alarm-table-container">
          <el-table :data="bridgeAlarmRankingData" style="width: 100%"
            :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
            :row-class-name="tableRowClassName" highlight-current-row height="300px">
            <el-table-column prop="index" label="排名" width="70" align="center">
              <template #default="scope">
                <div class="rank-tag" :class="'rank-' + scope.row.index">
                  {{ scope.row.index }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="bridgeName" label="桥梁名称" min-width="180"></el-table-column>
            <el-table-column prop="structureTypeName" label="结构类型" min-width="120"></el-table-column>
            <el-table-column prop="alarmCount" label="报警数" width="100" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 底部统计和安全评分 -->
    <div class="bottom-section">
      <!-- 左侧统计图表 -->
      <div class="statistics-charts">
        <div class="section-header">
          <div class="section-title">报警统计</div>
          <div class="action">
            <el-radio-group v-model="timeRange" size="small">
              <el-radio-button label="7">近7日</el-radio-button>
              <el-radio-button label="30">近30日</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="statistics-data">
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.alarmCount }}</div>
            <div class="stat-label">全部报警</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.handleCount }}</div>
            <div class="stat-label">已处理</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatistics.handleRate }}%</div>
            <div class="stat-label">处理完成率</div>
          </div>
        </div>

        <div ref="trendChartRef" class="trend-chart-container"></div>
      </div>

      <!-- 右侧桥梁安全评分 -->
      <div class="bridge-safety-section">
        <div class="section-header">
          <div class="section-title">桥梁安全评分</div>
        </div>
        <div class="bridge-safety-content">
          <div class="bridge-safety-table-container">
            <el-table :data="bridgeSafetyData" style="width: 100%"
              :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
              :row-class-name="tableRowClassName" highlight-current-row>
              <el-table-column prop="index" label="排名" width="70" align="center">
                <template #default="scope">
                  <div class="rank-tag" :class="'rank-' + scope.row.index">
                    {{ scope.row.index }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="bridgeName" label="桥梁名称" min-width="180"></el-table-column>
              <el-table-column prop="totalScore" label="安全评分" width="100" align="center">
                <template #default="scope">
                  <span :style="{ color: getSafetyScoreColor(scope.row.totalScore) }">{{ scope.row.totalScore }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="level" label="安全等级" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="getSafetyLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount, computed, watch } from 'vue'
import { Location, Clock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getBridgeHomePageOverview,
  getBridgeHomePageAlarmCount,
  getBridgeHomePageUnhandleAlarm,
  getBridgeHomePageAlarmRank,
  getBridgeHomePageAlarmStatistics,
  getBridgeHomePageScoreRank
} from '@/api/bridge'

// 窗口尺寸状态
const windowSize = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

// 根据窗口大小计算是否应该截断长文本
const shouldTruncate = computed(() => {
  return windowSize.value.width < 1440
})

// 处理窗口大小变化
const handleResize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (trendChart) {
    trendChart.resize()
  }
}

// 数据状态定义
const bridgeOverview = ref({
  bridgeCount: 0,
  totalLength: 0,
  totalArea: 0,
  monitorTotalCount: 0
})

const alarmCount = ref({
  todayCount: 0,
  monthCount: 0
})

const unhandleAlarmStats = ref({
  level1count: 0,
  level2count: 0,
  level3count: 0
})

const alarmListData = ref([])

// 桥梁报警排名数据
const bridgeAlarmRankingData = ref([])

// 桥梁安全评分数据
const bridgeSafetyData = ref([])

// 时间范围选择
const timeRange = ref('7')

// 报警统计数据
const alarmStatistics = ref({
  alarmCount: 0,
  handleCount: 0,
  handleRate: 0,
  alarmTrendStatistics: []
})

// 图表引用
const trendChartRef = ref(null)
let trendChart = null

// API 调用函数
const fetchBridgeOverview = async () => {
  try {
    const response = await getBridgeHomePageOverview()
    if (response.code === 200) {
      bridgeOverview.value = response.data
    }
  } catch (error) {
    console.error('获取桥梁信息统计失败:', error)
  }
}

const fetchAlarmCount = async () => {
  try {
    const response = await getBridgeHomePageAlarmCount()
    if (response.code === 200) {
      alarmCount.value = response.data
    }
  } catch (error) {
    console.error('获取报警数量统计失败:', error)
  }
}

const fetchUnhandleAlarmStats = async () => {
  try {
    const response = await getBridgeHomePageUnhandleAlarm({
      pageNum: 1,
      pageSize: 10
    })
    if (response.code === 200) {
      const data = response.data
      unhandleAlarmStats.value = {
        level1count: data.level1count,
        level2count: data.level2count,
        level3count: data.level3count
      }
      // 处理报警列表数据
      alarmListData.value = data.alarmInfoPage.records || []
    }
  } catch (error) {
    console.error('获取待处置报警统计失败:', error)
  }
}

const fetchBridgeAlarmRanking = async () => {
  try {
    const response = await getBridgeHomePageAlarmRank()
    if (response.code === 200) {
      bridgeAlarmRankingData.value = response.data.map((item, index) => ({
        index: index + 1,
        bridgeName: item.bridgeName,
        structureTypeName: item.structureTypeName,
        alarmCount: item.alarmCount
      }))
    }
  } catch (error) {
    console.error('获取桥梁报警排名失败:', error)
  }
}

const fetchAlarmStatistics = async (dayIndex = 7) => {
  try {
    const response = await getBridgeHomePageAlarmStatistics({ dayIndex })
    if (response.code === 200) {
      alarmStatistics.value = response.data
    }
  } catch (error) {
    console.error('获取报警统计失败:', error)
  }
}

const fetchBridgeSafety = async () => {
  try {
    const response = await getBridgeHomePageScoreRank()
    if (response.code === 200) {
      bridgeSafetyData.value = response.data.map((item, index) => ({
        index: index + 1,
        bridgeName: item.bridgeName,
        totalScore: item.totalScore,
        level: getScoreLevel(item.totalScore)
      }))
    }
  } catch (error) {
    console.error('获取桥梁安全评分失败:', error)
  }
}

// 工具函数
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${month}月${day}日 ${hours}:${minutes}`
  } catch (error) {
    return timeStr
  }
}

const getAlarmLevelClass = (levelName) => {
  if (levelName === '一级') return 'level-1-tag'
  if (levelName === '二级') return 'level-2-tag'
  if (levelName === '三级') return 'level-3-tag'
  return 'level-3-tag'
}

const getScoreLevel = (score) => {
  if (score >= 90) return 'A级'
  if (score >= 80) return 'B级'
  if (score >= 70) return 'C级'
  return 'D级'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 1 ? 'striped-row' : ''
}

// 获取安全评分颜色
const getSafetyScoreColor = (score) => {
  if (score >= 90) return '#67C23A' // 绿色
  if (score >= 80) return '#E6A23C' // 黄色
  if (score >= 70) return '#F56C6C' // 红色
  return '#F56C6C' // 默认红色
}

// 获取安全等级类型
const getSafetyLevelType = (level) => {
  if (level === 'A级') return 'success'
  if (level === 'B级') return 'warning'
  if (level === 'C级') return 'danger'
  return 'info'
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    if (trendChart) {
      trendChart.dispose()
    }

    trendChart = echarts.init(trendChartRef.value)

    // 根据接口数据构建图表数据
    const trendData = alarmStatistics.value.alarmTrendStatistics || []
    let dates = []
    let counts = []

    if (trendData.length > 0) {
      dates = trendData.map(item => {
        const date = new Date(item.date)
        return `${date.getMonth() + 1}-${date.getDate()}`
      })
      counts = trendData.map(item => item.totalCount || 0)
    } else {
      // 如果没有数据，显示空状态
      dates = timeRange.value === '7' ?
        Array.from({ length: 7 }, (_, i) => `${new Date().getMonth() + 1}-${new Date().getDate() - 6 + i}`) :
        Array.from({ length: 30 }, (_, i) => `${Math.floor((i + 1) / 10)}-${(i + 1) % 10 || 10}`)
      counts = Array(parseInt(timeRange.value)).fill(0)
    }

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLine: {
          lineStyle: {
            color: '#E5E5E5'
          }
        },
        axisLabel: {
          color: '#666',
          interval: timeRange.value === '30' ? 4 : 0
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E5E5E5',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '报警数量',
          type: 'line',
          smooth: true,
          data: counts,
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    trendChart.setOption(option)
  }
}

// 初始化所有数据
const initAllData = async () => {
  await Promise.all([
    fetchBridgeOverview(),
    fetchAlarmCount(),
    fetchUnhandleAlarmStats(),
    fetchBridgeAlarmRanking(),
    fetchAlarmStatistics(parseInt(timeRange.value)),
    fetchBridgeSafety()
  ])

  // 数据加载完成后初始化图表
  setTimeout(() => {
    initTrendChart()
  }, 100)
}

// 监听时间范围变化
watch(timeRange, async (newVal) => {
  console.log('时间范围变化:', newVal)
  await fetchAlarmStatistics(parseInt(newVal))
  initTrendChart()
})

onMounted(() => {
  console.log('桥梁首页组件已挂载')
  window.addEventListener('resize', handleResize)

  // 初始化所有数据
  initAllData()
})

onBeforeUnmount(() => {
  // 移除监听器
  window.removeEventListener('resize', handleResize)

  // 释放图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})
</script>

<style scoped>
.bridge-home {
  padding: 1px;
  height: 99%;
  overflow: auto;
}

/* 顶部卡片统计样式 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.network-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 1556px;
  height: 140px;
  background: #FFFFFF;
  padding: 15px;
  border-radius: 4px;
}

.network-card {
  display: flex;
  width: 292px;
  height: 110px;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
}

.icon-box {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-box img {
  width: 56px;
  height: 56px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.data {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #000000;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.chart-img {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.chart-img img {
  max-width: 84px;
  max-height: 64px;
}

/* 为不同的图表设置不同的大小 */
.network-card:nth-child(1) .chart-img img {
  width: 84px;
  height: 64px;
}

.network-card:nth-child(2) .chart-img img,
.network-card:nth-child(4) .chart-img img {
  width: 78px;
  height: 51px;
}

.network-card:nth-child(3) .chart-img img {
  width: 64px;
  height: 64px;
}

/* 右侧报警信息样式 */
.alarm-info {
  width: 300px;
  height: 140px;
  background: linear-gradient(180deg, #FFE9E9 0%, #FFF7F7 100%);
  border-radius: 4px;
  border: 1px solid #EFF0F2;
  display: flex;
  gap: 78px;
  padding: 39px 47px;
}

.alarm-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.alarm-row:last-child {
  margin-bottom: 0;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 4px;
  white-space: nowrap;
}

.alarm-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 32px;
}

.alarm-today {
  color: #FF1414;
}

.alarm-month {
  color: #333333;
}

/* 第二行样式 */
.second-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.pending-alarm-section,
.bridge-alarm-ranking-section {
  background: #FFFFFF;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #EFF0F2;
  height: 378px;
}

/* 调整左右两侧宽度比例 */
.pending-alarm-section {
  flex: 1;
}

.bridge-alarm-ranking-section {
  flex: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
}

.alarm-levels {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.level-card {
  width: 278px;
  height: 75px;
  border-radius: 4px;
  border: 1px solid #F3F3F3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.level-one {
  background: linear-gradient(315deg, #FFFAFA 0%, #FF6565 100%);
}

.level-two {
  background: linear-gradient(135deg, #FFA149 0%, #FFFDFB 100%);
}

.level-three {
  background: linear-gradient(135deg, #8FBAFF 0%, #F3F8FF 100%);
}

.level-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
}

.alarm-list {
  max-height: 180px;
  overflow-y: auto;
  padding-right: 8px;
}

.alarm-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: #FFFFFF;
  margin-bottom: 12px;
  border-radius: 4px;
  border-left: 4px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.alarm-info-detail {
  display: flex;
  flex-direction: column;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.alarm-location-time {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
}

.alarm-location,
.alarm-time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  display: flex;
  align-items: center;
}

.alarm-location :deep(svg),
.alarm-time :deep(svg) {
  margin-right: 4px;
  font-size: 16px;
  color: #909399;
}

.alarm-level-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.level-1-tag {
  background-color: #FFEFEF;
  color: #FF1414;
}

.level-2-tag {
  background-color: #FFF6EC;
  color: #FF7C00;
}

.level-3-tag {
  background-color: #EDF5FF;
  color: #2D7EFF;
}

/* 排名标签样式 */
.rank-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  color: #FFFFFF;
}

.rank-1 {
  background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%);
}

.rank-2 {
  background: linear-gradient(135deg, #FF7A45 0%, #FF9C6E 100%);
}

.rank-3 {
  background: linear-gradient(135deg, #FFC53D 0%, #FFD666 100%);
}

.rank-4,
.rank-5,
.rank-6,
.rank-7,
.rank-8,
.rank-9,
.rank-10 {
  background: linear-gradient(135deg, #4096FF 0%, #69B1FF 100%);
}

/* 底部统计和安全评分样式 */
.bottom-section {
  display: flex;
  gap: 16px;
}

.statistics-charts,
.bridge-safety-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

.action {
  display: flex;
  align-items: center;
}

.statistics-data {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #0E1D33;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.trend-chart-container {
  height: 240px;
  width: 100%;
}

.bridge-safety-content {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.bridge-safety-table-container {
  width: 100%;
}

/* 表格样式 */
:deep(.el-table .striped-row) {
  background-color: #F5F7FA;
}

:deep(.el-table th) {
  background-color: #EEF5FF !important;
}

@media (min-height: 900px) and (max-height: 940px) {
  .bridge-home {
    height: 76%;
    overflow: auto;
  }
}
</style>