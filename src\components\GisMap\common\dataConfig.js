
const relativePath = './SampleData/';

const serverRootUri = "http://117.133.182.90:1151/";
const SpatialService = "http://117.133.182.90:1331/GSpatialServer/dataServer/baseMap/ctbase";

const serverMapUrl = "http://172.20.130.229:80/"

export const gisSource = {
    serverRootUri: serverRootUri,
    key: {
        IonDefaultAccessToken :"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmMmM4Yzc0YS02ZTM4LTQwODItOGI4ZC04OTI3Yzk5MjJlMGEiLCJpZCI6MjU5LCJpYXQiOjE2Njk5MDUxNDh9.tcSYhz3-NLfuOdb1w9wHOw-Ps85-AyR_mBYRHDifEi8",
        tianDiTu: '40a623cbe2c72e883a0c0066ef20e8cd', //'1b363f53f86a0e2b3dfa5f7a121d9850', //天地图 Key,需要去天地图公司注册，此key仅做示例，加载人数过多可能会被限制
        arcGis: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
        arcGisDark: 'http://cache1.arcgisonline.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer',//深色
        dark: 'http://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',//深色影像key
        gaoDeStreetImage: 'http://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',//高德街道底图key
        gaoDeImage: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',//高德影像key
        gaoDeMark: 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8',//高德标注key
        openStreet: 'https://a.tile.openstreetmap.org/',//OpenStreet Key
        superMap: 'http://www.supermapol.com/realspace/services/map-China400/rest/maps/China400',//SuperMap影像key
    },
    // 地形
    terrain: {
        dongming: serverMapUrl + 'data/dem/dmdem',
        suZhou: serverRootUri + "data/dem/suZhouTerrain3",
    },
    // 矢量拉伸体
    boxModel: {
        beiJingBuildings: serverRootUri + 'data/blocksystem/beiJingBuildingWhite/tileset.json', //北京建筑白膜
    },
    //手工模型
    maxModel: {
        dongMingBridgeModel1: serverMapUrl + 'data/model/dmql20250417/tileset.json',
        dongMingBridgeModel2: serverMapUrl + 'data/model/dmql202501/tileset.json',
        dongMingBridgeModel3: serverMapUrl + 'data/model/dmql202502/tileset.json',
        dongMingBridgeModel4: serverMapUrl + 'data/model/dmql202503/tileset.json',
        dongMingBridgeModel5: serverMapUrl + 'data/model/dmql202504/tileset.json',
        dongMingBridgeModel6: serverMapUrl + 'data/model/dmql202505/tileset.json',
        dongMingBridgeModel7: serverMapUrl + 'data/model/dmql202506/tileset.json',
        dongMingBridgeModel8: serverMapUrl + 'data/model/dmql202507/tileset.json',
        suZhouModelPartA: serverRootUri + 'data/model/suZhouPartA/tileset.json',
    },
    //倾斜摄影//无
    osgb: {
        dongMing: serverMapUrl + 'data/osgb/dm20250417/tileset.json',
        suZhou: serverRootUri + 'data/osgb/suzhou1027/tileset.json',
    },
    // BIM（建筑信息模型）
    bim: {
        suZhou: serverRootUri + 'data/bim/building8/tileset.json',
        suZhouUUID: "b6efdd69-0009-4e31-a66b-afb76d20a739",
    },
    serverApi: {
        // bimTreeIp: serverRootUri + 'search?t=4&c=bim_bimmodel',//BIM树构建URL
        commonService: serverMapUrl,//查询分析通用IP
        geoalyService: serverMapUrl + 'geoalsycesium?',//地质剖切的IP
    },
    //影像//无
    image: {
        dongMing: serverMapUrl + 'data/dom/dmdom20250525/{z}/{x}/{y}.webp',
        suZhou: serverRootUri + 'data/dom/suzhou1025/{z}/{x}/{y}.webp',
        spatialWMS:SpatialService,
        spatialWMTS:SpatialService,
    },
    //管线场景
    pipeLineScenario: {
        suZhouGovernment: serverRootUri + "data/suZhouPipe.xml", // 服务端路径,与上一条内容一致
        burtsAnalysis: serverRootUri + "data/suZhouPipeForBurts.xml",//爆管分析
    },
    // 管线
    pipeLine: { // todo 优化命名 //老地址 dmgx20250417
        dongMingWSContainer: serverMapUrl + 'data/block/dmgx20250709/ws/container/tileset.json', // 污水管道
        dongMingWSWell: serverMapUrl + 'data/block/dmgx20250709/ws/well/tileset.json', // 污水管井
        dongMingWSJoint: serverMapUrl + 'data/block/dmgx20250709/ws/joint/tileset.json', // 污水管点
        dongMingWSEquipment: serverMapUrl + 'data/block/dmgx20250709/ws/equipment/tileset.json', // 污水设备
        dongMingWSPlate: serverMapUrl + 'data/block/dmgx20250709/ws/plate/tileset.json', // 污水板材
        dongMingYSContainer: serverMapUrl + 'data/block/dmgx20250709/ys/container/tileset.json', // 雨水管道
        dongMingYSWell: serverMapUrl + 'data/block/dmgx20250709/ys/well/tileset.json', // 雨水管井
        dongMingYSJoint: serverMapUrl + 'data/block/dmgx20250709/ys/joint/tileset.json', // 雨水管点
        dongMingYSEquipment: serverMapUrl + 'data/block/dmgx20250709/ys/equipment/tileset.json', // 雨水设备
        dongMingYSPlate: serverMapUrl + 'data/block/dmgx20250709/ys/plate/tileset.json', // 雨水板材
        dongMingTRContainer: serverMapUrl + 'data/block/dmgx20250709/tr/container/tileset.json', // 燃气管道
        dongMingTRWell: serverMapUrl + 'data/block/dmgx20250709/tr/well/tileset.json', // 燃气管井
        dongMingTRJoint: serverMapUrl + 'data/block/dmgx20250709/tr/joint/tileset.json', // 燃气管点
        dongMingTREquipment: serverMapUrl + 'data/block/dmgx20250709/tr/equipment/tileset.json', // 燃气设备
        dongMingTRPlate: serverMapUrl + 'data/block/dmgx20250709/tr/plate/tileset.json', // 燃气板材
        dongMingRSContainer: serverMapUrl + 'data/block/dmgx20250709/rs/container/tileset.json', // 供热管道
        dongMingRSWell: serverMapUrl + 'data/block/dmgx20250709/rs/well/tileset.json', // 供热管井
        dongMingRSJoint: serverMapUrl + 'data/block/dmgx20250709/rs/joint/tileset.json', // 供热管点
        dongMingRSEquipment: serverMapUrl + 'data/block/dmgx20250709/rs/equipment/tileset.json', // 供热设备
        dongMingRSPlate: serverMapUrl + 'data/block/dmgx20250709/rs/plate/tileset.json', // 供热板材

        // 流向分析 这份数据专门给流向分析用的 todo 可以简化，不必这样存储
        suZhouWuShuiContainer: serverRootUri + 'data/block/suzhou/ws/container/tileset.json',//管道
        suZhouWuShuiWell: serverRootUri + 'data/block/suzhou/ws/well/tileset.json',//管井
        suZhouWuShuiJoint: serverRootUri + 'data/block/suzhou/ws/joint/tileset.json',//管点
    },
    // 点模型
    pointmodel: {
        //城市小品（路灯，产业园，树）
        xiongAnStreetLamp: serverRootUri + 'data/matchmodel/samplelight/tileset.json',
    },
    // 地质体
    geostructure: {
        hangZhou: serverRootUri + 'data/geostructure/hangzhou/',
    },
    // 标注
    mark: {
        changTing: serverRootUri + 'data/vector/annotation/ctgongjiao/layer.kml',
    },
    //矢量点
    point: {
        bigPonitData: serverRootUri + 'data/vector/poi/vctrPoint//tileset.json',//海量点数据
    },
    //矢量线
    polyline: {
        changTing: serverRootUri + 'data/vector/polyline/ctroad/layer.json',
    },
    //矢量面
    polygon: {
        bigPolygonData: serverRootUri + 'data/vector/polygon/vctrBuilding/tileset.json',//海量面数据
    },
    // 点云//无
    pointCloud: {
        buildingPnts: serverRootUri + 'data/pointcloud/dianyun/tileset.json',
    },
    // gltf模型
    gltf: {
        geniusMan: relativePath + "gltf/GeniusMan.glb",//正元人模型
        geniusBalloon: relativePath + "gltf/GeniusBalloon.glb",//正元热气球
        airPlane: relativePath + "gltf/Airplane.glb",//飞机
        fountainModel: serverRootUri + 'data/model/penquan2/0/penquan.gltf', //喷泉模型
    },
    // 查询的UUID，可在GManager中获取，或在数据查询时获取，也可根据加载XML数据时获取
    UUID: {
        osgbQuery: "2ca6b640-f24b-443b-bb78-b0ee4b5428c6",
        vectorQuery: "6405f293-d798-4d58-b115-cb4f9f5a5176",
    },
    //SHP数据
    vector: {
        shapefile: {
            polygonShpWuHan: "./SampleData/vector/shapefile/polygonhebei.shp",
            polygonDbfWuHan: "./SampleData/vector/shapefile/polygonhebei.dbf",
        },
        geojson: {
            rongjiang: "./SampleData/vector/geojson/rongjiang.json",
        }
    },
    //GPS数据
    gpx: {
        complexTrk: relativePath + "gpx/complexTrk.gpx",
        lamina: relativePath + "gpx/lamina.gpx",
        route: relativePath + "gpx/route.gpx",
        simple: relativePath + "gpx/simple.gpx",
        wpt: relativePath + "gpx/wpt.gpx",
    },
    // 漫游路线
    flyRoute: {
        sample: "./SampleData/flyRoute/sample000.json"
    },
    czml: {
        earthquake2010: relativePath + "czml/earthquake2010.czml",
    },
    skybox: {
        celestrak: {
            px: relativePath + "skybox/celestrak/00h+00.jpg",
            py: relativePath + "skybox/celestrak/06h+00.jpg",
            pz: relativePath + "skybox/celestrak/06h+90.jpg",
            nx: relativePath + "skybox/celestrak/12h+00.jpg",
            ny: relativePath + "skybox/celestrak/18h+00.jpg",
            nz: relativePath + "skybox/celestrak/06h-90.jpg",
        }
    },
    tle: {
        celestrakTLE: relativePath + "tle/celestrakTLE.txt",
        celestrakCat: relativePath + "tle/satcat.csv",
    },
    waterSurface:{
        yitongRiver:serverRootUri + 'data/waterSurface/YiTongRiver2D_500/HydrologySimulation.json',
    },
    voxel:{
        yitongRiver:"./SampleData/voxel/yitong_depth.nc"
    },
    colormap:{
        qgisColormap:"./SampleData/colormap/colormap_0.qml"
    }
}
