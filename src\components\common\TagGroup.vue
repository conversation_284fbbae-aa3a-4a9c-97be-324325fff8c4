<template>
  <div class="tag-group-container">
    <!-- 显示的标签 -->
    <div class="tag-list">
      <el-tag
        v-for="(tag, index) in displayTags"
        :key="index"
        class="tag-item"
        size="small"
        type="info"
      >
        {{ tag }}
      </el-tag>
      
      <!-- 展开/收起按钮 -->
      <el-tag
        v-if="hasMoreTags"
        class="tag-item expand-tag"
        size="small"
        type="primary"
        @click="toggleExpand"
        style="cursor: pointer;"
      >
        {{ isExpanded ? '收起' : `+${remainingCount}` }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElTag } from 'element-plus'

// 定义props
const props = defineProps({
  // 传入的数据字符串，格式如："水位:-999m、流量:0.0093m³/s、流速:-999m/s"
  data: {
    type: String,
    default: ''
  },
  // 默认显示的标签数量
  maxDisplay: {
    type: Number,
    default: 3
  },
  // 分隔符
  separator: {
    type: String,
    default: '、'
  }
})

// 响应式数据
const isExpanded = ref(false)

// 解析数据为标签数组
const tags = computed(() => {
  if (!props.data || typeof props.data !== 'string') {
    return []
  }
  return props.data.split(props.separator).filter(tag => tag.trim()).map(tag => tag.replace(/-999/g, ' - '));
})

// 是否有更多标签
const hasMoreTags = computed(() => {
  return tags.value.length > props.maxDisplay
})

// 剩余标签数量
const remainingCount = computed(() => {
  return Math.max(0, tags.value.length - props.maxDisplay)
})

// 显示的标签
const displayTags = computed(() => {
  if (isExpanded.value || !hasMoreTags.value) {
    return tags.value
  }
  return tags.value.slice(0, props.maxDisplay)
})

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 监听数据变化，重置展开状态
watch(() => props.data, () => {
  isExpanded.value = false
})
</script>

<style scoped>
.tag-group-container {
  width: 100%;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.tag-item {
  margin: 0;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
  white-space: nowrap;
}

.expand-tag {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.expand-tag:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 确保标签在一行内显示，超出时换行 */
:deep(.el-tag) {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>