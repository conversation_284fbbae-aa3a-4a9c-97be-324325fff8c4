/**
 * WebSocket连接管理器
 * 负责全局报警消息的WebSocket连接和消息处理
 */

import { useAlarmStore } from '@/stores/alarm'

class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.heartbeatInterval = 30000
    this.isManualClose = false
    this.alarmStore = null
  }

  /**
   * 初始化WebSocket连接
   * @param {string} url WebSocket服务地址
   */
  init(url = 'ws://172.20.130.240:32021/basic/websocket') {
  // init(url = 'ws://http://1.95.86.209:32021/basic/websocket') {
    try {
      console.log('初始化WebSocket连接:', url)
      this.ws = new WebSocket(url)
      this.setupEventListeners()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleReconnect()
    }
  }

  /**
   * 设置WebSocket事件监听器
   */
  setupEventListeners() {
    if (!this.ws) return

    this.ws.onopen = (event) => {
      console.log('WebSocket连接已建立')
      this.reconnectAttempts = 0
      this.startHeartbeat()
      
      // 初始化告警store
      if (!this.alarmStore) {
        this.alarmStore = useAlarmStore()
      }
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('收到WebSocket消息:', data)
        this.handleAlarmMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason)
      this.stopHeartbeat()
      
      if (!this.isManualClose) {
        this.handleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
    }
  }

  /**
   * 处理报警消息
   * @param {Object} data 接收到的消息数据
   */
  handleAlarmMessage(data) {
    // 验证消息格式
    if (!this.isValidAlarmMessage(data)) {
      console.warn('无效的报警消息格式:', data)
      return
    }

    // 只处理报警消息 (eventType = 10)
    if (data.eventType == 10) {
      const alarmMessage = {
        id: `${data.deviceId}_${data.time}_${Date.now()}`, // 生成唯一ID
        type: data.type,
        deviceType: data.deviceType,
        time: data.time,
        content: data.content,
        level: data.level,
        deviceId: data.deviceId,
        value: data.value,
        timestamp: Date.now() // 添加接收时间戳
      }

      console.log('处理报警消息:', alarmMessage)
      
      // 发送到store
      if (this.alarmStore) {
        this.alarmStore.addAlarmMessage(alarmMessage)
      }
    }
  }

  /**
   * 验证报警消息格式
   * @param {Object} data 消息数据
   * @returns {boolean} 是否为有效格式
   */
  isValidAlarmMessage(data) {
    return data &&
           typeof data.eventType !== 'undefined' &&
           data.type &&
           data.deviceType &&
           data.time &&
           data.content &&
           data.level &&
           data.deviceId
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        try {
          this.ws.send(JSON.stringify({ type: 'ping' }))
        } catch (error) {
          console.error('发送心跳失败:', error)
        }
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 处理重连逻辑
   */
  handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    this.reconnectTimer = setTimeout(() => {
      this.init()
    }, this.reconnectInterval)
  }

  /**
   * 手动关闭WebSocket连接
   */
  close() {
    console.log('手动关闭WebSocket连接')
    this.isManualClose = true
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 获取连接状态
   * @returns {string} 连接状态
   */
  getConnectionState() {
    if (!this.ws) return 'CLOSED'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING'
      case WebSocket.OPEN:
        return 'OPEN'
      case WebSocket.CLOSING:
        return 'CLOSING'
      case WebSocket.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }

  /**
   * 重新连接
   */
  reconnect() {
    this.close()
    this.isManualClose = false
    this.reconnectAttempts = 0
    setTimeout(() => {
      this.init()
    }, 1000)
  }
}

// 创建全局实例
export const websocketManager = new WebSocketManager()

export default websocketManager