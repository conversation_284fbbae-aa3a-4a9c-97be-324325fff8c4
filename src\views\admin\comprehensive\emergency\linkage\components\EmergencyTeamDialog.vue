<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="emergency-team-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="队伍编码" prop="teamCode">
            <el-input v-model="formData.teamCode" placeholder="请输入队伍编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="队伍名称" prop="teamName">
            <el-input v-model="formData.teamName" placeholder="请输入队伍名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="队伍类型" prop="teamType">
            <el-select v-model="formData.teamType" placeholder="请选择队伍类型" class="w-full" @change="handleTeamTypeChange">
              <el-option v-for="item in teamTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="级别" prop="teamLevel">
            <el-select v-model="formData.teamLevel" placeholder="请选择级别" class="w-full" @change="handleTeamLevelChange">
              <el-option v-for="item in teamLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="人数" prop="teamPeopleNum">
            <el-input-number v-model="formData.teamPeopleNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成立时间" prop="establishTime">
            <el-date-picker
              v-model="formData.establishTime"
              type="date"
              placeholder="请选择成立时间"
              class="w-full"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管理单位" prop="managementUnitName">
            <el-input v-model="formData.managementUnitName" placeholder="请输入管理单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="responsibleUser">
            <el-input v-model="formData.responsibleUser" placeholder="请输入负责人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人电话" prop="responsibleUserPhone">
            <el-input v-model="formData.responsibleUserPhone" placeholder="请输入负责人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="固定电话" prop="landline">
            <el-input v-model="formData.landline" placeholder="请输入固定电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人1" prop="contactUser1">
            <el-input v-model="formData.contactUser1" placeholder="请输入联系人1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话1" prop="contactInfo1">
            <el-input v-model="formData.contactInfo1" placeholder="请输入联系电话1" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人2" prop="contactUser2">
            <el-input v-model="formData.contactUser2" placeholder="请输入联系人2" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话2" prop="contactInfo2">
            <el-input v-model="formData.contactInfo2" placeholder="请输入联系电话2" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="社区">
            <el-input v-model="formData.communityName" placeholder="请输入社区" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveEmergencyTeam,
  updateEmergencyTeam
} from '@/api/comprehensive';
import { EMERGENCY_TEAM_LEVEL_OPTIONS, EMERGENCY_TEAM_TYPE_OPTIONS } from '@/constants/comprehensive';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const teamLevelOptions = EMERGENCY_TEAM_LEVEL_OPTIONS;
const teamTypeOptions = EMERGENCY_TEAM_TYPE_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增救援队伍',
    edit: '编辑救援队伍',
    view: '救援队伍详情'
  };
  return titles[props.mode] || '救援队伍信息';
});

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  teamCode: '',
  teamName: '',
  teamType: '',
  teamTypeName: '',
  teamLevel: '',
  teamLevelName: '',
  teamPeopleNum: 0,
  establishTime: '',
  managementUnitName: '',
  responsibleUser: '',
  responsibleUserPhone: '',
  landline: '',
  contactUser1: '',
  contactInfo1: '',
  contactUser2: '',
  contactInfo2: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  community: '',
  communityName: '',
  keyWord: ''
});

// 表单验证规则
const formRules = {
  teamCode: [{ required: true, message: '请输入队伍编码', trigger: 'blur' }],
  teamName: [{ required: true, message: '请输入队伍名称', trigger: 'blur' }],
  teamType: [{ required: true, message: '请选择队伍类型', trigger: 'change' }],
  teamLevel: [{ required: true, message: '请选择级别', trigger: 'change' }],
  teamPeopleNum: [{ required: true, message: '请输入人数', trigger: 'blur' }],
  responsibleUser: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  responsibleUserPhone: [
    { required: true, message: '请输入负责人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'teamPeopleNum') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理队伍类型变化
const handleTeamTypeChange = (value) => {
  const selected = teamTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.teamTypeName = selected.label;
  }
};

// 处理级别变化
const handleTeamLevelChange = (value) => {
  const selected = teamLevelOptions.find(item => item.value === value);
  if (selected) {
    formData.teamLevelName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveEmergencyTeam(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEmergencyTeam(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});
</script>

<style scoped>
.emergency-team-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 