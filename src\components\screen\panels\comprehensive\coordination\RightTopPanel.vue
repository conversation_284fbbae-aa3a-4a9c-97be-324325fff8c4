<template>
  <PanelBox title="预警变化趋势">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-icon total"></span>
          <span class="legend-text">燃气</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-one"></span>
          <span class="legend-text">排水</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-two"></span>
          <span class="legend-text">供热</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-three"></span>
          <span class="legend-text">桥梁</span>
        </div>
      </div>
      <div class="chart-title-container">
        <span class="unit-label">单位（个）</span>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import moment from 'moment'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getLinkageWarningTrendStatistics } from '@/api/comprehensive'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null
// 加载状态
const loading = ref(false)

/**
 * 根据时间范围类型计算开始时间和结束时间
 * @param {string} range 时间范围类型：'week'、'month'、'year'
 * @returns {object} 包含startTime和endTime的对象
 */
const getTimeRange = (range) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime

  switch (range) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }

  return { startTime, endTime }
}

// 预设颜色
const colors = {
  total: '#055ADB',
  levelOne: '#FF311D',
  levelTwo: '#FF6817',
  levelThree: '#FFD32E'
}

// 图表数据
const chartData = ref({
  xAxis: [],
  series: [
    {
      name: '燃气',
      data: [],
      color: colors.total
    },
    {
      name: '排水',
      data: [],
      color: colors.levelOne
    },
    {
      name: '供热',
      data: [],
      color: colors.levelTwo
    },
    {
      name: '桥梁',
      data: [],
      color: colors.levelThree
    }
  ]
})

/**
 * 获取联动预警趋势统计数据
 * @param {string} timeRange 时间范围
 */
const fetchTrendStatistics = async (timeRange = 'week') => {
  try {
    loading.value = true
    const { startTime, endTime } = getTimeRange(timeRange)

    const response = await getLinkageWarningTrendStatistics({
      startTime,
      endTime
    })

    if (response.code === 200 && response.data) {
      processChartData(response.data, timeRange)
    }
  } catch (error) {
    console.error('获取联动预警趋势统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理图表数据
 * @param {object} data API返回的数据
 * @param {string} timeRange 时间范围
 */
const processChartData = (data, timeRange) => {
  // 生成时间轴数据
  const xAxisData = generateTimeAxis(timeRange)

  // 初始化各业务类型数据
  const gasData = new Array(xAxisData.length).fill(0)
  const drainData = new Array(xAxisData.length).fill(0)
  const heatData = new Array(xAxisData.length).fill(0)
  const bridgeData = new Array(xAxisData.length).fill(0)

  // 处理燃气数据
  if (data.gasTrendResponse && data.gasTrendResponse.length > 0) {
    data.gasTrendResponse.forEach(item => {
      const index = findTimeIndex(item.date, xAxisData, timeRange)
      if (index !== -1) {
        gasData[index] = item.totalCount || 0
      }
    })
  }

  // 处理排水数据
  if (data.drainTrendResponse && data.drainTrendResponse.length > 0) {
    data.drainTrendResponse.forEach(item => {
      const index = findTimeIndex(item.date, xAxisData, timeRange)
      if (index !== -1) {
        drainData[index] = item.totalCount || 0
      }
    })
  }

  // 处理供热数据
  if (data.heatTrendResponse && data.heatTrendResponse.length > 0) {
    data.heatTrendResponse.forEach(item => {
      const index = findTimeIndex(item.date, xAxisData, timeRange)
      if (index !== -1) {
        heatData[index] = item.totalCount || 0
      }
    })
  }

  // 处理桥梁数据
  if (data.bridgeTrendResponse && data.bridgeTrendResponse.length > 0) {
    data.bridgeTrendResponse.forEach(item => {
      const index = findTimeIndex(item.date, xAxisData, timeRange)
      if (index !== -1) {
        bridgeData[index] = item.totalCount || 0
      }
    })
  }

  // 更新图表数据
  chartData.value.xAxis = xAxisData
  chartData.value.series[0].data = gasData
  chartData.value.series[1].data = drainData
  chartData.value.series[2].data = heatData
  chartData.value.series[3].data = bridgeData

  // 更新图表
  updateChart()
}

/**
 * 生成时间轴数据
 * @param {string} timeRange 时间范围
 * @returns {Array} 时间轴标签数组
 */
const generateTimeAxis = (timeRange) => {
  const labels = []
  let dataPoints = 7 // 默认一周

  if (timeRange === 'month') {
    dataPoints = 30
  } else if (timeRange === 'year') {
    dataPoints = 12
  }

  for (let i = dataPoints - 1; i >= 0; i--) {
    let label = ''
    if (timeRange === 'week') {
      const date = moment().subtract(i, 'days')
      const dayOfWeek = date.day()
      label = dayOfWeek === 0 ? '周日' : `周${dayOfWeek}`
    } else if (timeRange === 'month') {
      const date = moment().subtract(i, 'days')
      label = `${date.date()}日`
    } else {
      const date = moment().subtract(i, 'months')
      label = `${date.month() + 1}月`
    }
    labels.push(label)
  }

  return labels
}

/**
 * 查找时间在轴上的索引
 * @param {string} dateStr 日期字符串
 * @param {Array} xAxisData 时间轴数据
 * @param {string} timeRange 时间范围
 * @returns {number} 索引位置，-1表示未找到
 */
const findTimeIndex = (dateStr, xAxisData, timeRange) => {
  const targetDate = moment(dateStr)
  const now = moment()

  if (timeRange === 'week') {
    const daysDiff = now.diff(targetDate, 'days')
    return daysDiff >= 0 && daysDiff < 7 ? 6 - daysDiff : -1
  } else if (timeRange === 'month') {
    const daysDiff = now.diff(targetDate, 'days')
    return daysDiff >= 0 && daysDiff < 30 ? 29 - daysDiff : -1
  } else {
    const monthsDiff = now.diff(targetDate, 'months')
    return monthsDiff >= 0 && monthsDiff < 12 ? 11 - monthsDiff : -1
  }
}

// 数据刷新定时器
const refreshTimer = ref(null)

// 开始数据刷新
const startDataRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }

  refreshTimer.value = setInterval(() => {
    fetchTrendStatistics(selectedTime.value)
  }, 10 * 60 * 1000) // 每10分钟刷新一次
}

// 停止数据刷新
const stopDataRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchTrendStatistics(value)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '燃气',
        type: 'line',
        data: chartData.value.series[0].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.total
        },
        lineStyle: {
          color: colors.total,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(5, 90, 219, 0.5)' },
              { offset: 1, color: 'rgba(5, 90, 219, 0)' }
            ]
          }
        }
      },
      {
        name: '排水',
        type: 'line',
        data: chartData.value.series[1].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelOne
        },
        lineStyle: {
          color: colors.levelOne,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 49, 29, 0.5)' },
              { offset: 1, color: 'rgba(255, 49, 29, 0)' }
            ]
          }
        }
      },
      {
        name: '供热',
        type: 'line',
        data: chartData.value.series[2].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelTwo
        },
        lineStyle: {
          color: colors.levelTwo,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 104, 23, 0.5)' },
              { offset: 1, color: 'rgba(255, 104, 23, 0)' }
            ]
          }
        }
      },
      {
        name: '桥梁',
        type: 'line',
        data: chartData.value.series[3].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelThree
        },
        lineStyle: {
          color: colors.levelThree,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 219, 35, 0.5)' },
              { offset: 1, color: 'rgba(255, 196, 35, 0)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: chartData.value.xAxis
    },
    series: [
      { data: chartData.value.series[0].data },
      { data: chartData.value.series[1].data },
      { data: chartData.value.series[2].data },
      { data: chartData.value.series[3].data }
    ]
  })
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  fetchTrendStatistics()
  // 开始数据刷新
  startDataRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopDataRefresh()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.com-select {
  margin-right: 20px;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 20px;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  padding: 1px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 2px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.level-one {
  background: #FF311D;
}

.legend-icon.level-two {
  background: #FF6817;
}

.legend-icon.level-three {
  background: #FFD32E;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  min-height: 150px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }

  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .chart-wrapper {
    min-height: 170px;
  }
}
</style>