<template>
  <PanelBox title="桥梁病害">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 病害数量统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner red"></span></span>
            <span class="stat-label">病害总数</span>
            <span class="stat-value red-gradient">{{ statsData.total }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner blue"></span></span>
            <span class="stat-label">已处理</span>
            <span class="stat-value blue-value">{{ statsData.handled }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
            <span class="stat-label">处理完成率</span>
            <span class="stat-value green-gradient">{{ statsData.rate }}</span>
          </div>
        </div>
      </div>

      <!-- 病害列表区域 -->
      <div v-if="loading" class="loading-wrapper">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <span class="loading-text">加载中...</span>
        </div>
      </div>
      <div v-else-if="alarmList.length === 0" class="no-data-wrapper">
        <NoData />
      </div>
      <ScrollTable v-else :columns="tableColumns" :data="alarmList" :autoScroll="true" :scrollSpeed="2000"
        :tableHeight="tableHeight" :visibleRows="2" :hiddenHeader="true">
        <template #custom="{ row }">
          <div class="alarm-row" @click="openAlarmDetail(row)">
            <div class="alarm-main">
              <span class="alarm-type">{{ row.type }}</span>
              <div class="alarm-level">
                <span class="level-tag" :style="{ background: getLevelColor(row.level) }">{{ row.level }}</span>
              </div>
              <div class="alarm-status">
                <span class="status-text">{{ row.status }}</span>
              </div>
            </div>
            <div class="alarm-location">
              <img src="@/assets/images/screen/common/location.svg" alt="location" class="location-icon" />
              <span class="location-text">{{ row.location }}</span>
              <span class="time-text">{{ row.time }}</span>
            </div>
            <div class="alarm-divider"></div>
          </div>
        </template>
      </ScrollTable>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import NoData from '@/components/common/NoData.vue'
import { getBridgeDiseaseStatistics } from '@/api/bridge'

// 定义props
const props = defineProps({
  bridgeId: {
    type: [String, Number],
    required: true
  },
  bridgeName: {
    type: String,
    default: '未知桥梁'
  }
})

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到dayIndex
const timeRangeMap = {
  week: 7,
  month: 30,
  year: 365
}

// 统计数据
const statsData = ref({
  total: 0,
  handled: 0,
  rate: '0%'
})

// 加载状态
const loading = ref(false)

// 病害列表数据
const alarmList = ref([])

// 获取桥梁病害统计数据
const fetchDiseaseStatistics = async () => {
  if (!props.bridgeId) return

  try {
    loading.value = true
    const dayIndex = timeRangeMap[timeRange.value]
    const response = await getBridgeDiseaseStatistics({
      bridgeId: props.bridgeId,
      dayIndex: dayIndex,
      pageNum: 1,
      pageSize: 9999
    })

    if (response.code === 200 && response.data) {
      const data = response.data

      // 更新统计数据
      statsData.value = {
        total: data.totalCount || 0,
        handled: data.handledCount || 0,
        rate: data.handledRate !== null ? `${data.handledRate}%` : '0%'
      }

      // 更新病害列表数据
      const records = data.diseaseList?.records || []
      alarmList.value = records.map(item => ({
        type: item.diseaseName || '未知病害',
        level: item.diseaseLevelName || '未知等级',
        status: item.processTypeName || '未知状态',
        location: item.diseaseLocation || '未知位置',
        time: formatDiseaseTime(item.createTime),
        // 保留原始数据用于详情查看
        originalData: item
      }))
    }
  } catch (error) {
    console.error('获取桥梁病害统计数据失败:', error)
    // 保持默认值
  } finally {
    loading.value = false
  }
}

// 格式化病害时间
const formatDiseaseTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

// 表格配置
const tableColumns = [
  { title: '病害信息', dataIndex: 'custom', width: '100%' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  if (window.innerHeight === 910) {
    return '280px'
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '320px'
  } else if (window.innerWidth >= 2561) {
    return '360px'
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '300px'
  } else {
    return '260px'
  }
})

// 获取病害等级对应的颜色
const getLevelColor = (level) => {
  const colorMap = {
    '1级': '#FB3737', // 红色
    '2级': '#FF6D28', // 橙色
    '3级': '#EAA01B', // 黄色
    '4级': '#3FD87C', // 绿色
    '一级病害': '#FB3737',
    '二级病害': '#FF6D28',
    '三级病害': '#EAA01B',
    '四级病害': '#3FD87C',
    '轻微病害': '#3FD87C',
    '中等病害': '#EAA01B',
    '严重病害': '#FF6D28',
    '危险病害': '#FB3737'
  }
  return colorMap[level] || '#3FD87C'
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  timeRange.value = value
  fetchDiseaseStatistics()
}

// 打开病害详情弹窗
const openAlarmDetail = (row) => {
  console.log('打开病害详情:', row)
  // 预留病害详情弹窗功能
}

// 监听bridgeId变化
watch(() => props.bridgeId, (newId) => {
  if (newId) {
    fetchDiseaseStatistics()
  }
}, { immediate: false })

// 组件挂载时获取数据
onMounted(() => {
  fetchDiseaseStatistics()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px 0px 10px;
}

.stat-item {
  display: flex;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.red {
  background: #055ADB;
}

.stat-dot-inner.blue {
  background: #23CAFF;
}

.stat-dot-inner.green {
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.red) {
  background: rgba(5,90,219,0.4);
}

.stat-dot:has(.stat-dot-inner.blue) {
  background: rgba(35, 202, 255, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.red-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  background-clip: text;
  -webkit-background-clip: text;
}

.blue-value {
  color: #3CF3FF;
}

.green-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  background-clip: text;
  -webkit-background-clip: text;
}

/* 病害列表样式 */
.alarm-row {
  padding: 10px 5px;
  width: 100%;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.alarm-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.alarm-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.alarm-type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  width: 40%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alarm-level {
  width: 30%;
  display: flex;
  justify-content: center;
}

.level-tag {
  width: 60px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.alarm-status {
  width: 30%;
  display: flex;
  justify-content: flex-end;
}

.status-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFAA28;
}

.alarm-location {
  display: flex;
  align-items: center;
  gap: 10px;
}

.location-icon {
  width: 12px;
  height: 12px;
}

.location-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.6;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.8;
}

.alarm-divider {
  display: none;
}

/* Loading样式 */
.loading-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #3CF3FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* NoData样式 */
.no-data-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 16px;
    line-height: 18px;
  }
  .alarm-main {
    margin-bottom: 2px;
  }
  .alarm-row {
    padding: 5px 5px;
  }
}
</style>