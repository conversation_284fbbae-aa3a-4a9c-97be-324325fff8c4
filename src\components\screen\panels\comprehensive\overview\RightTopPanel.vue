<template>
  <PanelBox title="报警处置监管">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">报警总数</span>
          <span class="stat-value-blue">{{ statsData.totalAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">今日报警</span>
          <span class="stat-value-sky">{{ statsData.todayAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">本月报警</span>
          <span class="stat-value-gradient">{{ statsData.monthAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
      </div>

      <div class="alarm-stats-container">
        <div class="alarm-stats-box">
          <div class="alarm-item">
            <div class="alarm-label">一级报警</div>
            <div class="alarm-value-red">{{ alarmData.level1 }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">二级报警</div>
            <div class="alarm-value-orange">{{ alarmData.level2 }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">三级报警</div>
            <div class="alarm-value-yellow">{{ alarmData.level3 }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">已处置</div>
            <div class="alarm-value-blue">{{ alarmData.handled }}</div>
          </div>
          <div class="alarm-item">
            <div class="alarm-label">处置完成率</div>
            <div class="alarm-value-green">{{ alarmData.handleRate }}</div>
          </div>
        </div>
      </div>
      
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getAlarmStatistics } from '@/api/comprehensive'

// 类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '供热', value: 'heating' },
  { label: '桥梁', value: 'bridge' }
]

// 统计数据
const statsData = reactive({
  totalAlarms: 0,
  todayAlarms: 0,
  monthAlarms: 0
})

// 报警数据
const alarmData = reactive({
  level1: 0,
  level2: 0,
  level3: 0,
  handled: 0,
  handleRate: '0%'
})

// 图表数据
const chartData = reactive({
  xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
})

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 处理类型变化
const handleTypeChange = (value) => {
  console.log('类型变化为:', value)
  fetchData(value)
}

// 从后端获取数据
const fetchData = async (type) => {
  try {
    // 根据类型映射relatedBusiness参数
    const businessMap = {
      'gas': '7000501',
      'water': '7000502', 
      'heating': '7000503',
      'bridge': '7000504'
    }
    
    const relatedBusiness = type === 'all' ? undefined : businessMap[type]
    const response = await getAlarmStatistics(relatedBusiness)
    
    if (response.code === 200 && response.data) {
      const data = response.data
      
      // 更新统计数据
      statsData.totalAlarms = data.totalCount || 0
      statsData.todayAlarms = data.todayCount || 0
      statsData.monthAlarms = data.monthCount || 0
      
      // 更新报警数据
      alarmData.level1 = data.level1Count || 0
      alarmData.level2 = data.level2Count || 0
      alarmData.level3 = data.level3Count || 0
      alarmData.handled = data.handledCount || 0
      alarmData.handleRate = data.handledRate || '0%'
      
      // 更新图表数据
      if (data.trends && data.trends.length > 0) {
        // 按月份顺序排序
        const monthOrder = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        const sortedTrends = monthOrder.map(month => {
          const trend = data.trends.find(t => t.month === month)
          return trend ? trend.count : 0
        })
        chartData.values = sortedTrends
      } else {
        // 如果没有趋势数据，保持全0以维持图表结构
        chartData.values = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      }
      
      // 更新图表
      if (chartInstance) {
        const option = createChartOption()
        chartInstance.setOption(option)
      }
    }
  } catch (error) {
    console.error('获取报警处置数据失败:', error)
  }
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '7%',
      right: '1%',
      bottom: '40%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC'
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>${param.marker}报警数量：${param.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        interval: 0,
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 2
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12,
        padding: [0, 30, 0, 0]
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 2
      }
    },
    series: [
      {
        type: 'line',
        data: chartData.values,
        smooth: false,
        symbol: 'none',
        symbolSize: 6,
        itemStyle: {
          color: '#D9E7FF'
        },
        lineStyle: {
          color: '#D9E7FF',
          width: 2,
          shadowColor: 'rgba(0, 92, 228, 0.4)',
          shadowBlur: 6
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(217, 231, 255, 0.45)' },
              { offset: 1, color: 'rgba(217, 231, 255, 0.01)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  // 响应式调整图表大小
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 初始化
onMounted(async () => {
  await nextTick()
  initChart()
  // 获取初始数据
  fetchData(selectedType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 0px 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-sky {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
  text-align: left;
  font-style: normal;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.alarm-stats-container {
  display: flex;
  justify-content: center;
  margin: 5px 0;
}

.alarm-stats-box {
  width: 424px;
  height: 55px;
  border: 1px solid;
  border-image: radial-gradient(circle, rgba(0, 170, 255, 1), rgba(0, 141, 255, 0.5)) 1 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
}

.alarm-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.alarm-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.alarm-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FF0000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-orange {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FF7B00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-yellow {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FFC400 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #3CF3FF 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alarm-value-green {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  min-height: 200px;
}


@media (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }
  
  .stats-row {
    margin-bottom: 0;
  }
  
  .stat-item {
    gap: 2px;
  }
  
  .stat-dot {
    width: 6px;
    height: 6px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .stat-value-blue,
  .stat-value-sky,
  .stat-value-gradient {
    font-size: 16px;
    line-height: 18px;
  }
  
  .stat-unit {
    font-size: 9px;
  }
  
  .alarm-stats-box {
    height: 42px;
    padding: 0 8px;
  }
  
  .alarm-label {
    font-size: 11px;
  }
  
  .alarm-value-red,
  .alarm-value-orange,
  .alarm-value-yellow,
  .alarm-value-blue,
  .alarm-value-green {
    font-size: 14px;
    line-height: 16px;
  }
  
  .chart-wrapper {
    min-height: 220px;
  }
}
</style>