<template>
  <PanelBox title="隐患整改统计">
    <template #extra>
      <div class="extra-container">
        <div class="com-select">
          <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
        </div>
        <div class="com-select">
          <CommonSelect v-model="selectedTime" :options="timeOptions" @change="handleTimeChange" />
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 隐患数量统计 -->
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">隐患总数</span>
          <span class="stat-value-red">{{ statsData.totalIssues }} 个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已整改</span>
          <span class="stat-value-blue">{{ statsData.fixedIssues }} 个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">今日整改</span>
          <span class="stat-value-green">{{ statsData.todayFixed }} 个</span>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="chart-left" ref="barChartRef"></div>
        <div class="chart-right" ref="lineChartRef"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import moment from 'moment'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getDangerRectifyStatistics } from '@/api/comprehensive'

// 专项类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: '7000501' },
  { label: '排水', value: '7000502' },
  { label: '桥梁', value: '7000504' },
  { label: '供热', value: '7000503' }
]

// 专项类型代码映射
const typeCodeMap = {
  'all': null,
  '7000501': 7000501,
  '7000502': 7000502,
  '7000503': 7000503,
  '7000504': 7000504
}

// 时间选择
const selectedTime = ref('year')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = reactive({
  totalIssues: 0,
  fixedIssues: 0,
  todayFixed: 0
})

// 图表DOM引用
const barChartRef = ref(null)
const lineChartRef = ref(null)

// 图表实例
let barChartInstance = null
let lineChartInstance = null

// 柱状图数据
const barChartData = reactive({
  categories: [],
  series: [
    { name: '已整改', data: [], color: '#F4DD70' },
    { name: '整改中', data: [], color: '#FF954C' },
    { name: '未整改', data: [], color: '#40CDFF' }
  ]
})

// 折线图数据
const lineChartData = reactive({
  xAxis: [],
  values: []
})

// 获取时间范围
const getTimeRange = (timeType) => {
  const now = moment()
  let startTime, endTime

  switch (timeType) {
    case 'week':
      startTime = now.clone().subtract(1, 'week').format('YYYY-MM-DD HH:mm:ss')
      endTime = now.format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = now.clone().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss')
      endTime = now.format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
    default:
      startTime = now.clone().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss')
      endTime = now.format('YYYY-MM-DD HH:mm:ss')
      break
  }

  return { startTime, endTime }
}

// 获取隐患整改统计数据
const fetchRectifyData = async () => {
  try {
    const { startTime, endTime } = getTimeRange(selectedTime.value)
    const relatedBusiness = typeCodeMap[selectedType.value]

    const params = {
      startTime,
      endTime
    }

    if (relatedBusiness) {
      params.relatedBusiness = relatedBusiness
    }

    const response = await getDangerRectifyStatistics(params)

    if (response && response.data) {
      const data = response.data

      // 更新统计数据
      statsData.totalIssues = data.totalCount || 0
      statsData.fixedIssues = data.rectifiedCount || 0
      statsData.todayFixed = data.todayRectifiedCount || 0

      // 更新柱状图数据
      if (data.relatedBusinessStatistics && data.relatedBusinessStatistics.length > 0) {
        barChartData.categories = data.relatedBusinessStatistics.map(item => item.relatedBusinessName)
        barChartData.series[0].data = data.relatedBusinessStatistics.map(item => item.rectifiedCount || 0)
        barChartData.series[1].data = data.relatedBusinessStatistics.map(item => item.rectifyingCount || 0)
        barChartData.series[2].data = data.relatedBusinessStatistics.map(item => item.unRectifyCount || 0)
      } else {
        // 如果没有数据，清空图表
        barChartData.categories = []
        barChartData.series[0].data = []
        barChartData.series[1].data = []
        barChartData.series[2].data = []
      }

      // 更新折线图数据
      if (data.trends && data.trends.length > 0) {
        lineChartData.xAxis = data.trends.map(item => item.month)
        lineChartData.values = data.trends.map(item => item.rate || 0)
      } else {
        lineChartData.xAxis = []
        lineChartData.values = []
      }

      // 更新图表
      updateCharts()
    }
  } catch (error) {
    console.error('获取隐患整改统计数据失败:', error)
    // 重置数据
    statsData.totalIssues = 0
    statsData.fixedIssues = 0
    statsData.todayFixed = 0
    barChartData.categories = []
    barChartData.series[0].data = []
    barChartData.series[1].data = []
    barChartData.series[2].data = []
    lineChartData.xAxis = []
    lineChartData.values = []
    updateCharts()
  }
}

// 处理类型变化
const handleTypeChange = (value) => {
  console.log('专项类型变更为:', value)
  fetchRectifyData()
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchRectifyData()
}

// 更新图表
const updateCharts = () => {
  updateBarChart()
  updateLineChart()
}

// 创建柱状图选项
const createBarChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '25%',
      left: '12%',
      right: '5%',
      bottom: '15%'
    },
    legend: {
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      top: '5%',
      icon: 'rect'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
    },
    xAxis: {
      type: 'category',
      data: barChartData.categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: barChartData.series.map((item, index) => {
      let gradientColor

      if (item.name === '已整改') {
        gradientColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#F4DD70' },
          { offset: 1, color: 'rgba(59, 59, 2, 0.01)' }
        ])
      } else if (item.name === '整改中') {
        gradientColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#FF954C' },
          { offset: 1, color: 'rgba(89, 49, 0, 0.01)' }
        ])
      } else {
        gradientColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#40CDFF' },
          { offset: 1, color: 'rgba(0, 59, 89, 0.01)' }
        ])
      }

      return {
        name: item.name,
        type: 'bar',
        stack: 'total',
        barWidth: 15,
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: gradientColor
        },
        data: item.data
      }
    })
  }
}

// 创建折线图选项
const createLineChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '25%',
      left: '5%',
      right: '5%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
    },
    xAxis: {
      type: 'category',
      data: lineChartData.xAxis,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'line',
        data: lineChartData.values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#00C2FF',
          borderColor: 'rgba(215, 236, 255, 0.3)',
          borderWidth: 6
        },
        lineStyle: {
          color: '#246BFD',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(36, 107, 253, 0.4)' },
              { offset: 1, color: 'rgba(36, 107, 253, 0.01)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化柱状图
const initBarChart = () => {
  // 检查DOM元素是否存在
  if (!barChartRef.value) return

  // 确保DOM元素有尺寸
  if (barChartRef.value.clientWidth === 0 || barChartRef.value.clientHeight === 0) {
    setTimeout(initBarChart, 100)
    return
  }

  // 初始化柱状图
  barChartInstance = echarts.init(barChartRef.value)

  // 设置柱状图配置
  const option = createBarChartOption()
  barChartInstance.setOption(option)
}

// 初始化折线图
const initLineChart = () => {
  // 检查DOM元素是否存在
  if (!lineChartRef.value) return

  // 确保DOM元素有尺寸
  if (lineChartRef.value.clientWidth === 0 || lineChartRef.value.clientHeight === 0) {
    setTimeout(initLineChart, 100)
    return
  }

  // 初始化折线图
  lineChartInstance = echarts.init(lineChartRef.value)

  // 设置折线图配置
  const option = createLineChartOption()
  lineChartInstance.setOption(option)
}

// 更新柱状图
const updateBarChart = () => {
  if (!barChartInstance) return

  // 更新柱状图配置
  const option = createBarChartOption()
  barChartInstance.setOption(option)
}

// 更新折线图
const updateLineChart = () => {
  if (!lineChartInstance) return

  // 更新折线图配置
  const option = createLineChartOption()
  lineChartInstance.setOption(option)
}

// 窗口大小变化时重置图表大小
const handleResize = () => {
  barChartInstance && barChartInstance.resize()
  lineChartInstance && lineChartInstance.resize()
}

// 组件挂载后初始化
onMounted(() => {
  // 获取初始数据
  fetchRectifyData()

  // 使用nextTick等待DOM更新
  nextTick(() => {
    // 等待一段时间确保DOM完全渲染
    setTimeout(() => {
      initBarChart()
      initLineChart()
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)
    }, 300)
  })

  // 备份方案：如果图表仍未初始化，重试
  setTimeout(() => {
    if (!barChartInstance && barChartRef.value) {
      initBarChart()
    }
    if (!lineChartInstance && lineChartRef.value) {
      initLineChart()
    }
  }, 1000)
})

// 组件销毁前清理图表实例
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)

  if (barChartInstance) {
    barChartInstance.dispose()
    barChartInstance = null
  }

  if (lineChartInstance) {
    lineChartInstance.dispose()
    lineChartInstance = null
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.extra-container {
  display: flex;
  gap: 15px;
}

.com-select {
  margin-right: 5px;
}

/* 顶部统计信息样式 */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(215, 48, 48, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #D73030;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #DB2D05 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #E2FBFF 0%, #23CAFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-green {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 图表容器样式 */
.charts-container {
  flex: 1;
  display: flex;
  gap: 10px;
}

.chart-left,
.chart-right {
  flex: 1;
  height: 100%;
  min-height: 180px;
}

/* 响应式适配 */
@media (min-height: 910px) and (max-height: 1050px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value-red,
  .stat-value-blue,
  .stat-value-green {
    font-size: 16px;
    line-height: 18px;
  }

  .charts-container {
    gap: 8px;
  }
}

@media (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }

  .stats-row {
    margin-bottom: 5px;
  }

  .stat-dot {
    width: 8px;
    height: 8px;
  }

  .stat-dot-inner {
    width: 4px;
    height: 4px;
  }

  .stat-label {
    font-size: 11px;
  }

  .stat-value-red,
  .stat-value-blue,
  .stat-value-green {
    font-size: 14px;
    line-height: 16px;
  }

  .charts-container {
    gap: 5px;
  }

  .chart-left,
  .chart-right {
    min-height: 160px;
  }
}
</style>