<template>
  <div class="video-monitoring-container">
    <!-- 搜索区域 -->
    <GasMonitorVideoSearch @search="handleSearch" @reset="handleReset" />
    
    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :max-height="tableMaxHeight"
      v-loading="loading"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="设备名称" min-width="120" />
      <el-table-column prop="code" label="设备编号" min-width="120" />
      <el-table-column prop="target" label="监测对象" min-width="120" />
      <el-table-column prop="position" label="位置" min-width="150" />
      <el-table-column prop="status" label="设备状态" min-width="100">
        <template #default="{ row }">
          {{ getStatusText(row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="220" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleEdit(scope.row)">编辑</el-button>
            <el-button type="primary" link @click.stop="handleDelete(scope.row)">删除</el-button>
            <el-button type="primary" link @click.stop="handleLocation(scope.row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElButton } from 'element-plus';
import GasMonitorVideoSearch from './GasMonitorVideoSearch.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 模拟数据
const mockData = [
  // {
  //   name: '视频监控设备1',
  //   code: 'VID001',
  //   target: '燃气管道',
  //   position: '经度: 120.123, 纬度: 30.456',
  //   status: '001'
  // }
];

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '001': '使用中',
    '002': '未使用',
    '003': '已废弃'
  };
  return statusMap[status] || '未知';
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  fetchVideoData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchVideoData();
};

// 获取视频监控数据
const fetchVideoData = async () => {
  loading.value = true;
  try {
    // TODO: 替换为实际API调用
    tableData.value = mockData;
    total.value = mockData.length;
  } catch (error) {
    console.error('获取视频监控数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchVideoData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchVideoData();
};

// 表头样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleEdit = (row) => {
  console.log('编辑:', row);
};

const handleDelete = (row) => {
  console.log('删除:', row);
};

const handleLocation = (row) => {
  console.log('定位:', row);
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.video-monitoring-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.gas-monitor-video-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const paginationReservedHeight = 80;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchVideoData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.video-monitoring-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>