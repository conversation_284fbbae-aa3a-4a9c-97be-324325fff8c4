<template>
  <div class="drainage-pump-container">
    <!-- 搜索区域 -->
    <div class="drainage-pump-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">泵站类型:</span>
          <el-select v-model="formData.stationType" class="form-input" placeholder="请选择">
            <el-option v-for="item in PUMP_STATION_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">泵站名称:</span>
          <el-input v-model="formData.stationName" class="form-input" placeholder="请输入泵站名称" />
        </div>
        <div class="form-item">
          <span class="label">使用状态:</span>
          <el-select v-model="formData.usageStatus" class="form-input" placeholder="请选择">
            <el-option v-for="item in usageStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :max-height="tableMaxHeight"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :scrollbar-always-on="true"
      :fit="true"
      v-loading="loading"
      empty-text="暂无数据"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="stationName" label="泵站名称" min-width="120" />
      <el-table-column prop="stationCode" label="泵站编码" min-width="120" />
      <el-table-column prop="stationTypeName" label="泵站类型" min-width="120" />
      <el-table-column prop="usageStatusName" label="使用状态" min-width="120" />
      <el-table-column prop="pumpNum" label="泵数量" min-width="100" />
      <el-table-column prop="drainSewageCapacity" label="污水排水能力(m³/h)" min-width="140" />
      <el-table-column prop="drainRainCapacity" label="雨水排水能力(m³/h)" min-width="140" />
      <el-table-column prop="serverArea" label="服务面积(km²)" min-width="120" />
      <el-table-column prop="serverRange" label="服务范围" min-width="150" />
      <el-table-column prop="address" label="位置" min-width="150" />
      <el-table-column label="操作" fixed="right" min-width="220" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 泵站弹窗 -->
    <DrainagePumpStationDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, onUnmounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getPumpStationPage, getPumpStationDetail, deletePumpStation } from '@/api/drainage';
import { misPosition } from '@/hooks/gishooks';
import { PUMP_STATION_TYPE_OPTIONS, USE_TYPE } from '@/constants/drainage';
import DrainagePumpStationDialog from './components/DrainagePumpStationDialog.vue';

// 加载状态
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 表单数据
const formData = ref({
  stationType: '',
  stationName: '',
  usageStatus: ''
});

// 使用状态选项，添加"全部"选项
const usageStatusOptions = computed(() => {
  return [
    { label: '全部', value: '' },
    ...USE_TYPE
  ];
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 表格最大高度
const tableMaxHeight = ref(500);

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理查询
const handleSearch = () => {
  fetchPumpStationData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    stationType: '',
    stationName: '',
    usageStatus: ''
  };
  fetchPumpStationData();
};

// 获取泵站分页数据
const fetchPumpStationData = async () => {
  loading.value = true;
  try {
    const params = {
      stationType: formData.value.stationType,
      stationName: formData.value.stationName,
      usageStatus: formData.value.usageStatus,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };
    
    const res = await getPumpStationPage(params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;
      nextTick(() => {
        calculateTableMaxHeight();
      });
    }
  } catch (error) {
    console.error('获取泵站数据失败:', error);
    ElMessage.error('获取泵站数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchPumpStationData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchPumpStationData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    // 获取详细数据
    const res = await getPumpStationDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取泵站详情失败');
    }
  } catch (error) {
    console.error('获取泵站详情失败:', error);
    ElMessage.error('获取泵站详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    // 获取详细数据
    const res = await getPumpStationDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取泵站详情失败');
    }
  } catch (error) {
    console.error('获取泵站详情失败:', error);
    ElMessage.error('获取泵站详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该泵站吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deletePumpStation(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPumpStationData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除泵站失败:', error);
      ElMessage.error('删除泵站失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理弹窗成功
const handleDialogSuccess = () => {
  fetchPumpStationData();
};

// 处理导入
const handleImport = () => {
  console.log('导入');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.drainage-pump-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.drainage-pump-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchPumpStationData();
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.drainage-pump-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #FFFFFF;
}

/* 搜索区域样式 */
.drainage-pump-search {
  flex-shrink: 0;
  margin-bottom: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 80px;
  height: 32px;
  padding: 0;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 - 使用最大高度限制 */

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style> 