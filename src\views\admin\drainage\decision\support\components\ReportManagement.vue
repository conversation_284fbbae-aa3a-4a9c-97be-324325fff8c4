<template>
  <div class="report-management-container">
    <!-- 搜索区域 -->
    <div class="report-search">
      <div class="search-form">
        <!-- <div class="form-item">
          <span class="label">报告类型:</span>
          <el-select v-model="formData.reportType" class="form-input" placeholder="全部">
            <el-option v-for="item in reportTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报告编码:</span>
          <el-input v-model="formData.reportCode" class="form-input" placeholder="输入报告编码" />
        </div> -->
        <div class="form-item">
          <span class="label">报告名称:</span>
          <el-input v-model="formData.reportName" class="form-input" placeholder="输入报告名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <!-- <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">批量导入</el-button>
      </div>
    </div> -->
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      v-loading="loading" empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="reportCode" label="报告编码" min-width="120" />
        <el-table-column prop="reportName" label="报告名称" min-width="200" />
        <el-table-column prop="reportTypeName" label="报告类型" min-width="120" />
        <el-table-column prop="createBy" label="创建人" min-width="100" />
        <el-table-column label="创建时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="更新人" min-width="100" />
        <el-table-column label="更新时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" />
        <el-table-column label="操作" fixed="right" min-width="280">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleDownload(row)" v-if="row.fileUrls">下载</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

    <!-- 对话框区域 -->
    <ReportDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getAssessReportPage, 
  deleteAssessReport, 
  getAssessReportDetail,
  downloadAssessReport
} from '@/api/drainage';
import { REPORT_TYPE_OPTIONS, REPORT_TYPE_MAP } from '@/constants/drainage';
import ReportDialog from './ReportDialog.vue';
import moment from 'moment';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 下拉选项数据
const reportTypeOptions = REPORT_TYPE_OPTIONS;

// 表单数据
const formData = ref({
  reportType: '',
  reportCode: '',
  reportName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dateTime ? moment(dateTime).format('YYYY-MM-DD HH:mm:ss') : '-';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchReportData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    reportType: '',
    reportCode: '',
    reportName: ''
  };
  currentPage.value = 1;
  fetchReportData();
};

// 获取报告分页数据
const fetchReportData = async () => {
  try {
    loading.value = true;
    const params = {
      reportType: formData.value.reportType,
      reportCode: formData.value.reportCode,
      reportName: formData.value.reportName
    };
    
    const res = await getAssessReportPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      // 处理表格数据，添加报告类型名称
      const records = res.data.records || [];
      tableData.value = records.map(item => ({
        ...item,
        reportTypeName: REPORT_TYPE_MAP[item.reportType] || '-'
      }));
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取报告数据失败:', error);
    ElMessage.error('获取报告数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchReportData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchReportData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getAssessReportDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取报告详情失败');
    }
  } catch (error) {
    console.error('获取报告详情失败:', error);
    ElMessage.error('获取报告详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getAssessReportDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取报告详情失败');
    }
  } catch (error) {
    console.error('获取报告详情失败:', error);
    ElMessage.error('获取报告详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该报告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteAssessReport(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchReportData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除报告失败:', error);
      ElMessage.error('删除报告失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理下载
const handleDownload = async (row) => {
  if (!row.fileUrls) {
    ElMessage.warning('该报告没有附件文件');
    return;
  }
  
  try {
    const response = await downloadAssessReport(row.fileUrls);
    const blob = new Blob([response]);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${row.reportName || '报告'}.pdf`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    ElMessage.success('下载成功');
  } catch (error) {
    console.error('下载报告失败:', error);
    ElMessage.error('下载报告失败');
  }
};

// 处理导入
const handleImport = () => {
  console.log('导入');
  ElMessage.info('导入功能待实现');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchReportData();
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.report-management-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.report-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(() => {
  fetchReportData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.report-management-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.report-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 