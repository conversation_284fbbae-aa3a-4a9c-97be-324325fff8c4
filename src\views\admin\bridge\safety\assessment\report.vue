<template>
  <div class="safety-report-container">
    <!-- 搜索区域 -->
    <div class="safety-report-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">报告类型:</span>
          <el-select v-model="formData.reportType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in reportTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">上传时间:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="form-input date-range"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>
        <div class="form-item">
          <el-input v-model="formData.reportName" class="form-input" placeholder="输入报告名称/编号" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table 
      :data="tableData" 
      style="width: 100%" 
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" 
      @row-click="handleRowClick" 
      :max-height="tableMaxHeight"
      empty-text="暂无数据"
      v-loading="loading"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="reportCode" label="报告编号" min-width="120" />
      <el-table-column prop="reportName" label="报告名称" min-width="150" />
      <el-table-column prop="reportTypeName" label="报告类型" min-width="120" />
      <el-table-column prop="reportDate" label="报告日期" min-width="170" />
      <el-table-column prop="reportObjectNames" label="报告对象" min-width="120" />
      <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" />
      <el-table-column label="桥梁数量" min-width="100">
        <template #default="{ row }">
          {{ row.bridgeCount || (row.bridgeName ? row.bridgeName.split(',').length : 0) }}
        </template>
      </el-table-column>
      <el-table-column prop="reportDate" label="上传时间" min-width="170" />
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click.stop="handleView(row)">查看</el-button>
            <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            <el-button type="primary" link @click.stop="handleDownload(row)" v-if="row.fileUrl">下载文件</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <SafetyReportDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  getSafetyAssessReportPage, 
  deleteSafetyAssessReport, 
  getSafetyAssessReportDetail
} from '@/api/bridge'
import { SAFETY_REPORT_TYPE_OPTIONS } from '@/constants/bridge'
import SafetyReportDialog from './components/SafetyReportDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 下拉选项数据
const reportTypeOptions = ref(SAFETY_REPORT_TYPE_OPTIONS)

// 搜索表单数据
const formData = ref({
  reportType: '',
  reportName: ''
})

// 日期范围
const dateRange = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchReportData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    reportType: '',
    reportName: ''
  }
  dateRange.value = []
  currentPage.value = 1
  fetchReportData()
}

// 获取报告分页数据
const fetchReportData = async () => {
  loading.value = true
  try {
    const params = {
      reportType: formData.value.reportType,
      reportName: formData.value.reportName
    }
    
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }
    
    const res = await getSafetyAssessReportPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取安全评估报告数据失败:', error)
    ElMessage.error('获取安全评估报告数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchReportData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchReportData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getSafetyAssessReportDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取安全评估报告详情失败')
    }
  } catch (error) {
    console.error('获取安全评估报告详情失败:', error)
    ElMessage.error('获取安全评估报告详情失败')
  }
}

// 处理查看详情
const handleView = async (row) => {
  try {
    const res = await getSafetyAssessReportDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取安全评估报告详情失败')
    }
  } catch (error) {
    console.error('获取安全评估报告详情失败:', error)
    ElMessage.error('获取安全评估报告详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该安全评估报告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteSafetyAssessReport(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchReportData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除安全评估报告失败:', error)
      ElMessage.error('删除安全评估报告失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理文件下载
const handleDownload = (row) => {
  if (row.fileUrl) {
    // 支持多个文件，取第一个文件下载
    const urls = row.fileUrl.split(',').filter(url => url.trim())
    if (urls.length > 0) {
      const link = document.createElement('a')
      link.href = urls[0].trim()
      link.download = `${row.reportName}_${row.reportCode}`
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      ElMessage.warning('没有可下载的文件')
    }
  } else {
    ElMessage.warning('没有可下载的文件')
  }
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchReportData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const container = document.querySelector('.safety-report-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const searchSection = container.querySelector('.safety-report-search');
    const headerSection = container.querySelector('.table-header');
    const paginationContainer = container.querySelector('.pagination-container');

    const searchHeight = searchSection ? searchSection.offsetHeight : 0;
    const headerHeight = headerSection ? headerSection.offsetHeight : 0;
    const paginationHeight = paginationContainer ? paginationContainer.offsetHeight : 0;
    
    const containerPadding = 16 * 2;
    const margins = 16 + 16;

    const otherElementsHeight = searchHeight + headerHeight + paginationHeight + containerPadding + margins;
    
    const containerRect = container.getBoundingClientRect();
    
    tableMaxHeight.value = window.innerHeight - containerRect.top - otherElementsHeight;
  });
};

const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(() => {
  fetchReportData()
  calculateTableMaxHeight();
  window.addEventListener('resize', handleResize);
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.safety-report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.safety-report-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

.date-range {
  width: 260px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>