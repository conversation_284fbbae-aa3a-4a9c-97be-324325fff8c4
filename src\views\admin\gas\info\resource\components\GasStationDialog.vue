<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :before-close="handleClose" class="gas-station-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="mode === 'view'">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="场站编码" prop="stationCode">
            <el-input v-model="formData.stationCode" placeholder="请输入场站编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场站名称" prop="stationName">
            <el-input v-model="formData.stationName" placeholder="请输入场站名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="场站类型" prop="stationType">
            <el-select v-model="formData.stationType" placeholder="请选择" class="w-full">
              <el-option v-for="item in stationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分输介质" prop="medium">
            <el-input v-model="formData.medium" placeholder="请输入分输介质" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设计供气能力" prop="designCapacity">
            <el-input v-model="formData.designCapacity" placeholder="请输入设计供气能力" type="number">
              <template #append>立方米/日</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="储罐类型" prop="tankType">
            <el-select v-model="formData.tankType" placeholder="请选择" class="w-full">
              <el-option v-for="item in tankTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="储罐压力等级" prop="tankPressureLevel">
            <el-input v-model="formData.tankPressureLevel" placeholder="请输入储罐压力等级" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="储罐数量" prop="tankCount">
            <el-input v-model="formData.tankCount" placeholder="请输入储罐数量" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总储罐容量" prop="totalTankVolume">
            <el-input v-model="formData.totalTankVolume" placeholder="请输入总储罐容量" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投入运行时间" prop="operationTime">
            <el-date-picker v-model="formData.operationTime" type="date" placeholder="请选择投入运行时间" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地理位置">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"
                :disabled="mode === 'view'"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader v-model="formData.town" :options="areaOptions" :props="{
                value: 'code',
                label: 'name',
                children: 'children'
              }" placeholder="请选择所属区域" class="w-full" @change="handleAreaChange" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入详细地址" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveGasStation, updateGasStation, getManagementUnits } from '@/api/gas';
import { STATION_TYPES, STATION_TYPE_MAP, TANK_TYPES, TANK_TYPE_MAP, AREA_OPTIONS } from '@/constants/gas';
import moment from 'moment';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  };
  return titles[props.mode] || '场站信息';
});

// 表单数据
const formData = reactive({
  id: '',
  stationCode: '',
  stationName: '',
  stationType: '',
  stationTypeName: '',
  medium: '',
  designCapacity: '',
  tankType: '',
  tankTypeName: '',
  tankPressureLevel: '',
  tankCount: '',
  totalTankVolume: '',
  contactPerson: '',
  contactPhone: '',
  managementUnit: '',
  managementUnitName: '',
  operationTime: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  stationCode: [{ required: true, message: '请输入场站编码', trigger: 'blur' }],
  stationName: [{ required: true, message: '请输入场站名称', trigger: 'blur' }],
  stationType: [{ required: true, message: '请选择场站类型', trigger: 'change' }],
  medium: [{ required: true, message: '请选择分输介质', trigger: 'change' }],
  designCapacity: [{ required: true, message: '请输入设计供气能力', trigger: 'blur' }],
  tankType: [{ required: true, message: '请选择储罐类型', trigger: 'change' }],
  tankCount: [{ required: true, message: '请输入储罐数量', trigger: 'blur' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  county: [{ required: true, message: '请选择区县', trigger: 'change' }],
  town: [{ required: true, message: '请选择乡镇', trigger: 'change' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern:/^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/, message: '请输入正确的11位手机号码', trigger: 'blur' }
  ]
};

// 场站类型选项
const stationTypeOptions = STATION_TYPES.map(({ label, value }) => ({
  label: STATION_TYPE_MAP[value],
  value
}));

// 储罐类型选项
const tankTypeOptions = TANK_TYPES.map(({ label, value }) => ({
  label,
  value
}));

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 监听场站类型变化
watch(() => formData.stationType, (newVal) => {
  if (newVal) {
    formData.stationTypeName = STATION_TYPE_MAP[newVal] || '';
  } else {
    formData.stationTypeName = '';
  }
}, { immediate: true });

// 监听储罐类型变化
watch(() => formData.tankType, (newVal) => {
  if (newVal) {
    formData.tankTypeName = TANK_TYPE_MAP[newVal] || '';
  } else {
    formData.tankTypeName = '';
  }
}, { immediate: true });

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else if (key === 'operationTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交数据
    const submitData = { ...formData };

    // 确保经纬度为数值类型
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);
    if (submitData.designCapacity) submitData.designCapacity = Number(submitData.designCapacity);
    if (submitData.tankCount) submitData.tankCount = Number(submitData.tankCount);
    if (submitData.totalTankVolume) submitData.totalTankVolume = Number(submitData.totalTankVolume);

    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveGasStation(submitData);
    } else if (props.mode === 'edit') {
      res = await updateGasStation(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.gas-station-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.location-select {
  width: 120px;
}

.coordinate-input {
  width: 120px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}
</style>