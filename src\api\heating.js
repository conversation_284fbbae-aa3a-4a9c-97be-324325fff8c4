import request from '@/utils/request'
import moment from 'moment'

// 获取报警统计数据
export function getAlarmStatistics() {
  return request({
    url: '/heat/api/v1/situation/monitor/alarm/statistics',
    method: 'get'
  })
}

// 获取风险监测设备统计数据
export function getRiskMonitorStatistics() {
  return request({
    url: '/heat/api/v1/situation/monitor/device/statistics',
    method: 'get'
  })
}

// 获取基础设施统计数据
export function getInfrastructureStatisticsScreen() {
  return request({
    url: '/heat/api/v1/situation/infrastructure/statistics',
    method: 'get'
  })
}

// 获取管线统计数据（按材质和管龄）
export function getPipelineStatistics() {
  return request({
    url: '/heat/api/v1/situation/pipeline/statistics',
    method: 'get'
  })
}

// 获取管线风险统计数据
export function getPipelineRiskStatistics() {
  return request({
    url: '/heat/api/v1/pipeline/risk/pipeline/risk/statistics',
    method: 'get'
  })
}

// 获取区域管线风险统计数据
export function getRegionPipelineRiskStatistics() {
  return request({
    url: '/heat/api/v1/pipeline/risk/region/statistics',
    method: 'get'
  })
}

// 获取热源厂风险统计数据
export function getFactoryRiskStatistics() {
  return request({
    url: '/heat/api/v1/pipeline/risk/factory/risk/statistics',
    method: 'get'
  })
}

// 获取换热站风险统计数据
export function getStationRiskStatistics() {
  return request({
    url: '/heat/api/v1/pipeline/risk/station/risk/statistics',
    method: 'get'
  })
}

// 获取管线风险统计数据（新接口）
export function getSituationPipelineRiskStatistics() {
  return request({
    url: '/heat/api/v1/situation/pipeline/risk/statistics',
    method: 'get'
  })
}

// 获取热源厂风险统计数据（新接口）
export function getSituationFactoryRiskStatistics() {
  return request({
    url: '/heat/api/v1/situation/factory/risk/statistics',
    method: 'get'
  })
}

// 获取换热站风险统计数据（新接口）
export function getSituationStationRiskStatistics() {
  return request({
    url: '/heat/api/v1/situation/station/risk/statistics',
    method: 'get'
  })
}

// 获取隐患整改统计数据
export function getHiddenDangerStatisticsScreen() {
  return request({
    url: '/heat/api/v1/situation/hiddenDanger/statistics',
    method: 'get'
  })
}

// 获取高发报警设备列表
export function getHighFrequencyDevices(params) {
  return request({
    url: '/heat/api/v1/monitor/analysis/device/high-frequency',
    method: 'post',
    params
  })
}

// 获取供热企业分页数据
export function getEnterpriseList(pageNum, pageSize, params) {
  return request({
    url: `/heat/usmBasicEnterprise/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取供热企业详情
export function getEnterpriseDetail(id) {
  return request({
    url: `/heat/usmBasicEnterprise/${id}`,
    method: 'get'
  })
}

// 删除供热企业
export function deleteEnterprise(id) {
  return request({
    url: `/heat/usmBasicEnterprise/${id}`,
    method: 'delete'
  })
}

// 获取所有企业列表
export function getAllEnterpriseList(params = {}) {
  return request({
    url: '/heat/usmBasicEnterprise/list',
    method: 'post',
    data: params
  })
}

// 新增企业信息
export function saveEnterprise(data) {
  return request({
    url: '/heat/usmBasicEnterprise/save',
    method: 'post',
    data
  })
}

// 更新企业信息
export function updateEnterprise(data) {
  return request({
    url: '/heat/usmBasicEnterprise/update',
    method: 'post',
    data
  })
}


// 获取热源工厂列表
export function getHeatFactoryList(params = {}) {
  return request({
    url: '/heat/usmBasicHeatFactory/list',
    method: 'post',
    data: params
  })
}

// 获取热源工厂分页列表
export function getHeatFactoryPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicHeatFactory/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取热源信息详情
export function getHeatFactoryDetail(id) {
  return request({
    url: `/heat/usmBasicHeatFactory/${id}`,
    method: 'get'
  })
}

// 删除热源信息
export function deleteHeatFactory(id) {
  return request({
    url: `/heat/usmBasicHeatFactory/${id}`,
    method: 'delete'
  })
}

// 新增热源信息
export function saveHeatFactory(data) {
  return request({
    url: '/heat/usmBasicHeatFactory/save',
    method: 'post',
    data
  })
}

// 更新热源信息
export function updateHeatFactory(data) {
  return request({
    url: '/heat/usmBasicHeatFactory/update',
    method: 'post',
    data
  })
}

// 获取换热站分页列表
export function getHeatStationPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicHeatStation/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取换热站信息详情
export function getHeatStationDetail(id) {
  return request({
    url: `/heat/usmBasicHeatStation/${id}`,
    method: 'get'
  })
}
// 获取所有换热站列表
export function getAllHeatStationList(params = {}) {
  return request({
    url: '/heat/usmBasicHeatStation/list',
    method: 'post',
    data: params
  })
}
// 删除换热站信息
export function deleteHeatStation(id) {
  return request({
    url: `/heat/usmBasicHeatStation/${id}`,
    method: 'delete'
  })
}

// 新增换热站信息
export function saveHeatStation(data) {
  return request({
    url: '/heat/usmBasicHeatStation/save',
    method: 'post',
    data
  })
}

// 更新换热站信息
export function updateHeatStation(data) {
  return request({
    url: '/heat/usmBasicHeatStation/update',
    method: 'post',
    data
  })
}

// 获取机组分页列表
export function getUnitPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicUnit/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取机组信息详情
export function getUnitDetail(id) {
  return request({
    url: `/heat/usmBasicUnit/${id}`,
    method: 'get'
  })
}

// 删除机组信息
export function deleteUnit(id) {
  return request({
    url: `/heat/usmBasicUnit/${id}`,
    method: 'delete'
  })
}

// 新增机组信息
export function saveUnit(data) {
  return request({
    url: '/heat/usmBasicUnit/save',
    method: 'post',
    data
  })
}

// 更新机组信息
export function updateUnit(data) {
  return request({
    url: '/heat/usmBasicUnit/update',
    method: 'post',
    data
  })
}

// 获取管线信息分页列表
export function getPipelineInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicPipeline/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管线信息详情
export function getPipelineInfoDetail(id) {
  return request({
    url: `/heat/usmBasicPipeline/${id}`,
    method: 'get'
  })
}

// 新增管线信息
export function savePipelineInfo(data) {
  return request({
    url: '/heat/usmBasicPipeline/save',
    method: 'post',
    data
  })
}

// 更新管线信息
export function updatePipelineInfo(data) {
  return request({
    url: '/heat/usmBasicPipeline/update',
    method: 'post',
    data
  })
}

// 删除管线信息
export function deletePipelineInfo(id) {
  return request({
    url: `/heat/usmBasicPipeline/${id}`,
    method: 'delete'
  })
}

// 获取管点信息分页列表
export function getPipelineNodePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicPoint/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管点信息详情
export function getPipelineNodeDetail(id) {
  return request({
    url: `/heat/usmBasicPoint/${id}`,
    method: 'get'
  })
}

// 新增管点信息
export function savePipelineNode(data) {
  return request({
    url: '/heat/usmBasicPoint/save',
    method: 'post',
    data
  })
}

// 更新管点信息
export function updatePipelineNode(data) {
  return request({
    url: '/heat/usmBasicPoint/update',
    method: 'post',
    data
  })
}

// 删除管点信息
export function deletePipelineNode(id) {
  return request({
    url: `/heat/usmBasicPoint/${id}`,
    method: 'delete'
  })
}

// 获取管线维修记录分页列表
export function getPipelineMaintenancePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicRepair/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管线维修记录详情
export function getPipelineMaintenanceDetail(id) {
  return request({
    url: `/heat/usmBasicRepair/${id}`,
    method: 'get'
  })
}

// 新增管线维修记录
export function savePipelineMaintenance(data) {
  return request({
    url: '/heat/usmBasicRepair/save',
    method: 'post',
    data
  })
}

// 更新管线维修记录
export function updatePipelineMaintenance(data) {
  return request({
    url: '/heat/usmBasicRepair/update',
    method: 'post',
    data
  })
}

// 删除管线维修记录
export function deletePipelineMaintenance(id) {
  return request({
    url: `/heat/usmBasicRepair/${id}`,
    method: 'delete'
  })
}

// 获取监测预警阈值分页列表
export function getAlarmThresholdPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmAlarmThreshold/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取监测预警阈值详情
export function getAlarmThresholdDetail(id) {
  return request({
    url: `/heat/usmAlarmThreshold/${id}`,
    method: 'get'
  })
}
// 获取报警类型
export function getAlarmType() {
  return request({
    url: `/heat/usmMonitorAlarm/getAlarmType`,
    method: 'get'
  })
}

// 新增监测预警阈值
export function saveAlarmThreshold(data) {
  return request({
    url: '/heat/usmAlarmThreshold/save',
    method: 'post',
    data
  })
}

// 更新监测预警阈值
export function updateAlarmThreshold(data) {
  return request({
    url: '/heat/usmAlarmThreshold/update',
    method: 'post',
    data
  })
}

// 删除监测预警阈值
export function deleteAlarmThreshold(id) {
  return request({
    url: `/heat/usmAlarmThreshold/${id}`,
    method: 'delete'
  })
}

// 获取监测设备列表
export function getMonitorDeviceList(params = {}) {
  return request({
    url: '/heat/usmMonitorDevice/list',
    method: 'post',
    data: params
  })
}

// 获取传感器设备分页列表
export function getSensorDevicePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmMonitorDevice/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取传感器设备详情
export function getSensorDeviceDetail(id) {
  return request({
    url: `/heat/usmMonitorDevice/${id}`,
    method: 'get'
  })
}

// 新增传感器设备
export function saveSensorDevice(data) {
  return request({
    url: '/heat/usmMonitorDevice/save',
    method: 'post',
    data
  })
}

// 更新传感器设备
export function updateSensorDevice(data) {
  return request({
    url: '/heat/usmMonitorDevice/update',
    method: 'post',
    data
  })
}

// 删除传感器设备
export function deleteSensorDevice(id) {
  return request({
    url: `/heat/usmMonitorDevice/${id}`,
    method: 'delete'
  })
}

// 获取管线列表
export function getPipelineList(params = {}) {
  return request({
    url: '/heat/usmBasicPipeline/list',
    method: 'post',
    data: params
  })
}

// 获取用户列表
export function getUserList(params = {}) {
  return request({
    url: '/heat/usmBasicUser/list',
    method: 'post',
    data: params
  })
}

// 获取窨井列表
export function getWellList(params = {}) {
  return request({
    url: '/heat/usmBasicWell/list',
    method: 'post',
    data: params
  })
}

// 获取监测指标列表
export function getMonitorIndicatorsList(params = {}) {
  return request({
    url: '/heat/usmMonitorIndicators/list',
    method: 'post',
    data: params
  })
}

// 获取设备类型列表
export function getDeviceType() {
  return request({
    url: '/heat/usmMonitorDevice/getDeviceType',
    method: 'get'
  })
}

// ============ 管网运行监测预警管理相关接口 ============

// 获取监测报警统计数据
export function getHeatingAlarmStatistics(params) {
  return request({
    url: '/heat/usmAlarmSituationAnalysis/statistics',
    method: 'post',
    data: {
      startDate: params.startDate || '',
      endDate: params.endDate || ''
    }
  })
}

// 获取报警等级统计数据
export function getHeatingAlarmLevelStatistics(params) {
  return request({
    url: '/heat/usmAlarmSituationAnalysis/level/statistics',
    method: 'post',
    data: {
      startDate: params.startDate || '',
      endDate: params.endDate || ''
    }
  })
}

// 获取报警信息列表（分页查询）
export function getHeatingAlarmList(page, size, params = {}) {
  return request({
    url: `/heat/usmMonitorAlarm/search/${page}/${size}`,
    method: 'post',
    data: params
  })
}

// 获取报警详情
export function getHeatingAlarmDetail(id) {
  return request({
    url: `/heat/usmMonitorAlarm/${id}`,
    method: 'get'
  })
}

// 报警确认
export function confirmHeatingAlarm(data) {
  return request({
    url: '/heat/usmMonitorAlarm/alarm/confirm',
    method: 'post',
    data
  })
}

// 报警处置新增/编辑
export function handleHeatingAlarm(data) {
  return request({
    url: '/heat/usmMonitorAlarm/alarm/handle',
    method: 'post',
    data
  })
}

// 删除报警处置
export function deleteHeatingAlarmHandle(id) {
  return request({
    url: `/heat/usmMonitorAlarm/alarm/handle/${id}`,
    method: 'delete'
  })
}

// 获取报警处置列表
export function getHeatingAlarmHandleList(id) {
  return request({
    url: `/heat/usmMonitorAlarm/alarm/handleList/${id}`,
    method: 'get'
  })
}

// 获取报警状态记录时间线
export function getHeatingAlarmStatusList(params) {
  return request({
    url: '/heat/usmMonitorAlarmStatus/list',
    method: 'post',
    data: params
  })
}
// 获取设备监测指标
export function getMonitorIndicators(deviceId) {
  return request({
    url: `/heat/usmMonitorRecord/monitorIndicators/${deviceId}`,
    method: 'get'
  });
}
// 获取报警监测曲线数据
export function getHeatingAlarmMonitorCurve(params) {
  return request({
    url: '/heat/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  })
}

// 获取报警记录
export function getHeatingAlarmRecords(deviceId) {
  return request({
    url: `/heat/usmMonitorAlarm/alarmRecord/${deviceId}`,
    method: 'get'
  })
}

// ============ 供热区域建筑信息管理相关接口 ============

// 获取建筑信息分页列表
export function getBuildingPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicBuilding/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取建筑信息详情
export function getBuildingDetail(id) {
  return request({
    url: `/heat/usmBasicBuilding/${id}`,
    method: 'get'
  })
}

// 新增建筑信息
export function saveBuilding(data) {
  return request({
    url: '/heat/usmBasicBuilding/save',
    method: 'post',
    data
  })
}

// 更新建筑信息
export function updateBuilding(data) {
  return request({
    url: '/heat/usmBasicBuilding/update',
    method: 'post',
    data
  })
}

// 删除建筑信息
export function deleteBuilding(id) {
  return request({
    url: `/heat/usmBasicBuilding/${id}`,
    method: 'delete'
  })
}

// 获取所有机组列表
export function getAllUnitList(params = {}) {
  return request({
    url: '/heat/usmBasicUnit/list',
    method: 'post',
    data: params
  })
}

// ============ 供热用户信息管理相关接口 ============

// 获取用户信息分页列表
export function getUserPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicUser/search/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取用户信息详情
export function getUserDetail(id) {
  return request({
    url: `/heat/usmBasicUser/${id}`,
    method: 'get'
  })
}

// 新增用户信息
export function saveUser(data) {
  return request({
    url: '/heat/usmBasicUser/save',
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUser(data) {
  return request({
    url: '/heat/usmBasicUser/update',
    method: 'post',
    data
  })
}

// 删除用户信息
export function deleteUser(id) {
  return request({
    url: `/heat/usmBasicUser/${id}`,
    method: 'delete'
  })
}

// 获取所有建筑列表
export function getAllBuildingList(params = {}) {
  return request({
    url: '/heat/usmBasicBuilding/list',
    method: 'post',
    data: params
  })
}

// ============ 供热窨井信息管理相关接口 ============

// 获取窨井信息分页列表
export function getManholeWellPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmBasicWell/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取窨井信息详情
export function getManholeWellDetail(id) {
  return request({
    url: `/heat/usmBasicWell/${id}`,
    method: 'get'
  })
}

// 新增窨井信息
export function saveManholeWell(data) {
  return request({
    url: '/heat/usmBasicWell/save',
    method: 'post',
    data
  })
}

// 更新窨井信息
export function updateManholeWell(data) {
  return request({
    url: '/heat/usmBasicWell/update',
    method: 'post',
    data
  })
}

// 删除窨井信息
export function deleteManholeWell(id) {
  return request({
    url: `/heat/usmBasicWell/${id}`,
    method: 'delete'
  })
}

// ============ 基础数据统计分析相关接口 ============

// 获取基础设施统计数据
export function getInfrastructureStatistics(params = {}) {
  return request({
    url: '/heat/usmBasicStatisticsAnalysis/infrastructure/statistics',
    method: 'post',
    data: params
  })
}

// 获取管线材料统计数据
export function getPipelineMaterialStatistics(params = {}) {
  return request({
    url: '/heat/usmBasicStatisticsAnalysis/pipeline/material/statistics',
    method: 'post',
    data: params
  })
}

// 获取管线年龄统计数据
export function getPipelineAgeStatistics(params = {}) {
  return request({
    url: '/heat/usmBasicStatisticsAnalysis/pipeline/age/statistics',
    method: 'post',
    data: params
  })
}

// ============ 预案管理相关接口 ============

// 获取预案分页列表
export function getEmergencyPlanPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmEmergencyScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取预案详情
export function getEmergencyPlanDetail(id) {
  return request({
    url: `/heat/usmEmergencyScheme/${id}`,
    method: 'get'
  })
}

// 新增预案
export function saveEmergencyPlan(data) {
  return request({
    url: '/heat/usmEmergencyScheme/save',
    method: 'post',
    data
  })
}

// 更新预案
export function updateEmergencyPlan(data) {
  return request({
    url: '/heat/usmEmergencyScheme/update',
    method: 'post',
    data
  })
}

// 删除预案
export function deleteEmergencyPlan(id) {
  return request({
    url: `/heat/usmEmergencyScheme/${id}`,
    method: 'delete'
  })
}

// ============ 供热安全事故信息管理相关接口 ============

// 获取事故信息分页列表
export function getAccidentPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmRiskEvent/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取事故信息详情
export function getAccidentDetail(id) {
  return request({
    url: `/heat/usmRiskEvent/${id}`,
    method: 'get'
  })
}

// 新增事故信息
export function saveAccident(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.eventTime) {
    submitData.eventTime = moment(submitData.eventTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.receiveTime) {
    submitData.receiveTime = moment(submitData.receiveTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handleTime) {
    submitData.handleTime = moment(submitData.handleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskEvent/save',
    method: 'post',
    data: submitData
  })
}

// 更新事故信息
export function updateAccident(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.eventTime) {
    submitData.eventTime = moment(submitData.eventTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.receiveTime) {
    submitData.receiveTime = moment(submitData.receiveTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handleTime) {
    submitData.handleTime = moment(submitData.handleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskEvent/update',
    method: 'post',
    data: submitData
  })
}

// 删除事故信息
export function deleteAccident(id) {
  return request({
    url: `/heat/usmRiskEvent/${id}`,
    method: 'delete'
  })
}

// 根据事件分类查询处置方案
export function getEmergencySchemeByEventType(eventType) {
  return request({
    url: '/heat/usmEmergencyScheme/list',
    method: 'post',
    data: { eventType }
  })
}

// ============ 危险源信息管理相关接口 ============

// 获取危险源分页列表
export function getDangerPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmRiskDanger/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取危险源详情
export function getDangerDetail(id) {
  return request({
    url: `/heat/usmRiskDanger/${id}`,
    method: 'get'
  })
}

// 新增危险源
export function saveDanger(data) {
  return request({
    url: '/heat/usmRiskDanger/save',
    method: 'post',
    data
  })
}

// 更新危险源
export function updateDanger(data) {
  return request({
    url: '/heat/usmRiskDanger/update',
    method: 'post',
    data
  })
}

// 删除危险源
export function deleteDanger(id) {
  return request({
    url: `/heat/usmRiskDanger/${id}`,
    method: 'delete'
  })
}

// ============ 防护目标信息管理相关接口 ============

// 获取防护目标分页列表
export function getProtectionPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmRiskProtect/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取防护目标详情
export function getProtectionDetail(id) {
  return request({
    url: `/heat/usmRiskProtect/${id}`,
    method: 'get'
  })
}

// 新增防护目标
export function saveProtection(data) {
  return request({
    url: '/heat/usmRiskProtect/save',
    method: 'post',
    data
  })
}

// 更新防护目标
export function updateProtection(data) {
  return request({
    url: '/heat/usmRiskProtect/update',
    method: 'post',
    data
  })
}

// 删除防护目标
export function deleteProtection(id) {
  return request({
    url: `/heat/usmRiskProtect/${id}`,
    method: 'delete'
  })
}

// ============ 态势数据报送相关接口 ============

// 获取态势数据报送分页列表
export function getSituationReportPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmSituationReport/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取态势数据报送详情
export function getSituationReportDetail(id) {
  return request({
    url: `/heat/usmSituationReport/${id}`,
    method: 'get'
  })
}

// 新增态势数据报送
export function saveSituationReport(data) {
  return request({
    url: '/heat/usmSituationReport/save',
    method: 'post',
    data
  })
}

// 更新态势数据报送
export function updateSituationReport(data) {
  return request({
    url: '/heat/usmSituationReport/update',
    method: 'post',
    data
  })
}

// 删除态势数据报送
export function deleteSituationReport(id) {
  return request({
    url: `/heat/usmSituationReport/${id}`,
    method: 'delete'
  })
}

// ============ 态势数据分析相关接口 ============

/**
 * 获取供热报警统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatAlarmStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmAlarmSituationAnalysis/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热报警处置、误报统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatAlarmDisposalSituation(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmAlarmSituationAnalysis/statistics/disposalSituation',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热报警趋势分析统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatAlarmTrendStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmAlarmSituationAnalysis/trend/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热报警等级统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatAlarmLevelStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmAlarmSituationAnalysis/level/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热高发报警设备列表
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatAlarmHighFrequencyDevices(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmAlarmSituationAnalysis/device/high-frequency',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热企业报警信息统计
 * @param {number} pageNum - 页码
 * @param {number} pageSize - 每页大小
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatAlarmEnterpriseStatistics(pageNum, pageSize, params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/heat/usmAlarmSituationAnalysis/enterprise/statistics`,
    method: 'post',
    data: submitData,
    params: {
      current: pageNum,
      size: pageSize
    }
  })
}

/**
 * 获取供热事件统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatEventStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmEventSituationAnalysis/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热事件分类统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatEventTypeStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmEventSituationAnalysis/type/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热事件趋势分析统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatEventTrendStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmEventSituationAnalysis/trend/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热企业事件统计
 * @param {number} pageNum - 页码
 * @param {number} pageSize - 每页大小
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatEventEnterpriseStatistics(pageNum, pageSize, params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/heat/usmEventSituationAnalysis/enterprise/statistics`,
    method: 'post',
    data: submitData,
    params: {
      current: pageNum,
      size: pageSize
    }
  })
}

/**
 * 获取供热隐患统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatDangerStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmDangerSituationAnalysis/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热隐患趋势分析统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatDangerTrendStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmDangerSituationAnalysis/trend/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热隐患核心指标统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatDangerCoreStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmDangerSituationAnalysis/core/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热隐患等级统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatDangerLevelStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmDangerSituationAnalysis/level/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热隐患类型统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatDangerTypeStatistics(params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmDangerSituationAnalysis/type/statistics',
    method: 'post',
    data: submitData
  })
}

/**
 * 获取供热企业隐患统计
 * @param {number} pageNum - 页码
 * @param {number} pageSize - 每页大小
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始时间
 * @param {string} params.endDate - 结束时间
 */
export function getHeatDangerEnterpriseStatistics(pageNum, pageSize, params) {
  const submitData = { ...params }
  if (submitData.startDate) {
    submitData.startDate = moment(submitData.startDate).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endDate) {
    submitData.endDate = moment(submitData.endDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/heat/usmDangerSituationAnalysis/enterprise/statistics`,
    method: 'post',
    data: submitData,
    params: {
      current: pageNum,
      size: pageSize
    }
  })
}

// ============ 供热管网风险评估管理相关接口 ============

// 获取管网风险评估统计数据
export function getPipelineRiskAssessmentStatistics() {
  return request({
    url: '/heat/usmRiskAssessmentPipeline/statistics',
    method: 'get'
  })
}

// 获取管网风险评估分页数据
export function getPipelineRiskAssessmentPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmRiskAssessmentPipeline/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管网风险评估详情
export function getPipelineRiskAssessmentDetail(id) {
  return request({
    url: `/heat/usmRiskAssessmentPipeline/${id}`,
    method: 'get'
  })
}

// 更新管网风险评估
export function updatePipelineRiskAssessment(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.assessmentDate) {
    submitData.assessmentDate = moment(submitData.assessmentDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskAssessmentPipeline/update',
    method: 'post',
    data: submitData
  })
}

// ============ 供热场站风险评估管理相关接口 ============

// 获取场站风险评估统计数据
export function getStationRiskAssessmentStatistics() {
  return request({
    url: '/heat/usmRiskAssessmentFactory/statistics',
    method: 'get'
  })
}

// 获取场站风险评估分页数据
export function getStationRiskAssessmentPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/heat/usmRiskAssessmentFactory/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取场站风险评估详情
export function getStationRiskAssessmentDetail(id) {
  return request({
    url: `/heat/usmRiskAssessmentFactory/${id}`,
    method: 'get'
  })
}

// 更新场站风险评估
export function updateStationRiskAssessment(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.assessmentDate) {
    submitData.assessmentDate = moment(submitData.assessmentDate).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskAssessmentFactory/update',
    method: 'post',
    data: submitData
  })
}

// ============ 供热隐患信息管理相关接口 ============

// 获取隐患状态统计数据
export function getHeatingHiddenDangerStatusStatistics() {
  return request({
    url: '/heat/usmRiskHiddenDanger/statisticsByStatus',
    method: 'get'
  })
}

// 获取隐患等级统计数据
export function getHeatingHiddenDangerLevelStatistics() {
  return request({
    url: '/heat/usmRiskHiddenDanger/statisticsByLevel',
    method: 'get'
  })
}

// 获取隐患分页数据
export function getHeatingHiddenDangerPage(pageNum, pageSize, params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/heat/usmRiskHiddenDanger/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: submitData
  })
}

// 获取隐患详情
export function getHeatingHiddenDangerDetail(id) {
  return request({
    url: `/heat/usmRiskHiddenDanger/${id}`,
    method: 'get'
  })
}

// 新增隐患信息
export function saveHeatingHiddenDanger(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.rectificationDeadline) {
    submitData.rectificationDeadline = moment(submitData.rectificationDeadline).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskHiddenDanger/save',
    method: 'post',
    data: submitData
  })
}

// 更新隐患信息
export function updateHeatingHiddenDanger(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.rectificationDeadline) {
    submitData.rectificationDeadline = moment(submitData.rectificationDeadline).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskHiddenDanger/update',
    method: 'post',
    data: submitData
  })
}

// 获取隐患整改列表
export function getHeatingHiddenDangerHandleList(id) {
  return request({
    url: `/heat/usmRiskHiddenDanger/handleList/${id}`,
    method: 'get'
  })
}

// 新增/编辑隐患整改
export function addHeatingHiddenDangerHandle(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskHiddenDanger/addHandle',
    method: 'post',
    data: submitData
  })
}

// 删除隐患整改
export function deleteHeatingHiddenDangerHandle(id) {
  return request({
    url: `/heat/usmRiskHiddenDanger/deleteHandle/${id}`,
    method: 'delete'
  })
}

// 隐患复查
export function reviewHeatingHiddenDanger(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: '/heat/usmRiskHiddenDanger/review',
    method: 'post',
    data: submitData
  })
}

// 获取隐患整改状态时间线
export function getHeatingHiddenDangerStatusList(dangerId) {
  return request({
    url: '/heat/usmRiskHiddenDangerStatus/list',
    method: 'post',
    data: { dangerId }
  })
}

// ============ 供热管线风险清单相关接口 ============

// 获取管线风险清单数据
export function getPipelineRiskList(pageNum = 1, pageSize = 10, params = {}) {
  return request({
    url: `/heat/api/v1/pipeline/risk/pipeline/list`,
    method: 'get',
    params: {
      pageNum,
      pageSize,
      ...params
    }
  })
}

// 获取热源厂风险清单数据
export function getFactoryRiskList(pageNum = 1, pageSize = 10, params = {}) {
  return request({
    url: `/heat/api/v1/pipeline/risk/facotry/list`,
    method: 'get',
    params: {
      pageNum,
      pageSize,
      ...params
    }
  })
}

// 获取换热站风险清单数据
export function getStationRiskList(pageNum = 1, pageSize = 10, params = {}) {
  return request({
    url: `/heat/api/v1/pipeline/risk/station/list`,
    method: 'get',
    params: {
      pageNum,
      pageSize,
      ...params
    }
  })
}

// 获取隐患信息统计数据
export function getHiddenDangerStatistics(pageNum = 1, pageSize = 10, dayIndex = 7, params = {}) {
  return request({
    url: `/heat/api/v1/pipeline/risk/hiddenDanger/statistics`,
    method: 'get',
    params: {
      pageNum,
      pageSize,
      dayIndex,
      ...params
    }
  })
}

// 获取隐患类型统计数据
export function getHiddenDangerTypeStatistics(dayIndex = 7, params = {}) {
  return request({
    url: `/heat/api/v1/pipeline/risk/hiddenDanger/type/statistics`,
    method: 'get',
    params: {
      dayIndex,
      ...params
    }
  })
}

// 获取隐患整改分析数据
export function getHiddenDangerRectifyAnalysis(dayIndex = 7, params = {}) {
  return request({
    url: `/heat/api/v1/pipeline/risk/hiddenDanger/rectify/analysis`,
    method: 'get',
    params: {
      dayIndex,
      ...params
    }
  })
}

// 获取监测设备统计数据
export function getMonitorDeviceStatistics(params = {}) {
  return request({
    url: `/heat/api/v1/monitor/analysis/device/statistics`,
    method: 'get',
    params
  })
}

// 获取监测分析统计数据（报警统计）
export function getMonitorAnalysisStatistics(pageNum = 1, pageSize = 10, dayIndex = 7, params = {}) {
  return request({
    url: `/heat/api/v1/monitor/analysis/statistics`,
    method: 'post',
    params: {
      pageNum,
      pageSize,
      dayIndex,
      ...params
    }
  })
}

// 获取监测分析统计条件数据（报警列表）
export function getMonitorAnalysisStatisticsCondition(pageNum = 1, startDate, endDate, params = {}) {
  return request({
    url: `/heat/api/v1/monitor/analysis/statistics/condition`,
    method: 'post',
    data: {
      pageNum,
      startDate,
      endDate,
      ...params
    }
  })
}

// 获取监测分析等级统计数据
export function getMonitorAnalysisLevelStatistics(dayIndex = 7, params = {}) {
  return request({
    url: `/heat/api/v1/monitor/analysis/level/statistics`,
    method: 'post',
    params: {
      dayIndex,
      ...params
    }
  })
}

// 获取监测分析趋势统计数据
export function getMonitorAnalysisTrendStatistics(dayIndex = 7, params = {}) {
  return request({
    url: `/heat/api/v1/monitor/analysis/trend/statistics`,
    method: 'post',
    params: {
      dayIndex,
      ...params
    }
  })
}