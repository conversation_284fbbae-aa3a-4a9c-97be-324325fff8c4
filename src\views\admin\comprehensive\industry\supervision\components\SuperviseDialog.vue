<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="supervise-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工作事项" prop="taskName">
            <el-input v-model="formData.taskName" placeholder="请输入工作事项" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择所属行业" class="w-full" @change="handleBusinessChange">
              <el-option v-for="item in businessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="签发单位" prop="signUnit">
            <el-input v-model="formData.signUnit" placeholder="请输入签发单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主办单位" prop="organizeUnit">
            <el-input v-model="formData.organizeUnit" placeholder="请输入主办单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="协办单位" prop="coOrganizeUnit">
            <el-input v-model="formData.coOrganizeUnit" placeholder="请输入协办单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事项状态" prop="taskStatus">
            <el-select v-model="formData.taskStatus" placeholder="请选择事项状态" class="w-full" @change="handleTaskStatusChange">
              <el-option v-for="item in taskStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务下达时间" prop="issuedTime">
            <el-date-picker
              v-model="formData.issuedTime"
              type="datetime"
              placeholder="请选择任务下达时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="要求完成时间" prop="requiredCompletionTime">
            <el-date-picker
              v-model="formData.requiredCompletionTime"
              type="datetime"
              placeholder="请选择要求完成时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="相关附件">
            <div class="file-upload-container">
              <!-- 文件上传按钮 -->
              <div class="upload-header">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :file-list="[]"
                  :disabled="mode === 'view' || fileList.length >= 3"
                  :show-file-list="false"
                  multiple
                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                >
                  <el-button 
                    type="primary" 
                    :disabled="mode === 'view' || fileList.length >= 3"
                  >
                    选择文件
                  </el-button>
                </el-upload>
                <span class="upload-tip">请上传大小不超过10MB格式为doc/docx/pdf/md的文件</span>
              </div>
              
              <!-- 文件列表表格 -->
              <div class="file-list-table" v-if="fileList.length > 0">
                <div class="table-header">
                  <div class="column file-name">文件名</div>
                  <div class="column file-size">大小</div>
                  <div class="column file-actions">操作</div>
                </div>
                <div 
                  class="table-row" 
                  v-for="(file, index) in fileList" 
                  :key="file.uid || index"
                >
                  <div class="column file-name">
                    <div class="file-info">
                      <el-icon class="file-icon">
                        <Document v-if="isDocumentFile(file.name)" />
                        <Picture v-else-if="isImageFile(file.name)" />
                        <Files v-else />
                      </el-icon>
                      <span class="file-name-text">{{ file.name }}</span>
                    </div>
                  </div>
                  <div class="column file-size">
                    {{ formatFileSize(file.size) }}
                  </div>
                  <div class="column file-actions">
                    <div class="action-buttons">
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileRemove(file, index)"
                        v-if="mode !== 'view'"
                      >
                        删除
                      </el-button>
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileDownload(file)"
                        v-if="file.url && file.status === 'success'"
                      >
                        下载
                      </el-button>
                      <div class="upload-status" v-if="file.status === 'uploading'">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>上传中</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 详情模式下显示反馈记录 -->
      <div v-if="mode === 'view' && feedbackRecords.length > 0" class="feedback-section">
        <div class="feedback-title">
          <el-icon><Clock /></el-icon>
          <span>反馈记录</span>
        </div>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in feedbackRecords"
            :key="record.id"
            :timestamp="record.dealTime"
            placement="top"
            :type="getTimelineType(record.superviseStatus)"
          >
            <el-card>
              <h4>{{ record.superviseStatusName }}</h4>
              <p>{{ record.description }}</p>
              <p v-if="record.handleStatusName">处理状态：{{ record.handleStatusName }}</p>
              <div v-if="record.fileUrls" class="attachment-info">
                <el-icon><Paperclip /></el-icon>
                <span>有附件</span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files, Loading, Clock, Paperclip } from '@element-plus/icons-vue'
import {
  saveSupervise,
  updateSupervise,
  getSuperviseStatusList
} from '@/api/comprehensive'
import { uploadFile } from '@/api/upload'
import { 
  SUPERVISE_BUSINESS_OPTIONS, 
  TASK_STATUS_OPTIONS 
} from '@/constants/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const uploadRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  }
  return titles[props.mode] || '督查督办'
})

// 下拉选项
const businessOptions = ref(SUPERVISE_BUSINESS_OPTIONS)
const taskStatusOptions = ref(TASK_STATUS_OPTIONS)

// 文件列表
const fileList = ref([])

// 反馈记录
const feedbackRecords = ref([])

// 表单数据
const formData = reactive({
  id: '',
  taskName: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  signUnit: '',
  signUnitName: '',
  organizeUnit: '',
  organizeUnitName: '',
  coOrganizeUnit: '',
  coOrganizeUnitName: '',
  taskStatus: '',
  taskStatusName: '',
  issuedTime: '',
  issuedUnit: '',
  issuedUnitName: '',
  requiredCompletionTime: '',
  fileUrls: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  taskName: [{ required: true, message: '请输入工作事项', trigger: 'blur' }],
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  signUnit: [{ required: true, message: '请输入签发单位', trigger: 'blur' }],
  organizeUnit: [{ required: true, message: '请输入主办单位', trigger: 'blur' }],
  taskStatus: [{ required: true, message: '请选择事项状态', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  fileList.value = []
  feedbackRecords.value = []
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx', '.md']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 获取时间线类型
const getTimelineType = (status) => {
  const typeMap = {
    7000701: 'primary', // 新增
    7000702: 'warning', // 下发
    7000703: 'success'  // 处理
  }
  return typeMap[status] || 'info'
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, async (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理文件显示
    if (newVal.fileUrls) {
      const urls = newVal.fileUrls.split(',').filter(url => url.trim())
      const existingFiles = urls.map((url, index) => ({
        name: `attachment_${index + 1}${getFileExtension(url)}`,
        url: url.trim(),
        uid: Date.now() + index,
        status: 'success',
        size: 0
      }))
      fileList.value = existingFiles
    }

    // 详情模式加载反馈记录
    if (props.mode === 'view' && newVal.id) {
      await fetchFeedbackRecords(newVal.id)
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 获取文件扩展名
const getFileExtension = (url) => {
  const match = url.match(/\.[^.]*$/)
  return match ? match[0] : ''
}

// 处理所属行业选择变化
const handleBusinessChange = (value) => {
  const selected = businessOptions.value.find(item => item.value === value)
  if (selected) {
    formData.relatedBusinessName = selected.label
  }
}

// 处理事项状态选择变化
const handleTaskStatusChange = (value) => {
  const selected = taskStatusOptions.value.find(item => item.value === value)
  if (selected) {
    formData.taskStatusName = selected.label
  }
}

// 处理文件变化
const handleFileChange = async (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 3) {
    ElMessage.warning('最多只能上传3个文件')
    return false
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }

  // 添加到文件列表，状态为上传中
  const fileItem = {
    name: file.name,
    size: file.size,
    uid: file.uid || Date.now(),
    status: 'uploading',
    raw: file,
    url: ''
  }
  
  fileList.value.push(fileItem)

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response && response.status === 200) {
      // 更新文件状态为成功
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value[index].url = response.data.url
        fileList.value[index].status = 'success'
      }
      
      // 更新formData中的fileUrls
      updateFileUrl()
      
      ElMessage.success('文件上传成功')
    } else {
      // 上传失败，移除文件
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value.splice(index, 1)
      }
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 上传失败，移除文件
    const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
const handleFileRemove = (file, index) => {
  fileList.value.splice(index, 1)
  updateFileUrl()
  ElMessage.success('文件已移除')
}

// 处理文件下载
const handleFileDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 更新文件URL字符串
const updateFileUrl = () => {
  nextTick(() => {
    const urls = fileList.value
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    formData.fileUrls = urls.join(',')
  })
}

// 获取反馈记录
const fetchFeedbackRecords = async (superviseId) => {
  try {
    const res = await getSuperviseStatusList(superviseId)
    if (res && res.code === 200) {
      feedbackRecords.value = res.data || []
    }
  } catch (error) {
    console.error('获取反馈记录失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveSupervise(submitData)
    } else if (props.mode === 'edit') {
      res = await updateSupervise(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style scoped>
.supervise-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

/* 文件列表表格样式 */
.file-list-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f5f7fa;
}

.column {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.file-name {
  flex: 1;
  min-width: 0;
}

.file-size {
  width: 100px;
  justify-content: center;
}

.file-actions {
  width: 120px;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
}

.upload-status .el-icon {
  font-size: 14px;
}

.upload-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 反馈记录样式 */
.feedback-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.feedback-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.feedback-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.attachment-info {
  display: flex;
  align-items: center;
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

.attachment-info .el-icon {
  margin-right: 4px;
}

:deep(.el-timeline-item__content) {
  margin-bottom: 16px;
}

:deep(.el-card) {
  border-radius: 4px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style> 