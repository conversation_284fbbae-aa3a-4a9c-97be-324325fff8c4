<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'报警详情'"
    width="80%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    class="gas-leak-alert-dialog"
  >
    <el-tabs v-model="activeTab">
      <!-- 报警详情标签页 -->
      <el-tab-pane label="报警详情" name="detail">
        <div class="detail-container">
          <div class="detail-left">
            <div class="detail-item">
              <span class="item-label">报警编号：</span>
              <span class="item-value">{{ alarmDetail.alarmCode }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警时间：</span>
              <span class="item-value">{{ alarmDetail.alarmTime }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警值：</span>
              <span class="item-value">{{ alarmDetail.alarmValue }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警位置：</span>
              <span class="item-value">{{ alarmDetail.alarmLocation }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警级别：</span>
              <span class="item-value" :class="getAlarmLevelClass(alarmDetail.alarmLevel)">
                {{ getAlarmLevelText(alarmDetail.alarmLevel) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警状态：</span>
              <span class="item-value">{{ alarmDetail.alarmStatusName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">设备编码：</span>
              <span class="item-value">{{ alarmDetail.deviceCode }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">设备名称：</span>
              <span class="item-value">{{ alarmDetail.deviceName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">报警类型：</span>
              <span class="item-value">{{ alarmDetail.alarmType }}</span>
            </div>
          </div>
          <div class="detail-right">
            <div class="timeline-container">
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in activities"
                  :key="index"
                  :type="activity.type"
                  :size="activity.size"
                  :hollow="activity.hollow"
                >
                  <h4>{{ activity.title }}</h4>
                  <p v-if="activity.content">{{ activity.content }}</p>
                  <p v-if="activity.extra.confirmResult">确认结果：{{ activity.extra.confirmResult }}</p>
                  <p v-if="activity.extra.handleStatus">处置状态：{{ activity.extra.handleStatus }}</p>
                  <p v-if="activity.extra.handleUser">处置人：{{ activity.extra.handleUser }}</p>
                  <p v-if="activity.extra.unit">处置单位：{{ activity.extra.unit }}</p>
                  <p>{{ activity.timestamp }}</p>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 监测曲线标签页 -->
      <el-tab-pane label="监测曲线" name="curve">
        <div class="curve-container">
          <div class="curve-header">
            <el-radio-group v-model="selectedTimeRange" @change="handleTimeRangeChange">
              <el-radio-button :value ="'24h'">近24小时</el-radio-button>
              <el-radio-button :value="'7d'">近7天</el-radio-button>
              <el-radio-button :value="'30d'">近30天</el-radio-button>
            </el-radio-group>
            <el-select 
              v-model="selectedIndicator" 
              @change="handleIndicatorChange" 
              placeholder="请选择监测指标"
              style="margin-left: 20px; width: 200px;"
            >
              <el-option
                v-for="indicator in monitorIndicators"
                :key="indicator.monitorField"
                :label="indicator.monitorIndexName"
                :value="indicator.monitorField"
              />
            </el-select>
          </div>
          <div class="curve-chart" ref="chartRef" v-loading="chartLoading" element-loading-text="加载中..."></div>
        </div>
      </el-tab-pane>

      <!-- 报警记录标签页 -->
      <el-tab-pane label="报警记录" name="record">
        <div class="record-container">
          <el-table :data="alarmRecords" style="width: 100%">
            <el-table-column prop="alarmTime" label="报警时间" min-width="180" />
            <el-table-column prop="alarmValue" label="报警值" min-width="120" />
            <el-table-column prop="alarmLevelName" label="报警级别" min-width="120">
              <template #default="scope">
                <span :class="getAlarmLevelClass(scope.row.alarmLevel)">
                  {{ scope.row.alarmLevelName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="alarmStatusName" label="报警状态" min-width="120" />
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue';
import { ElDialog, ElTabs, ElTabPane, ElButton, ElTimeline, ElTimelineItem, ElDatePicker, ElTable, ElTableColumn, ElSelect, ElOption } from 'element-plus';
import { getAlarmDetail, getMonitorCurveData, getAlarmRecord, getAlarmStatusList, getMonitorIndicators } from '@/api/gas';
import { ALARM_LEVEL_MAP } from '@/constants/gas';
import * as echarts from 'echarts';

// 接收父组件传递的参数
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  alarmId: {
    type: String,
    default: ''
  }
});

// 向父组件发送事件
const emit = defineEmits(['update:visible']);

// 弹窗显示状态
const dialogVisible = ref(false);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.alarmId) {
    // 重置标签页到第一个报警详情标签
    activeTab.value = 'detail';
    fetchAlarmDetail();
    fetchAlarmStatusList(props.alarmId);
  }
});

// 监听弹窗显示状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 当前激活的标签页
const activeTab = ref('detail');

// 监听标签页切换
watch(() => activeTab.value, (newVal) => {
  if (newVal === 'curve') {
    nextTick(() => {
      // 重新初始化图表
      if (chart) {
        chart.dispose();
        chart = null;
      }
      if (selectedIndicator.value) {
        fetchMonitorCurve(getTimeRange(selectedTimeRange.value));
      }
    });
  }
});

// 报警详情数据
const alarmDetail = ref({});

// 时间轴数据
const activities = ref([]);

// 获取报警状态记录
const fetchAlarmStatusList = async (alarmId) => {
  try {
    const res = await getAlarmStatusList({ alarmId });
    if (res.code === 200 && res.data) {
      activities.value = res.data.map(item => ({
        title: item.alarmStatusName,
        content: item.description || '',
        timestamp: item.createTime,
        type: getStatusType(item.alarmStatus),
        size: 'normal',
        hollow: false,
        extra: {
          confirmResult: item.confirmResultName,
          handleStatus: item.handleStatusName,
          handleUser: item.handleUser,
          unit: item.unit
        }
      }));
    }
  } catch (error) {
    console.error('获取报警状态记录失败:', error);
  }
};

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '92001': 'danger',    // 发生报警
    '92002': 'warning',   // 确认报警
    '92003': 'success',   // 处置报警
    '92004': 'info'       // 关闭报警
  };
  return typeMap[status] || 'info';
};

// 监测曲线相关
const selectedTimeRange = ref('24h');
const selectedIndicator = ref('');
const monitorIndicators = ref([]);
const currentIndicatorInfo = ref(null);
const chartRef = ref(null);
const chartLoading = ref(false);
let chart = null;

// 格式化日期为 YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
  const pad = (num) => String(num).padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};

// 计算时间范围
const getTimeRange = (type) => {
  const endTime = new Date();
  let startTime = new Date();
  
  switch (type) {
    case '7d':
      startTime.setDate(endTime.getDate() - 7);
      break;
    case '30d':
      startTime.setDate(endTime.getDate() - 30);
      break;
    default: // 24h
      startTime.setDate(endTime.getDate() - 1);
      break;
  }
  
  return {
    startTime: formatDate(startTime),
    endTime: formatDate(endTime)
  };
};

// 报警记录数据
const alarmRecords = ref([]);

// 获取报警详情
const fetchAlarmDetail = async () => {
  try {
    const res = await getAlarmDetail(props.alarmId);
    if (res.code === 200 && res.data) {
      alarmDetail.value = res.data;
      // 获取该设备的报警记录
      fetchAlarmRecord(res.data.deviceId);
      // 获取该设备的监测指标
      await fetchMonitorIndicators(res.data.deviceId);
      // 设置默认时间范围为24小时
      selectedTimeRange.value = '24h';
    }
  } catch (error) {
    console.error('获取报警详情失败:', error);
  }
};

// 获取设备监测指标
const fetchMonitorIndicators = async (deviceId) => {
  try {
    const res = await getMonitorIndicators(deviceId);
    if (res.code === 200 && res.data && Array.isArray(res.data)) {
      monitorIndicators.value = res.data;
      // 默认选择第一个指标
      if (res.data.length > 0) {
        selectedIndicator.value = res.data[0].monitorField;
        currentIndicatorInfo.value = res.data[0];
        // 获取监测曲线数据
        fetchMonitorCurve(getTimeRange('24h'));
      }
    }
  } catch (error) {
    console.error('获取监测指标失败:', error);
  }
};

// 获取监测曲线数据
const fetchMonitorCurve = async (timeRange) => {
  if (!alarmDetail.value.deviceId || !timeRange || !selectedIndicator.value) return;
  
  try {
    chartLoading.value = true;
    const params = {
      deviceId: alarmDetail.value.deviceId,
      startTime: timeRange.startTime,
      endTime: timeRange.endTime
    };
    const res = await getMonitorCurveData(params);
    if (res.code === 200 && res.data) {
      initChart(res.data);
    }
  } catch (error) {
    console.error('获取监测曲线数据失败:', error);
  } finally {
    chartLoading.value = false;
  }
};

// 获取报警记录
const fetchAlarmRecord = async (deviceId) => {
  try {
    const res = await getAlarmRecord(deviceId);
    if (res.code === 200 && res.data) {
      alarmRecords.value = Array.isArray(res.data) ? res.data : [res.data];
    }
  } catch (error) {
    console.error('获取报警记录失败:', error);
  }
};

// 生成默认时间数据
const generateDefaultTimeData = (timeRange) => {
  const startTime = new Date(timeRange.startTime);
  const endTime = new Date(timeRange.endTime);
  const timeData = [];
  
  // 根据时间范围确定间隔
  let interval = 60 * 60 * 1000; // 默认1小时间隔
  if (selectedTimeRange.value === '24h') {
    interval = 60 * 60 * 1000; // 1小时
  } else if (selectedTimeRange.value === '7d') {
    interval = 6 * 60 * 60 * 1000; // 6小时
  } else if (selectedTimeRange.value === '30d') {
    interval = 24 * 60 * 60 * 1000; // 1天
  }
  
  let currentTime = new Date(startTime);
  while (currentTime <= endTime) {
    timeData.push(formatDate(currentTime));
    currentTime = new Date(currentTime.getTime() + interval);
  }
  
  return timeData;
};

// 初始化图表
const initChart = (data) => {
  if (!chartRef.value || !Array.isArray(data) || !currentIndicatorInfo.value) return;

  // 根据选中的指标字段过滤数据
  const fieldName = selectedIndicator.value;
  let validData = data.filter(item => 
    item && 
    item.monitorTime && 
    item[fieldName] !== undefined && 
    item[fieldName] !== null
  );
  
  // 如果没有有效数据，生成默认的零值数据
  if (validData.length === 0) {
    console.warn('没有有效的监测数据，生成默认数据');
    const timeRange = getTimeRange(selectedTimeRange.value);
    const defaultTimeData = generateDefaultTimeData(timeRange);
    
    validData = defaultTimeData.map(time => ({
      monitorTime: time,
      [fieldName]: 0
    }));
  }

  // 使用nextTick确保DOM已经更新
  nextTick(() => {
    if (!chart) {
      chart = echarts.init(chartRef.value);
    }

    // 处理数据
    const xData = validData.map(item => item.monitorTime);
    const indicatorType = currentIndicatorInfo.value.type;
    const unit = currentIndicatorInfo.value.measureUnit || '';
    const minRange = parseFloat(currentIndicatorInfo.value.measureRangeLow);
    const maxRange = parseFloat(currentIndicatorInfo.value.measureRangeUp);

    let option = {};

    if (indicatorType === 1) {
      // 数值型指标
      const yData = validData.map(item => {
        const value = parseFloat(item[fieldName]);
        return isNaN(value) ? null : value;
      });

      const series = [{
        name: '监测值',
        data: yData,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#409EFF'
        },
        lineStyle: {
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(64,158,255,0.2)'
            }, {
              offset: 1,
              color: 'rgba(64,158,255,0)'
            }]
          }
        }
      }];

      // 添加上限线和下限线
      if (!isNaN(maxRange)) {
        series.push({
          name: '上限值',
          data: new Array(xData.length).fill(maxRange),
          type: 'line',
          lineStyle: {
            color: '#FF0000',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          silent: true
        });
      }

      if (!isNaN(minRange)) {
        series.push({
          name: '下限值',
          data: new Array(xData.length).fill(minRange),
          type: 'line',
          lineStyle: {
            color: '#FF0000',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          silent: true
        });
      }

      option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = `${params[0].name}<br/>`;
            params.forEach(item => {
              if (item.seriesName === '监测值') {
                result += `${item.marker}${item.seriesName}：${item.value}${unit}<br/>`;
              } else {
                result += `${item.marker}${item.seriesName}：${item.value}${unit}<br/>`;
              }
            });
            return result;
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        dataZoom: [{
          type: 'slider',
          start: 20,
          end: 80,
          height: 20,
          bottom: 10,
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          textStyle: {
            color: '#333',
            fontSize: 12
          },
          borderColor: '#ddd',
          fillerColor: 'rgba(64,158,255,0.2)',
          backgroundColor: 'rgba(47,69,84,0)',
          selectedDataBackground: {
            lineStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: 'rgba(64,158,255,0.3)'
            }
          },
          labelFormatter: function(value, valueStr) {
            const index = Math.round(value * (xData.length - 1) / 100);
            if (index >= 0 && index < xData.length) {
              const timeStr = xData[index];
              if (selectedTimeRange.value === '24h') {
                return timeStr.split(' ')[1] || timeStr;
              } else {
                const date = new Date(timeStr);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                return `${month}/${day} ${hour}:${minute}`;
              }
            }
            return valueStr;
          }
        }, {
          type: 'inside',
          start: 20,
          end: 80
        }],
        grid: {
          top: 50,
          left: '5%',
          right: '8%',
          bottom: 90,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: 30,
            formatter: (value) => {
              if (selectedTimeRange.value === '24h') {
                return value.split(' ')[1]; // 只显示时间部分
              } else {
                // 7天和30天显示月/日 时:分格式
                const date = new Date(value);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                return `${month}/${day} ${hour}:${minute}`;
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: `监测值(${unit})`,
          nameTextStyle: {
            padding: [0, 30, 0, 0]
          },
          min: (() => {
            if (!isNaN(minRange)) {
              const validYData = yData.filter(v => v !== null && v !== undefined);
              if (validYData.length > 0) {
                return Math.min(minRange - (maxRange - minRange) * 0.1, Math.min(...validYData) - 5);
              } else {
                return minRange - 5;
              }
            }
            return undefined;
          })(),
          max: (() => {
            if (!isNaN(maxRange)) {
              const validYData = yData.filter(v => v !== null && v !== undefined);
              if (validYData.length > 0) {
                return Math.max(maxRange + (maxRange - minRange) * 0.1, Math.max(...validYData) + 5);
              } else {
                return maxRange + 5;
              }
            }
            return undefined;
          })(),
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series
      };
    } else {
      // 状态型指标 (type === 0)
      const statusData = validData.map(item => {
        const value = parseInt(item[fieldName]);
        return isNaN(value) ? null : value;
      });

      // 分别处理正常和异常状态的数据
      const normalData = statusData.map(value => value === 1 ? 1 : null);
      const abnormalData = statusData.map(value => value === 0 ? 1 : null);

      option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const dataIndex = params[0].dataIndex;
            const status = statusData[dataIndex];
            const statusText = status === 0 ? '正常' : status === 1 ? '异常' : '未知';
            return `${params[0].name}<br/>状态：${statusText}`;
          }
        },
        legend: {
          data: ['正常', '异常']
        },
        dataZoom: [{
          type: 'slider',
          start: 20,
          end: 80,
          height: 20,
          bottom: 10,
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          textStyle: {
            color: '#333',
            fontSize: 12
          },
          borderColor: '#ddd',
          fillerColor: 'rgba(64,158,255,0.2)',
          backgroundColor: 'rgba(47,69,84,0)',
          selectedDataBackground: {
            lineStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: 'rgba(64,158,255,0.3)'
            }
          },
          labelFormatter: function(value, valueStr) {
            const index = Math.round(value * (xData.length - 1) / 100);
            if (index >= 0 && index < xData.length) {
              const timeStr = xData[index];
              if (selectedTimeRange.value === '24h') {
                return timeStr.split(' ')[1] || timeStr;
              } else {
                const date = new Date(timeStr);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                return `${month}/${day} ${hour}:${minute}`;
              }
            }
            return valueStr;
          }
        }, {
          type: 'inside',
          start: 20,
          end: 80
        }],
        grid: {
          top: 50,
          left: '3%',
          right: '4%',
          bottom: 90,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: 30,
            formatter: (value) => {
              if (selectedTimeRange.value === '24h') {
                return value.split(' ')[1]; // 只显示时间部分
              } else {
                // 7天和30天显示月/日 时:分格式
                const date = new Date(value);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hour = String(date.getHours()).padStart(2, '0');
                const minute = String(date.getMinutes()).padStart(2, '0');
                return `${month}/${day} ${hour}:${minute}`;
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '状态',
          min: 0,
          max: 2,
          interval: 1,
          axisLabel: {
            formatter: (value) => {
              if (value === 0) return '';
              if (value === 1) return '';
              return '';
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [{
          name: '正常',
          data: normalData,
          type: 'line',
          step: 'end',
          lineStyle: {
            color: '#409EFF',
            width: 4
          },
          symbol: 'none'
        }, {
          name: '异常',
          data: abnormalData,
          type: statusData.length === 1 && statusData[0] === 0 ? 'scatter' : 'line',
          step: 'end',
          lineStyle: {
            color: '#FF0000',
            width: 4
          },
          symbol: statusData.length === 1 && statusData[0] === 0 ? 'circle' : 'none',
          symbolSize: statusData.length === 1 && statusData[0] === 0 ? 8 : 0
        }]
      };
    }

    chart.setOption(option);
    chart.resize();
  });
};

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  if (selectedIndicator.value) {
    fetchMonitorCurve(getTimeRange(value));
  }
};

// 处理指标变化
const handleIndicatorChange = (value) => {
  // 更新当前选中的指标信息
  const indicator = monitorIndicators.value.find(item => item.monitorField === value);
  if (indicator) {
    currentIndicatorInfo.value = indicator;
    // 重新获取监测曲线数据
    fetchMonitorCurve(getTimeRange(selectedTimeRange.value));
  }
};

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    '9101': 'alarm-level-first',
    '9102': 'alarm-level-second',
    '9103': 'alarm-level-third',
    '9104': 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};

// 监听窗口大小变化，重绘图表
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chart) {
    chart.dispose();
    chart = null;
  }
});
</script>

<style scoped>
.gas-leak-alert-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: calc(94vh - 120px);
    overflow-y: auto;
  }
}

.detail-container {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.detail-left {
  flex: 1;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 4px;
}

.detail-right {
  flex: 1;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 4px;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.item-label {
  width: 100px;
  color: #606266;
  font-size: 14px;
}

.item-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

.timeline-container {
  height: 400px;
  overflow-y: auto;
}

.curve-container {
  padding: 20px;
  min-height: 500px;
}

.curve-header {
  margin-bottom: 20px;
}

.curve-chart {
  height: 450px;
}

.record-container {
  padding: 20px;
  min-height: 400px;
}

/* 报警等级标签样式 */
.alarm-level-tag {
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  white-space: nowrap;
  flex: unset;
}

.alarm-level-first {
  background: rgba(255,0,0,0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.alarm-level-second {
  background: rgba(255,133,0,0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.alarm-level-third {
  background: rgba(255,211,0,0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style>