# 全局报警消息弹窗系统

## 功能概述

本系统实现了一个完整的全局报警消息弹窗功能，支持大屏端和管理端两种界面模式，包含WebSocket实时推送、消息管理、路由跳转和点位定位等功能。

## 核心组件

### 1. 报警数据存储 (AlarmStore)
**文件路径**: `src/stores/alarm.js`

**主要功能**:
- 全局报警消息列表管理
- 未读消息数量统计
- 消息状态管理（已读/未读）
- 按专项类型分组
- 消息增删查改操作

**主要方法**:
```javascript
// 添加报警消息
addAlarmMessage(message)

// 标记消息为已读
markAsRead(messageId)
markAllAsRead()

// 删除消息
removeMessage(messageId)
clearAllMessages()

// 显示/隐藏弹窗
openGlobalModal()
closeGlobalModal()
```

### 2. 全局报警弹窗组件 (GlobalAlarmModal)
**文件路径**: `src/components/GlobalAlarmModal.vue`

**主要功能**:
- 支持大屏和管理端两种样式模式
- 报警消息列表展示
- 智能路由跳转（根据专项类型和界面模式）
- 大屏端点位定位功能
- 消息状态管理和操作

**Props**:
```javascript
{
  mode: {
    type: String,
    default: 'screen',
    validator: (value) => ['screen', 'admin'].includes(value)
  }
}
```

### 3. 报警通知按钮 (AlarmNotificationButton)
**文件路径**: `src/components/AlarmNotificationButton.vue`

**主要功能**:
- 悬浮式报警通知按钮
- 未读消息数量显示
- 动画效果（脉冲、波纹、发光）
- 自定义位置样式
- 响应式设计

**Props**:
```javascript
{
  mode: {
    type: String,
    default: 'screen'
  },
  alwaysShow: {
    type: Boolean,
    default: false
  },
  positionClass: {
    type: String,
    default: ''
  }
}
```

### 4. WebSocket管理器 (WebSocketManager)
**文件路径**: `src/utils/websocket.js`

**主要功能**:
- WebSocket连接管理
- 自动重连机制
- 心跳检测
- 报警消息解析和分发
- 与AlarmStore集成

**使用方法**:
```javascript
import websocketManager from '@/utils/websocket'

// 初始化连接
websocketManager.init()

// 手动重连
websocketManager.reconnect()

// 关闭连接
websocketManager.close()
```

## 路由跳转规则

### 大屏端路由跳转
| 专项类型 | 目标路由 | 点位定位 |
|---------|---------|---------|
| gas | `/gas/monitoring` | ✓ |
| drainage | `/drainage/monitoring` | ✓ |
| heating | `/heating/monitoring` | ✓ |
| bridge | `/bridge` | ✗ (直接展示) |
| comprehensive | `/comprehensive/monitoring` | ✓ |

### 管理端路由跳转
| 专项类型 | 目标路由 |
|---------|---------|
| gas | `/gas/leak/monitor/alert` |
| drainage | `/drainage/monitoringMis/alarm/info` |
| heating | `/heating/monitoringMis/warning/network` |
| bridge | `/bridge/alarm/management/info` |
| comprehensive | `/comprehensive/public/service/alert` |

## 集成方式

### 1. 在布局组件中集成

**大屏布局** (`src/views/screen/index.vue`):
```vue
<template>
  <!-- 其他内容 -->
  
  <!-- 全局报警消息弹窗 -->
  <GlobalAlarmModal mode="screen" />
  
  <!-- 报警通知按钮 -->
  <AlarmNotificationButton mode="screen" />
</template>

<script setup>
import GlobalAlarmModal from '@/components/GlobalAlarmModal.vue'
import AlarmNotificationButton from '@/components/AlarmNotificationButton.vue'
</script>
```

**管理端布局** (`src/views/admin/index.vue`):
```vue
<template>
  <!-- 其他内容 -->
  
  <!-- 全局报警消息弹窗 -->
  <GlobalAlarmModal mode="admin" />
  
  <!-- 报警通知按钮 -->
  <AlarmNotificationButton mode="admin" />
</template>

<script setup>
import GlobalAlarmModal from '@/components/GlobalAlarmModal.vue'
import AlarmNotificationButton from '@/components/AlarmNotificationButton.vue'
</script>
```

### 2. 初始化WebSocket连接

**主应用入口** (`src/main.js`):
```javascript
import websocketManager from '@/utils/websocket'

// 初始化WebSocket连接
websocketManager.init()
```

## API接口

### 全局报警消息相关接口
**文件路径**: `src/api/comprehensive.js`

```javascript
// 获取全局报警消息列表
getGlobalAlarmMessages(params)

// 获取最新报警消息
getLatestAlarmMessages(params)

// 标记消息为已读
markAlarmMessagesAsRead(messageIds)

// 获取报警消息统计
getAlarmMessageStatistics(params)
```

## 开发测试

### 测试工具
**文件路径**: `src/utils/alarmTest.js`

在开发环境下，系统会自动注册测试函数到 `window.alarmTest`:

```javascript
// 添加单个测试消息
window.alarmTest.addOne(index)

// 批量添加测试消息
window.alarmTest.addBatch(count, interval)

// 开始模拟推送
const stopPush = window.alarmTest.startPush(interval)

// 清空所有消息
window.alarmTest.clear()
```

### 使用示例
```javascript
// 添加一条测试报警
window.alarmTest.addOne(0)

// 批量添加3条消息，间隔1秒
window.alarmTest.addBatch(3, 1000)

// 开始每30秒推送一条消息
const stopPush = window.alarmTest.startPush(30000)

// 停止推送
stopPush()
```

## 样式定制

### 大屏模式样式特点
- 深色背景渐变
- 蓝色边框和高亮
- 科技感动画效果
- 半透明背景

### 管理端模式样式特点
- 白色背景
- 标准边框样式
- 商务风格设计
- 传统界面布局

### 自定义位置类
```css
.top-right { top: 30px; right: 30px; }
.top-left { top: 30px; left: 30px; }
.bottom-left { bottom: 30px; left: 30px; }
.center { top: 50%; left: 50%; transform: translate(-50%, -50%); }
```

## 消息数据格式

### WebSocket消息格式
```javascript
{
  eventType: 10,        // 事件类型（10表示报警）
  type: 'laserMethane', // 设备类型
  time: '2024-01-01T12:00:00Z', // 时间
  content: '气体浓度超标',       // 报警内容
  level: '1',           // 报警等级
  deviceId: 'LM_001',   // 设备ID
  value: '85.6 ppm'     // 监测值
}
```

### 存储消息格式
```javascript
{
  id: 'alarm_1704110400000_abc123',  // 唯一ID
  type: 'laserMethane',              // 设备类型
  time: '2024-01-01T12:00:00Z',      // 报警时间
  content: '气体浓度超标',            // 报警内容
  level: '1',                        // 报警等级
  deviceId: 'LM_001',                // 设备ID
  value: '85.6 ppm',                 // 监测值
  isRead: false,                     // 是否已读
  receivedAt: '2024-01-01T12:00:00Z', // 接收时间
  timestamp: 1704110400000           // 时间戳
}
```

## 注意事项

1. **WebSocket连接**: 确保WebSocket服务地址正确配置
2. **路由权限**: 确保用户有访问目标页面的权限
3. **消息限制**: 默认最多保存100条消息，超出会自动清理
4. **性能考虑**: 大量消息时注意内存使用
5. **兼容性**: 支持现代浏览器，IE需要polyfill

## 扩展功能

### 自定义专项类型映射
可以在 `AlarmStore` 中修改 `getSpecialTypeByDeviceType` 方法来添加新的设备类型映射。

### 自定义路由跳转逻辑
可以在 `GlobalAlarmModal` 中修改 `handleScreenNavigation` 和 `handleAdminNavigation` 方法来自定义跳转逻辑。

### 自定义样式主题
可以通过CSS变量或者添加新的模式来扩展样式主题。

## 故障排查

### 常见问题

1. **WebSocket连接失败**
   - 检查服务器地址和端口
   - 检查网络连接
   - 查看浏览器控制台错误信息

2. **消息不显示**
   - 检查AlarmStore是否正确初始化
   - 检查消息格式是否正确
   - 查看组件是否正确集成

3. **路由跳转失败**
   - 检查路由配置是否正确
   - 检查用户权限
   - 验证路由路径是否存在

4. **样式异常**
   - 检查CSS类名是否正确
   - 检查模式参数是否传递
   - 验证样式文件是否加载

### 调试方法

1. 使用浏览器开发者工具查看WebSocket连接状态
2. 使用Vue DevTools检查Pinia store状态
3. 使用测试工具模拟报警消息
4. 检查浏览器控制台的错误和警告信息