<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :before-close="handleClose" class="gas-adjacent-manhole-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="mode === 'view'">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井编码" prop="wellCode">
            <el-input v-model="formData.wellCode" placeholder="请输入窨井编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="窨井类型" prop="wellType">
            <el-select v-model="formData.wellType" placeholder="请选择" class="w-full">
              <el-option v-for="item in WELL_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井深 (m)" prop="wellDepth">
            <el-input v-model="formData.wellDepth" placeholder="请输入井深" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="窨井形状" prop="wellShape">
            <el-select v-model="formData.wellShape" placeholder="请选择" class="w-full">
              <el-option v-for="item in WELL_SHAPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="窨井材质" prop="wellMaterial">
            <el-select v-model="formData.wellMaterial" placeholder="请选择" class="w-full">
              <el-option v-for="item in WELL_MATERIALS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖尺寸" prop="wellSize">
            <el-input v-model="formData.wellSize" placeholder="请输入井盖尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井室规格" prop="wellRoomStandard">
            <el-input v-model="formData.wellRoomStandard" placeholder="请输入井室规格" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="特征点" prop="feature">
            <el-input v-model="formData.feature" placeholder="请输入特征点" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附属物" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属物" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋深 (m)" prop="buriedDepth">
            <el-input v-model="formData.buriedDepth" placeholder="请输入埋深" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="高程 (m)" prop="elevation">
            <el-input v-model="formData.elevation" placeholder="请输入高程" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联管线" prop="pipelineCode">
            <el-select v-model="formData.pipelineCode" placeholder="请选择" class="w-full" @change="handlePipelineChange">
              <el-option label="请选择" value="" />
              <el-option v-for="item in pipelineOptions" :key="item.id" :label="item.pipelineCode" :value="item.pipelineCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnitName" placeholder="请选择" class="w-full"  @change="handleManagementUnitChange">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设时间" prop="constructionTime">
            <el-date-picker v-model="formData.constructionTime" type="date" placeholder="请选择建设时间" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地理位置" prop="longitude">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"
                :disabled="mode === 'view'" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader v-model="formData.town" :options="areaOptions" :props="{
                value: 'code',
                label: 'name',
                children: 'children'
              }" placeholder="请选择所属区域" class="w-full" @change="handleAreaChange" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  saveSurroundingWell, 
  updateSurroundingWell, 
  getManagementUnits, 
  getGasPipelineList
} from '@/api/gas';
import { 
  WELL_TYPES, 
  WELL_TYPE_MAP, 
  WELL_SHAPES, 
  WELL_SHAPE_MAP, 
  WELL_MATERIALS, 
  WELL_MATERIAL_MAP, 
  AREA_OPTIONS 
} from '@/constants/gas';
import moment from 'moment';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增窨井',
    edit: '编辑窨井',
    view: '窨井详情'
  };
  return titles[props.mode] || '窨井信息';
});

// 表单数据
const formData = reactive({
  id: '',
  wellCode: '',
  wellType: '',
  wellTypeName: '',
  wellDepth: '',
  roadName: '',
  wellShape: '',
  wellShapeName: '',
  wellMaterial: '',
  wellMaterialName: '',
  wellSize: '',
  wellRoomStandard: '',
  feature: '',
  attachedFacilities: '',
  buriedDepth: '',
  elevation: '',
  pipelineId: '',
  pipelineCode: '',
  constructionTime: '',
  managementUnit: '',
  managementUnitName: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  wellCode: [{ required: true, message: '请输入窨井编码', trigger: 'blur' }],
  wellType: [{ required: true, message: '请选择窨井类型', trigger: 'change' }],
  wellDepth: [{ required: true, message: '请输入井深', trigger: 'blur' }],
  wellShape: [{ required: true, message: '请选择窨井形状', trigger: 'change' }],
  wellMaterial: [{ required: true, message: '请选择窨井材质', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }]
};

// 管线选项
const pipelineOptions = ref([]);

// 获取管线列表
const fetchPipelineList = async () => {
  try {
    const res = await getGasPipelineList({});
    if (res && res.code === 200) {
      pipelineOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

const handleManagementUnitChange = (value) => {
  formData.managementUnit = value;
  const selectedUnit = managementUnits.value.find(unit => unit.id === value);
  if (selectedUnit) {
    formData.managementUnitName = selectedUnit.enterpriseName;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 监听窨井类型变化
watch(() => formData.wellType, (newVal) => {
  if (newVal) {
    formData.wellTypeName = WELL_TYPE_MAP[newVal] || '';
  } else {
    formData.wellTypeName = '';
  }
}, { immediate: true });

// 监听窨井形状变化
watch(() => formData.wellShape, (newVal) => {
  if (newVal) {
    formData.wellShapeName = WELL_SHAPE_MAP[newVal] || '';
  } else {
    formData.wellShapeName = '';
  }
}, { immediate: true });

// 监听窨井材质变化
watch(() => formData.wellMaterial, (newVal) => {
  if (newVal) {
    formData.wellMaterialName = WELL_MATERIAL_MAP[newVal] || '';
  } else {
    formData.wellMaterialName = '';
  }
}, { immediate: true });

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        if (key === 'constructionTime' && typeof newVal[key] === 'object') {
          // 处理日期时间对象
          if (newVal[key] && newVal[key].time) {
            formData[key] = moment(newVal[key].time).format('YYYY-MM-DD');
          } else {
            formData[key] = '';
          }
        } else {
          formData[key] = newVal[key];
        }
      }
    });
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else if (key === 'constructionTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交数据
    const submitData = { ...formData };

    // 确保数值字段为数值类型
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);
    if (submitData.wellDepth) submitData.wellDepth = Number(submitData.wellDepth);
    if (submitData.buriedDepth) submitData.buriedDepth = Number(submitData.buriedDepth);
    if (submitData.elevation) submitData.elevation = Number(submitData.elevation);

    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveSurroundingWell(submitData);
    } else if (props.mode === 'edit') {
      res = await updateSurroundingWell(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 处理管线选择变化
const handlePipelineChange = (value) => {
  if (value) {
    const selectedPipeline = pipelineOptions.value.find(item => item.pipelineCode === value);
    if (selectedPipeline) {
      formData.pipelineId = selectedPipeline.id;
    }
  } else {
    formData.pipelineId = '';
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
  fetchPipelineList();
});
</script>

<style scoped>
.gas-adjacent-manhole-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}
</style>