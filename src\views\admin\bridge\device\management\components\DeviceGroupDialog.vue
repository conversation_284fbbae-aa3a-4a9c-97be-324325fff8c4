<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="device-group-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model="formData.groupName" placeholder="请输入分组名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分组类型" prop="groupType">
            <el-select v-model="formData.groupType" placeholder="请选择" class="w-full">
              <el-option v-for="item in groupTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属桥梁" prop="bridgeId">
            <el-select v-model="formData.bridgeId" placeholder="请选择" class="w-full" @change="handleBridgeChange">
              <el-option v-for="item in bridgeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="分组描述">
            <el-input v-model="formData.groupDesc" type="textarea" :rows="3" placeholder="请输入分组描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 关联设备区域 -->
      <div class="related-devices">
        <h3 class="section-title">关联设备</h3>
        
        <div class="device-selector" v-if="mode !== 'view'">
          <div class="selector-row">
            <div class="form-item">
              <span class="label">设备类型:</span>
              <el-select v-model="selectedDeviceType" placeholder="请选择" class="form-input" @change="handleDeviceTypeChange">
                <el-option v-for="item in deviceTypeOptions" :key="item.deviceType" :label="item.deviceTypeName" :value="item.deviceType" />
              </el-select>
            </div>
            <div class="form-item">
              <span class="label">设备名称:</span>
              <el-select 
                v-model="selectedDevices" 
                placeholder="请选择" 
                class="form-input" 
                multiple
                filterable
                :filter-method="filterDevices"
              >
                <el-option 
                  v-for="item in filteredDeviceOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value"
                  :disabled="isDeviceSelected(item.value)"
                />
              </el-select>
            </div>
            <el-button type="primary" @click="addDevices" :disabled="selectedDevices.length === 0">添加</el-button>
          </div>
        </div>

        <!-- 设备列表 -->
        <div class="device-list">
          <el-table :data="deviceList" style="width: 100%" empty-text="暂无关联设备">
            <el-table-column label="序号" min-width="60">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="120" />
            <el-table-column prop="deviceTypeName" label="设备类型" min-width="100" />
            <el-table-column label="操作" min-width="80" v-if="mode !== 'view'">
              <template #default="{ row, $index }">
                <el-button type="primary" link @click="removeDevice($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveDeviceGroup,
  updateDeviceGroup,
  getDeviceGroupDeviceList,
  saveBatchDeviceToGroup,
  getBridgeBasicInfoList,
  getDeviceType,
  getPipelineInfoList,
  GROUP_TYPE_OPTIONS,
  deleteDeviceFromGroup
} from '@/api/bridge';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增设备分组',
    edit: '编辑设备分组',
    view: '设备分组详情'
  };
  return titles[props.mode] || '设备分组';
});

// 下拉选项数据
const groupTypeOptions = ref(GROUP_TYPE_OPTIONS);
const bridgeOptions = ref([]);
const deviceTypeOptions = ref([]);
const deviceOptions = ref([]);
const filteredDeviceOptions = ref([]);

// 设备选择相关
const selectedDeviceType = ref('');
const selectedDevices = ref([]);
const deviceList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  groupName: '',
  groupType: '',
  groupTypeName: '',
  bridgeId: '',
  groupDesc: '',
  createTime: '',
  updateTime: ''
});

// 表单验证规则
const formRules = {
  groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
  groupType: [{ required: true, message: '请选择分组类型', trigger: 'change' }],
  bridgeId: [{ required: true, message: '请选择所属桥梁', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  deviceList.value = [];
  selectedDeviceType.value = '';
  selectedDevices.value = [];
  filteredDeviceOptions.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, async (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 如果是编辑或查看模式，获取关联设备列表
    if ((props.mode === 'edit' || props.mode === 'view') && newVal.id) {
      await fetchGroupDevices(newVal.id);
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 获取桥梁列表
const fetchBridges = async () => {
  try {
    const res = await getBridgeBasicInfoList({});
    if (res && res.data) {
      bridgeOptions.value = res.data.map(item => ({
        label: item.bridgeName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error);
  }
};

// 获取设备类型列表
const fetchDeviceTypes = async () => {
  try {
    const res = await getDeviceType(0);
    if (res && res.data) {
      deviceTypeOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取设备类型列表失败', error);
  }
};

// 获取分组关联的设备列表
const fetchGroupDevices = async (groupId) => {
  try {
    const res = await getDeviceGroupDeviceList(groupId);
    if (res && res.data) {
      deviceList.value = res.data;
    }
  } catch (error) {
    console.error('获取分组设备列表失败', error);
  }
};

// 处理桥梁变化
const handleBridgeChange = (value) => {
  const selected = bridgeOptions.value.find(item => item.value === value);
  if (selected) {
    // 可以在这里添加根据桥梁过滤设备的逻辑
  }
};

// 处理设备类型变化
const handleDeviceTypeChange = async (value) => {
  if (value) {
    try {
      const res = await getPipelineInfoList({ deviceType: value });
      if (res && res.data) {
        deviceOptions.value = res.data.map(item => ({
          label: item.deviceName,
          value: item.id,
          deviceType: item.deviceType,
          deviceTypeName: item.deviceTypeName
        }));
        filteredDeviceOptions.value = [...deviceOptions.value];
      }
    } catch (error) {
      console.error('获取设备列表失败', error);
    }
  } else {
    deviceOptions.value = [];
    filteredDeviceOptions.value = [];
  }
  selectedDevices.value = [];
};

// 过滤设备
const filterDevices = (query) => {
  if (query) {
    filteredDeviceOptions.value = deviceOptions.value.filter(item =>
      item.label.toLowerCase().includes(query.toLowerCase())
    );
  } else {
    filteredDeviceOptions.value = [...deviceOptions.value];
  }
};

// 检查设备是否已选择
const isDeviceSelected = (deviceId) => {
  return deviceList.value.some(device => device.deviceId === deviceId);
};

// 添加设备到列表
const addDevices = () => {
  const newDevices = selectedDevices.value.map(deviceId => {
    const device = deviceOptions.value.find(item => item.value === deviceId);
    return {
      deviceId: deviceId,
      deviceName: device.label,
      deviceType: device.deviceType,
      deviceTypeName: device.deviceTypeName,
      groupId: formData.id || ''
    };
  }).filter(device => !isDeviceSelected(device.deviceId));

  deviceList.value.push(...newDevices);
  selectedDevices.value = [];
};

// 移除设备
const removeDevice = async (index) => {
  const device = deviceList.value[index];
  
  // 如果是编辑模式且设备有id，需要调用删除接口
  if (props.mode === 'edit' && device.id) {
    try {
      const res = await deleteDeviceFromGroup(device.id);
      if (res && res.code === 200) {
        deviceList.value.splice(index, 1);
        ElMessage.success('设备移除成功');
      } else {
        ElMessage.error('设备移除失败');
      }
    } catch (error) {
      console.error('删除设备失败:', error);
      ElMessage.error('设备移除失败');
    }
  } else {
    // 新增模式或未保存的设备，直接从列表中移除
    deviceList.value.splice(index, 1);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 更新分组类型名称
    const selectedGroupType = groupTypeOptions.value.find(item => item.value === formData.groupType);
    if (selectedGroupType) {
      formData.groupTypeName = selectedGroupType.label;
    }

    // 处理时间字段
    const submitData = { ...formData };
    if (submitData.createTime && typeof submitData.createTime === 'object') {
      submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (submitData.updateTime && typeof submitData.updateTime === 'object') {
      submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss');
    }

    let res;
    if (props.mode === 'add') {
      res = await saveDeviceGroup(submitData);
    } else if (props.mode === 'edit') {
      res = await updateDeviceGroup(submitData);
    }

    if (res && res.code === 200) {
      // 如果有设备需要关联，批量添加设备到分组
      if (deviceList.value.length > 0) {
        const groupId = props.mode === 'add' ? (res.data?.id || res.data) : formData.id;
        
        // 过滤出新增的设备（没有id或者id为空的设备）
        const newDevices = deviceList.value.filter(device => !device.id || device.id === '');
        
        if (newDevices.length > 0) {
          const devicesData = newDevices.map(device => ({
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            deviceType: device.deviceType,
            deviceTypeName: device.deviceTypeName,
            groupId: groupId
          }));
          
          await saveBatchDeviceToGroup(devicesData);
        }
      }

      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.message || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
    ElMessage.error('操作失败，请重试');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchBridges();
  fetchDeviceTypes();
});
</script>

<style scoped>
.device-group-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.related-devices {
  margin-top: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
  color: #333;
}

.device-selector {
  margin-bottom: 16px;
}

.selector-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 200px;
  height: 32px;
}

:deep(.form-input .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.device-list {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}
</style> 