import request from '@/utils/request'

/**
 * 燃气专项监测设备撒点数据
 */
export function postUsmMonitorDeviceList(data={}) {
    return request({
        url: "/gas/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 燃气监测设备详情
 */
export function getUsmMonitorDeviceInfo(id) {
    return request({
        url: `/gas/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 燃气监测设备指标枚举
 */
export function getGasUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/gas/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 燃气监测设备曲线数据
 */
export function getGasUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/gas/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 燃气监测设备报警信息详情
 */
export function getGasUsmMonitorAlarmInfo(id) {
    return request({
        url: `/gas/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取燃气监测设备报警状态记录列表
export function postGasAlarmStatusList(data) {
    return request({
        url: '/gas/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 查询燃气设备各指标对应的报警三级阈值
 */
export function getGasUsmThresholdByDeviceId(deviceId) {
    return request({
        url: `/gas/usmAlarmThreshold/getThresholdByDeviceId?deviceId=${deviceId}`,
        method: "get",
    });
}

/**
 * 燃气监测设备指标对应的报警信息
 */
export function getGasDeviceAlarmInfoByDeviceId(deviceId) {
    return request({
        url: `/gas/usmMonitorAlarm/queryDeviceAlarmInfo/${deviceId}`,
        method: "get",
    });
}

/**
 * 燃气管线撒点数据
 */
export function postUsmZyGasPipelineList(data={}) {
    return request({
        url: "/gas/usmZyGasPipeline/list",
        method: "post",
        data,
    });
}

/**
 * 燃气管线详情
 */
export function getUsmZyGasPipelineInfo(id) {
    return request({
        url: `/gas/usmZyGasPipeline/${id}`,
        method: "get",
    });
}

/**
 * 燃气管线风险评估撒点数据
 */
export function postUsmZyGasRiskPipelineList(data={}) {
    return request({
        url: "/gas/usmRiskAssessmentPipeline/list",
        method: "post",
        data,
    });
}

/**
 * 燃气管线风险评估详情
 */
export function getUsmZyGasRiskPipelineInfo(id) {
    return request({
        url: `/gas/usmRiskAssessmentPipeline/${id}`,
        method: "get",
    });
}

/**
 * 燃气管点撒点数据
 */
export function postUsmZyGasPointList(data={}) {
    return request({
        url: "/gas/usmZyGasPoint/list",
        method: "post",
        data,
    });
}

/**
 * 燃气管点详情
 */
export function getUsmZyGasPointInfo(id) {
    return request({
        url: `/gas/usmZyGasPoint/${id}`,
        method: "get",
    });
}

/**
 * 燃气场站撒点数据
 */
export function postUsmZyGasStationList(data={}) {
    return request({
        url: "/gas/usmZyGasStation/list",
        method: "post",
        data,
    });
}

/**
 * 燃气场站详情
 */
export function getUsmZyGasStationInfo(id) {
    return request({
        url: `/gas/usmZyGasStation/${id}`,
        method: "get",
    });
}

/**
 * 燃气场站风险撒点数据
 */
export function postUsmZyGasRiskStationList(data={}) {
    return request({
        url: "/gas/usmRiskAssessmentStation/list",
        method: "post",
        data,
    });
}

/**
 * 燃气场站风险详情
 */
export function getUsmZyGasRiskStationInfo(id) {
    return request({
        url: `/gas/usmRiskAssessmentStation/${id}`,
        method: "get",
    });
}

/**
 * 燃气窨井撒点数据
 */
export function postUsmZyGasWellList(data={}) {
    return request({
        url: "/gas/usmZyGasWell/list",
        method: "post",
        data,
    });
}

/**
 * 燃气窨井详情
 */
export function getUsmZyGasWellInfo(id) {
    return request({
        url: `/gas/usmZyGasWell/${id}`,
        method: "get",
    });
}

/**
 * 燃气危险源撒点数据
 */
export function postUsmZyGasDangerList(data={}) {
    return request({
        url: "/gas/usmRiskDanger/list",
        method: "post",
        data,
    });
}

/**
 * 燃气危险源详情
 */
export function getUsmZyGasDangerInfo(id) {
    return request({
        url: `/gas/usmRiskDanger/${id}`,
        method: "get",
    });
}

/**
 * 燃气防护目标撒点数据
 */
export function postUsmZyGasProtectList(data={}) {
    return request({
        url: "/gas/usmRiskProtect/list",
        method: "post",
        data,
    });
}

/**
 * 燃气防护目标详情
 */
export function getUsmZyGasProtectInfo(id) {
    return request({
        url: `/gas/usmRiskProtect/${id}`,
        method: "get",
    });
}

/**
 * 排水专项监测设备撒点数据
 */
export function postDrainUsmMonitorDeviceList(data={}) {
    return request({
        url: "/drain/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 排水监测设备详情
 */
export function getDrainUsmMonitorDeviceInfo(id) {
    return request({
        url: `/drain/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 排水监测设备指标枚举
 */
export function getDrainUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/drain/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 排水监测设备曲线数据
 */
export function getDrainUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/drain/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 排水监测设备报警信息详情
 */
export function getDrainUsmMonitorAlarmInfo(id) {
    return request({
        url: `/drain/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取排水监测设备报警状态记录列表
export function postDrainAlarmStatusList(data) {
    return request({
        url: '/drain/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 查询排水设备各指标对应的报警三级阈值
 */
export function getDrainUsmThresholdByDeviceId(deviceId) {
    return request({
        url: `/drain/usmAlarmThreshold/getThresholdByDeviceId?deviceId=${deviceId}`,
        method: "get",
    });
}

/**
 * 排水监测设备指标对应的报警信息
 */
export function getDrainDeviceAlarmInfoByDeviceId(deviceId) {
    return request({
        url: `/drain/usmMonitorAlarm/queryDeviceAlarmInfo/${deviceId}`,
        method: "get",
    });
}

/**
 * 排水管线撒点数据
 */
export function postDrainUsmBasicPipelineList(data={}) {
    return request({
        url: "/drain/usmBasicPipeline/list",
        method: "post",
        data,
    });
}

/**
 * 排水管线详情
 */
export function getDrainUsmBasicPipelineInfo(id) {
    return request({
        url: `/drain/usmBasicPipeline/${id}`,
        method: "get",
    });
}

/**
 * 排水泵站撒点数据
 */
export function postUsmBasicPumpStationList(data={}) {
    return request({
        url: "/drain/usmBasicPumpStation/list",
        method: "post",
        data,
    });
}

/**
 * 排水泵站详情
 */
export function getUsmBasicPumpStationInfo(id) {
    return request({
        url: `/drain/usmBasicPumpStation/${id}`,
        method: "get",
    });
}

/**
 * 排水污水厂撒点数据
 */
export function postUsmBasicSewageFactoryList(data={}) {
    return request({
        url: "/drain/usmBasicSewageFactory/list",
        method: "post",
        data,
    });
}

/**
 * 排水污水厂详情
 */
export function getUsmBasicSewageFactoryInfo(id) {
    return request({
        url: `/drain/usmBasicSewageFactory/${id}`,
        method: "get",
    });
}

/**
 * 排水排水口撒点数据
 */
export function postUsmBasicDrainOutletList(data={}) {
    return request({
        url: "/drain/usmBasicDrainOutlet/list",
        method: "post",
        data,
    });
}

/**
 * 排水排水口详情
 */
export function getUsmBasicDrainOutletInfo(id) {
    return request({
        url: `/drain/usmBasicDrainOutlet/${id}`,
        method: "get",
    });
}

/**
 * 排水管点撒点数据
 */
export function postDrainUsmBasicPointList(data={}) {
    return request({
        url: "/drain/usmBasicPoint/list",
        method: "post",
        data,
    });
}

/**
 * 排水管点详情
 */
export function getDrainUsmBasicPointInfo(id) {
    return request({
        url: `/drain/usmBasicPoint/${id}`,
        method: "get",
    });
}

/**
 * 排水窨井撒点数据
 */
export function postDrainUsmBasicWellList(data={}) {
    return request({
        url: "/drain/usmBasicWell/list",
        method: "post",
        data,
    });
}

/**
 * 排水窨井详情
 */
export function getDrainUsmBasicWellInfo(id) {
    return request({
        url: `/drain/usmBasicWell/${id}`,
        method: "get",
    });
}

/**
 * 排水易涝点撒点数据
 */
export function postDrainUsmBasicFloodPointList(data={}) {
    return request({
        url: "/drain/usmBasicFloodPoint/list",
        method: "post",
        data,
    });
}

/**
 * 排水易涝点详情
 */
export function getDrainUsmBasicFloodPointInfo(id) {
    return request({
        url: `/drain/usmBasicFloodPoint/${id}`,
        method: "get",
    });
}

/**
 * 排水危险源撒点数据
 */
export function postDrainUsmRiskDangerList(data={}) {
    return request({
        url: "/drain/usmRiskDanger/list",
        method: "post",
        data,
    });
}

/**
 * 排水危险源详情
 */
export function getDrainUsmRiskDangerInfo(id) {
    return request({
        url: `/drain/usmRiskDanger/${id}`,
        method: "get",
    });
}

/**
 * 排水防护目标撒点数据
 */
export function postDrainUsmRiskProtectList(data={}) {
    return request({
        url: "/drain/usmRiskProtect/list",
        method: "post",
        data,
    });
}

/**
 * 排水防护目标详情
 */
export function getDrainUsmRiskProtectInfo(id) {
    return request({
        url: `/drain/usmRiskProtect/${id}`,
        method: "get",
    });
}

/**
 * 排水管网风险评估撒点数据
 */
export function postDrainUsmRiskPipelineList(data={}) {
    return request({
        url: "/drain/usmRiskAssessmentPipeline/list",
        method: "post",
        data,
    });
}

/**
 * 排水管网风险评估详情
 */
export function getDrainUsmRiskPipelineInfo(id) {
    return request({
        url: `/drain/usmRiskAssessmentPipeline/${id}`,
        method: "get",
    });
}

/**
 * 排水污水厂风险评估撒点数据
 */
export function postDrainUsmRiskFactoryList(data={}) {
    return request({
        url: "/drain/usmRiskAssessmentFactory/list",
        method: "post",
        data,
    });
}

/**
 * 排水污水厂风险评估详情
 */
export function getDrainUsmRiskFactoryInfo(id) {
    return request({
        url: `/drain/usmRiskAssessmentFactory/${id}`,
        method: "get",
    });
}

/**
 * 排水泵站风险评估撒点数据
 */
export function postDrainUsmRiskStationList(data={}) {
    return request({
        url: "/drain/usmRiskAssessmentStation/list",
        method: "post",
        data,
    });
}

/**
 * 排水泵站风险评估详情
 */
export function getDrainUsmRiskStationInfo(id) {
    return request({
        url: `/drain/usmRiskAssessmentStation/${id}`,
        method: "get",
    });
}

/**
 * 排水隐患信息撒点数据
 */
export function postDrainUsmRiskHiddenDangerList(data={}) {
    return request({
        url: "/drain/usmRiskHiddenDanger/list",
        method: "post",
        data,
    });
}

/**
 * 排水隐患信息详情
 */
export function getDrainUsmRiskHiddenDangerInfo(id) {
    return request({
        url: `/drain/usmRiskHiddenDanger/${id}`,
        method: "get",
    });
}

/**
 * 热力专项监测设备撒点数据
 */
export function postHeatUsmMonitorDeviceList(data={}) {
    return request({
        url: "/heat/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 热力监测设备详情
 */
export function getHeatUsmMonitorDeviceInfo(id) {
    return request({
        url: `/heat/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 热力监测设备指标枚举
 */
export function getHeatUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/heat/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 热力监测设备曲线数据
 */
export function getHeatUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/heat/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 供热监测设备报警信息详情
 */
export function getHeatUsmMonitorAlarmInfo(id) {
    return request({
        url: `/heat/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取供热监测设备报警状态记录列表
export function postHeatAlarmStatusList(data) {
    return request({
        url: '/heat/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 查询供热设备各指标对应的报警三级阈值
 */
export function getHeatUsmThresholdByDeviceId(deviceId) {
    return request({
        url: `/heat/usmAlarmThreshold/getThresholdByDeviceId?deviceId=${deviceId}`,
        method: "get",
    });
}

/**
 * 供热监测设备指标对应的报警信息
 */
export function getHeatDeviceAlarmInfoByDeviceId(deviceId) {
    return request({
        url: `/heat/usmMonitorAlarm/queryDeviceAlarmInfo/${deviceId}`,
        method: "get",
    });
}

/**
 * 供热管线撒点数据
 */
export function postHeatUsmBasicPipelineList(data={}) {
    return request({
        url: "/heat/usmBasicPipeline/list",
        method: "post",
        data,
    });
}

/**
 * 供热管线详情
 */
export function getHeatUsmBasicPipelineInfo(id) {
    return request({
        url: `/heat/usmBasicPipeline/${id}`,
        method: "get",
    });
}

/**
 * 供热管点撒点数据
 */
export function postHeatUsmBasicPointList(data={}) {
    return request({
        url: "/heat/usmBasicPoint/list",
        method: "post",
        data,
    });
}

/**
 * 供热管点详情
 */
export function getHeatUsmBasicPointInfo(id) {
    return request({
        url: `/heat/usmBasicPoint/${id}`,
        method: "get",
    });
}
/**
 * 供热窨井撒点数据
 */
export function postHeatUsmBasicWellList(data={}) {
    return request({
        url: "/heat/usmBasicWell/list",
        method: "post",
        data,
    });
}

/**
 * 供热窨井详情
 */
export function getHeatUsmBasicWellInfo(id) {
    return request({
        url: `/heat/usmBasicWell/${id}`,
        method: "get",
    });
}

/**
 * 供热企业撒点数据
 */
export function postHeatUsmBasicEnterpriseList(data={}) {
    return request({
        url: "/heat/usmBasicEnterprise/list",
        method: "post",
        data,
    });
}

/**
 * 供热企业详情
 */
export function getHeatUsmBasicEnterpriseInfo(id) {
    return request({
        url: `/heat/usmBasicEnterprise/${id}`,
        method: "get",
    });
}

/**
 * 供热热源厂撒点数据
 */
export function postHeatUsmBasicHeatFactoryList(data={}) {
    return request({
        url: "/heat/usmBasicHeatFactory/list",
        method: "post",
        data,
    });
}

/**
 * 供热热源厂详情
 */
export function getHeatUsmBasicHeatFactoryInfo(id) {
    return request({
        url: `/heat/usmBasicHeatFactory/${id}`,
        method: "get",
    });
}

/**
 * 供热换热站撒点数据
 */
export function postHeatUsmBasicHeatStationList(data={}) {
    return request({
        url: "/heat/usmBasicHeatStation/list",
        method: "post",
        data,
    });
}

/**
 * 供热换热站详情
 */
export function getHeatUsmBasicHeatStationInfo(id) {
    return request({
        url: `/heat/usmBasicHeatStation/${id}`,
        method: "get",
    });
}

/**
 * 供热用户撒点数据
 */
export function postHeatUsmBasicUserList(data={}) {
    return request({
        url: "/heat/usmBasicUser/list",
        method: "post",
        data,
    });
}

/**
 * 供热用户详情
 */
export function getHeatUsmBasicUserInfo(id) {
    return request({
        url: `/heat/usmBasicUser/${id}`,
        method: "get",
    });
}

/**
 * 供热危险源撒点数据
 */
export function postHeatUsmRiskDangerList(data={}) {
    return request({
        url: "/heat/usmRiskDanger/list",
        method: "post",
        data,
    });
}

/**
 * 供热危险源详情
 */
export function getHeatUsmRiskDangerInfo(id) {
    return request({
        url: `/heat/usmRiskDanger/${id}`,
        method: "get",
    });
}
/**
 * 供热防护目标撒点数据
 */
export function postHeatUsmRiskProtectList(data={}) {
    return request({
        url: "/heat/usmRiskProtect/list",
        method: "post",
        data,
    });
}

/**
 * 供热防护目标详情
 */
export function getHeatUsmRiskProtectInfo(id) {
    return request({
        url: `/heat/usmRiskProtect/${id}`,
        method: "get",
    });
}

/**
 * 供热管线风险评估撒点数据
 */
export function postHeatUsmRiskPipelineList(data={}) {
    return request({
        url: "/heat/usmRiskAssessmentPipeline/list",
        method: "post",
        data,
    });
}

/**
 * 供热管线风险评估详情
 */
export function getHeatUsmRiskPipelineInfo(id) {
    return request({
        url: `/heat/usmRiskAssessmentPipeline/${id}`,
        method: "get",
    });
}

/**
 * 供热热源厂风险评估撒点数据
 */
export function postHeatUsmRiskFactoryList(data={}) {
    return request({
        url: "/heat/usmRiskAssessmentFactory/list",
        method: "post",
        data,
    });
}

/**
 * 供热热源厂风险评估详情
 */
export function getHeatUsmRiskFactoryInfo(id) {
    return request({
        url: `/heat/usmRiskAssessmentFactory/${id}`,
        method: "get",
    });
}

/**
 * 供热换热站风险评估撒点数据
 */
export function postHeatUsmRiskStationList(data={}) {
    return request({
        url: "/heat/usmRiskAssessmentStation/list",
        method: "post",
        data,
    });
}

/**
 * 供热换热站风险评估详情
 */
export function getHeatUsmRiskStationInfo(id) {
    return request({
        url: `/heat/usmRiskAssessmentStation/${id}`,
        method: "get",
    });
}

/**
 * 供热隐患信息撒点数据
 */
export function postHeatUsmRiskHiddenDangerList(data={}) {
    return request({
        url: "/heat/usmRiskHiddenDanger/list",
        method: "post",
        data,
    });
}

/**
 * 供热隐患信息详情
 */
export function getHeatUsmRiskHiddenDangerInfo(id) {
    return request({
        url: `/heat/usmRiskHiddenDanger/${id}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备撒点数据
 * @param data
 * @returns {*}
 */
export function postBridgeMapDevicePointsList(data={}) {
    return request({
        url: "/bridge/api/v1/mapScatterPoints/devicePoints",
        method: "post",
        data,
    });
}

/**
 * 桥梁监测设备详情
 */
export function getBridgeUsmMonitorDeviceInfo(id) {
    return request({
        url: `/bridge/usmMonitorDevice/${id}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备指标枚举
 */
export function getBridgeUsmMonitorIndicatorsInfo(deviceId) {
    return request({
        url: `/bridge/usmMonitorRecord/monitorIndicators/${deviceId}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备曲线数据
 */
export function getBridgeUsmMonitorRecordMonitorCurve(data={}) {
    return request({
        url: "/bridge/usmMonitorRecord/monitorCurve",
        method: "post",
        data,
    });
}

/**
 * 桥梁监测设备报警信息详情
 */
export function getBridgeUsmMonitorAlarmInfo(id) {
    return request({
        url: `/bridge/usmMonitorAlarm/${id}`,
        method: "get",
    });
}

// 获取桥梁监测设备报警状态记录列表
export function postBridgeAlarmStatusList(data) {
    return request({
        url: '/bridge/usmMonitorAlarmStatus/list',
        method: 'post',
        data
    });
}

/**
 * 查询桥梁设备各指标对应的报警三级阈值
 */
export function getBridgeUsmThresholdByDeviceId(deviceId) {
    return request({
        url: `/bridge/usmAlarmThreshold/getThresholdByDeviceId?deviceId=${deviceId}`,
        method: "get",
    });
}

/**
 * 桥梁监测设备指标对应的报警信息
 */
export function getBridgeDeviceAlarmInfoByDeviceId(deviceId) {
    return request({
        url: `/bridge/usmMonitorAlarm/queryDeviceAlarmInfo/${deviceId}`,
        method: "get",
    });
}

/**
 * 桥梁基本信息撒点数据
 */
export function postBridgeUsmBridgeBasicList(data={}) {
    return request({
        url: "/bridge/usmBridgeBasicInfo/list",
        method: "post",
        data,
    });
}

/**
 * 桥梁基本信息详情
 */
export function getBridgeUsmBridgeBasicInfo(id) {
    return request({
        url: `/bridge/usmBridgeBasicInfo/${id}`,
        method: "get",
    });
}

/**
 * 通用视频设备撒点数据
 */
export function postUsmVideoStreamPointList(data={}) {
    return request({
        url: "/basic/usmVideoStream/queryStreamMapPoint",
        method: "post",
        data,
    });
}

/**
 * 获取视频流
 */
export function queryUsmVideoStreamHls(data={}) {
    return request({
        url: "/basic/usmVideoStream/queryStreamHls",
        method: "post",
        data,
    });
}

/**
 * 综合应急避难所撒点数据
 */
export function postComUsmEmergencyShelterList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencyShelter/list",
        method: "post",
        data,
    });
}

/**
 * 综合应急避难所信息详情
 */
export function getComUsmEmergencyShelterInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencyShelter/${id}`,
        method: "get",
    });
}

/**
 * 综合应急救援队撒点数据
 */
export function postComUsmEmergencyTeamList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencyTeam/list",
        method: "post",
        data,
    });
}

/**
 * 综合应急救援队信息详情
 */
export function getComUsmEmergencyTeamInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencyTeam/${id}`,
        method: "get",
    });
}

/**
 * 综合应急物资撒点数据
 */
export function postComUsmEmergencySuppliesList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencySupplies/list",
        method: "post",
        data,
    });
}

/**
 * 综合应急物资信息详情
 */
export function getComUsmEmergencySuppliesInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencySupplies/${id}`,
        method: "get",
    });
}

/**
 * 综合应急救援人员撒点数据
 */
export function postComUsmEmergencyRespondersList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencyResponders/list",
        method: "post",
        data,
    });
}

/**
 * 综合应急救援人员信息详情
 */
export function getComUsmEmergencyRespondersInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencyResponders/${id}`,
        method: "get",
    });
}

/**
 * 综合医疗机构撒点数据
 */
export function postComUsmEmergencyHospitalList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencyHospital/list",
        method: "post",
        data,
    });
}

/**
 * 综合医疗机构信息详情
 */
export function getComUsmEmergencyHospitalInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencyHospital/${id}`,
        method: "get",
    });
}

/**
 * 综合应急仓库撒点数据
 */
export function postComUsmEmergencyStoreList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencyStore/list",
        method: "post",
        data,
    });
}

/**
 * 综合应急仓库信息详情
 */
export function getComUsmEmergencyStoreInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencyStore/${id}`,
        method: "get",
    });
}

/**
 * 综合隐患撒点数据
 */
export function postComUsmRiskHiddenDangerList(data={}) {
    return request({
        url: "/comprehensive/usmRiskHiddenDanger/list",
        method: "post",
        data,
    });
}

/**
 * 综合隐患信息详情
 */
export function getComUsmRiskHiddenDangerInfo(id) {
    return request({
        url: `/comprehensive/usmRiskHiddenDanger/${id}`,
        method: "get",
    });
}

/**
 * 综合应急事件撒点数据
 */
export function postComUsmEmergencyEventList(data={}) {
    return request({
        url: "/comprehensive/usmEmergencyEvent/list",
        method: "post",
        data,
    });
}

/**
 * 综合应急事件信息详情
 */
export function getComUsmEmergencyEventInfo(id) {
    return request({
        url: `/comprehensive/usmEmergencyEvent/${id}`,
        method: "get",
    });
}

/**
 * 综合预警撒点数据
 */
export function postComUsmWarningList(data={}) {
    return request({
        url: "/comprehensive/usmWarningInfo/list",
        method: "post",
        data,
    });
}

/**
 * 综合预警信息详情
 */
export function getComUsmWarningInfo(id) {
    return request({
        url: `/comprehensive/usmWarningInfo/${id}`,
        method: "get",
    });
}



