<template>
  <el-dialog v-model="dialogVisible" title="设备选择" width="800px" :close-on-click-modal="false">
    <div class="device-select-container">
      <!-- 搜索框 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入设备名称进行搜索"
          prefix-icon="Search"
          clearable
          class="search-input"
        />
      </div>

      <!-- 设备列表 -->
      <div class="device-list">
        <el-table
          ref="tableRef"
          :data="filteredDeviceList"
          @selection-change="handleSelectionChange"
          height="400"
          row-key="id"
          class="device-table"
        >
          <el-table-column
            v-if="!readonly"
            type="selection"
            width="55"
            :reserve-selection="true"
          />
          <el-table-column
            v-else
            width="55"
            align="center"
          >
            <template #default="{ row }">
              <el-icon v-if="selectedDeviceIds.includes(row.id)" color="#409eff">
                <Check />
              </el-icon>
            </template>
          </el-table-column>
          
         <el-table-column prop="index" label="序号" width="80" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称" min-width="220" show-overflow-tooltip>
            <template #default="{ row }">
              <span style="white-space: pre-line">{{ row.deviceName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="indexCode" label="设备编码" min-width="120" show-overflow-tooltip />
          <el-table-column prop="deviceTypeName" label="设备类型" min-width="100" show-overflow-tooltip >
            <template #default="{ row }">
              <span style="white-space: pre-line">{{ row.deviceTypeName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="设备位置" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 已选择设备统计 -->
      <div class="selected-info">
        <span>已选择 {{ currentSelectedIds.length }} 个设备</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ readonly ? '关 闭' : '取 消' }}</el-button>
        <el-button v-if="!readonly" type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Check } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceList: {
    type: Array,
    default: () => []
  },
  selectedDeviceIds: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 表格引用
const tableRef = ref(null)

// 搜索关键词
const searchKeyword = ref('')

// 当前选中的设备IDs
const currentSelectedIds = ref([])

// 是否正在搜索过程中（防止搜索时触发selection-change导致选择被清空）
const isSearching = ref(false)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 筛选后的设备列表
const filteredDeviceList = computed(() => {
  if (!searchKeyword.value) {
    return props.deviceList
  }
  return props.deviceList.filter(device => 
    device.deviceName && device.deviceName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 处理选择变化
const handleSelectionChange = (selection) => {
  // 如果正在搜索过程中，忽略此次选择变化（避免搜索时自动清空选择）
  if (isSearching.value) {
    return
  }
  
  // 获取当前筛选列表中的设备IDs
  const filteredIds = filteredDeviceList.value.map(item => item.id)
  
  // 移除当前筛选列表中原本已选择的设备
  const remainingSelectedIds = currentSelectedIds.value.filter(id => !filteredIds.includes(id))
  
  // 添加当前表格中新选择的设备
  const newSelectedIds = selection.map(item => item.id)
  
  // 合并已选择的设备（保留不在当前筛选列表中的 + 当前表格选择的）
  currentSelectedIds.value = [...remainingSelectedIds, ...newSelectedIds]
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', currentSelectedIds.value)
  dialogVisible.value = false
}

// 取消选择
const handleCancel = () => {
  dialogVisible.value = false
}

// 监听搜索关键词变化，重新设置表格选中状态
watch(searchKeyword, async () => {
  if (props.visible && tableRef.value && !props.readonly) {
    // 设置搜索状态，防止selection-change事件影响已选择设备
    isSearching.value = true
    
    await nextTick();
    // 清除所有选中
    tableRef.value.clearSelection();
    // 重新设置选中状态 - 只对当前筛选列表中已选择的设备进行勾选
    filteredDeviceList.value.forEach(device => {
      if (currentSelectedIds.value.includes(device.id)) {
        tableRef.value.toggleRowSelection(device, true);
      }
    });
    
    // 搜索完成，恢复正常状态
    await nextTick()
    isSearching.value = false
  }
});

// 监听对话框打开，设置已选中的设备
watch(() => props.visible, (visible) => {
  if (visible) {
    // 重置搜索关键词
    searchKeyword.value = ''
    // 设置当前选中的设备
    currentSelectedIds.value = [...props.selectedDeviceIds]
    
    // 等待表格渲染完成后设置选中状态
    nextTick(() => {
      if (tableRef.value && !props.readonly) {
        // 清除所有选中状态
        tableRef.value.clearSelection()
        // 设置已选中的设备
        filteredDeviceList.value.forEach(device => {
          if (props.selectedDeviceIds.includes(device.id)) {
            tableRef.value.toggleRowSelection(device, true)
          }
        })
      }
    })
  }
})
</script>

<style scoped>
.device-select-container {
  padding: 10px 0;
}

.search-bar {
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.device-list {
  margin-bottom: 15px;
}

.selected-info {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.el-input__inner) {
  border-radius: 6px;
  height: 36px;
}
</style>