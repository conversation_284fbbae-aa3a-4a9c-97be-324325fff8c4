<template>
  <PanelBox title="隐患类型统计" class="right-middle-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <NoData v-if="currentData.length === 0 && !loading" class="no-data-wrapper" />
      <div v-else class="chart-container" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import NoData from '@/components/common/NoData.vue'
import * as echarts from 'echarts'
import { getHiddenDangerTypeStatistics } from '@/api/drainage.js'

// 时间选择
const timeRange = ref(7)
const timeOptions = [
  { label: '近一周', value: 7 },
  { label: '近一月', value: 30 },
  { label: '近一年', value: 365 }
]

// 加载状态和数据
const loading = ref(false)
const dangerTypeData = ref([])

// 雷达图实例
const chartRef = ref(null)
let chartInstance = null

// 当前数据
const currentData = ref([])

// 初始化雷达图
const initChart = () => {
  if (!chartRef.value) return

  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 确保容器已渲染完成并且有尺寸
  setTimeout(() => {
    chartInstance = echarts.init(chartRef.value)
    updateChartData()
    
    // 添加窗口大小变化的监听器
    window.addEventListener('resize', resizeChart)
  }, 100)  // 延迟100ms确保DOM完全渲染
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance) return

  // 提取所有指标名称
  const indicators = currentData.value.map(item => ({
    name: item.name,
    max: 50
  }))

  // 雷达图配置
  const option = {
    color: ['#0057FF', '#70FF00', '#AC42FF'],
    radar: {
      indicator: indicators,
      shape: 'polygon',
      center: ['50%', '40%'],
      radius: '60%',
      nameGap: 5,
      splitNumber: 5,
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(0, 87, 255, 0.05)', 'rgba(0, 87, 255, 0.1)', 'rgba(0, 87, 255, 0.2)', 'rgba(0, 87, 255, 0.3)']
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      name: {
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12,
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400
        }
      }
    },
    series: [
      {
        name: '隐患类型',
        type: 'radar',
        data: [
          {
            value: currentData.value.map(item => item.value),
            name: '隐患类型',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(0, 87, 255, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(112, 255, 0, 0.3)'
                }
              ])
            },
            lineStyle: {
              color: '#0057FF',
              width: 2
            },
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#0057FF',
              borderWidth: 2
            },
            symbolSize: 6
          },
          {
            value: currentData.value.map(item => item.value * 0.6),
            name: '整改情况',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(112, 255, 0, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(172, 66, 255, 0.3)'
                }
              ])
            },
            lineStyle: {
              color: '#70FF00',
              width: 2
            },
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#70FF00',
              borderWidth: 2
            },
            symbolSize: 6
          },
          {
            value: currentData.value.map(item => item.value * 0.3),
            name: '未整改',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(172, 66, 255, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(255, 99, 71, 0.3)'
                }
              ])
            },
            lineStyle: {
              color: '#AC42FF',
              width: 2
            },
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#AC42FF',
              borderWidth: 2
            },
            symbolSize: 6
          }
        ]
      }
    ]
  }

  chartInstance.setOption(option)
}

// 响应窗口大小变化
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 获取隐患类型统计数据
const fetchDangerTypeData = async () => {
  try {
    loading.value = true
    const response = await getHiddenDangerTypeStatistics(timeRange.value)
    
    if (response.code === 200 && response.data && Array.isArray(response.data)) {
      // 转换API数据格式为图表需要的格式
      dangerTypeData.value = response.data
      currentData.value = response.data.map(item => ({
        name: item.dangerTypeName,
        value: item.count
      }))
    } else {
      dangerTypeData.value = []
      currentData.value = []
    }
  } catch (error) {
    console.error('获取隐患类型统计数据失败:', error)
    dangerTypeData.value = []
    currentData.value = []
  } finally {
    loading.value = false
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  timeRange.value = value
  fetchDangerTypeData()
}

// 监听时间范围变化
watch(timeRange, () => {
  fetchDangerTypeData()
})

// 监听数据变化，更新图表
watch(currentData, () => {
  nextTick(() => {
    updateChartData()
  })
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  // 使用setTimeout确保DOM已经渲染完毕
  setTimeout(() => {
    initChart()
  }, 300)
  // 初始加载数据
  fetchDangerTypeData()
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.right-middle-panel {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.chart-container {
  width: 100%;
  height: 100%;
  flex: 1;
  min-height: 250px;  /* 添加最小高度确保DOM渲染时有尺寸 */
}

.com-select {
  margin-right: 20px;
}

.no-data-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 10px;
  }
  .chart-container {
    height: 340px;
  }
}

@media screen and (max-width: 1919px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 8px;
  }
  .chart-container {
    height: 300px;
  }
}

@media screen and (min-width: 2561px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
  }
  .chart-container {
    height: 380px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
  }
  .chart-container {
    height: 360px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .right-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 10px;
  }
  .chart-container {
    height: 320px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .right-middle-panel {
    height: 252px;
  }
  .panel-content {
    padding: 8px;
  }
  .chart-container {
    height: 280px;
  }
}
</style>