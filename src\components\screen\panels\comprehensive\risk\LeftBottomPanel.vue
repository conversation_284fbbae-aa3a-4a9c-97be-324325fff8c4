0<template>
  <PanelBox title="供热风险">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="risk-chart-container">
        <div ref="chartRef" class="echarts-container"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts/core'
import { BarChart, PictorialBarChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, TitleComponent } from 'echarts/components' 
import { CanvasRenderer } from 'echarts/renderers'
import { 
  getSituationPipelineRiskStatistics, 
  getSituationFactoryRiskStatistics, 
  getSituationStationRiskStatistics 
} from '@/api/heating'

// 注册必须的组件
echarts.use([
  BarChart,
  PictorialBarChart,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  CanvasRenderer
])

// 综合态势总览左下面板组件

// 管线类型选项 - 与 左侧面板保持一致
const pipeTypeOptions = [
  { value: 'rain', label: '管线' },
  { value: 'plant', label: '热源厂' },
  { value: 'station', label: '换热站' }
]

// 默认选择管线
const selectedPipeType = ref('rain')

// 风险数据
const riskData = ref({
  rain: [
    { type: '重大风险', count: 0, value: 0 },
    { type: '较大风险', count: 0, value: 0 },
    { type: '一般风险', count: 0, value: 0 },
    { type: '低风险', count: 0, value: 0 }
  ],
  plant: [
    { type: '重大风险', count: 0, value: 0 },
    { type: '较大风险', count: 0, value: 0 },
    { type: '一般风险', count: 0, value: 0 },
    { type: '低风险', count: 0, value: 0 }
  ],
  station: [
    { type: '重大风险', count: 0, value: 0 },
    { type: '较大风险', count: 0, value: 0 },
    { type: '一般风险', count: 0, value: 0 },
    { type: '低风险', count: 0, value: 0 }
  ]
})

// 当前显示的风险数据
const currentRiskData = computed(() => {
  return riskData.value[selectedPipeType.value] || []
})

// 图表DOM引用
const chartRef = ref(null)
// 图表实例
let chartInstance = null
// 图表初始化状态
let chartInitialized = false

// 获取风险等级对应的颜色配置
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return { top: '#DE0101', bottom: 'rgba(222, 1, 1, 0.1)' }
    case '较大风险':
      return { top: '#DE5C01', bottom: 'rgba(222, 92, 1, 0.1)' }
    case '一般风险':
      return { top: '#DEA801', bottom: 'rgba(222, 168, 1, 0.1)' }
    case '低风险':
      return { top: '#0138DE', bottom: 'rgba(1, 56, 222, 0.1)' }
    default:
      return { top: '#0138DE', bottom: 'rgba(1, 56, 222, 0.1)' }
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 检查DOM元素是否有尺寸
  const container = chartRef.value
  if (container.clientWidth === 0 || container.clientHeight === 0) {
    // 如果DOM元素没有尺寸，延迟初始化
    setTimeout(initChart, 100)
    return
  }

  try {
    // 如果图表已经初始化，先销毁
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)
    chartInitialized = true

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', resizeChart)

    // 更新图表数据
    updateChartData()
  } catch (error) {
    console.error('图表初始化错误:', error)
    // 出错时，尝试重新初始化
    setTimeout(() => {
      if (chartRef.value) {
        initChart()
      }
    }, 500)
  }
}

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    try {
      chartInstance.resize({
        width: 'auto',
        height: 'auto'
      })
    } catch (error) {
      console.error('图表调整大小错误:', error)
    }
  }
}

// 强制重新初始化图表
const forceReinitChart = () => {
  if (chartInstance) {
    try {
      chartInstance.dispose()
      chartInstance = null
      chartInitialized = false
    } catch (error) {
      console.error('图表销毁错误:', error)
    }
  }
  
  // 延迟重新初始化，确保DOM已更新
  setTimeout(() => {
    if (chartRef.value) {
      initChart()
    }
  }, 300)
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance) return

  const data = currentRiskData.value

  // 准备数据
  const xAxisData = []
  const seriesData = []
  const barTopColor = []
  const barBottomColor = []
  let sum = 0

  // 处理数据
  data.forEach(item => {
    xAxisData.push(item.type)
    seriesData.push(item.count)
    sum += item.count

    const colors = getRiskColor(item.type)
    barTopColor.push(colors.top)
    barBottomColor.push(colors.bottom)
  })

  // 设置图表配置
  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '25%',
      bottom: '28%',
      left: '8%',
      right: '8%'
    },
    xAxis: {
      data: xAxisData,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: true,
        margin: 20,
        align: 'center',
        formatter: function (params, index) {
          const percentage = sum > 0 ? (((seriesData[index] || 0) / sum) * 100) : 0;
          return '{a|' + percentage.toFixed(0) + '%}' + '\n' + '{b|' + params + '}';
        },
        fontSize: 12,
        color: '#ffffff',
        rich: {
          a: {
            fontSize: 12,
            color: '#ffffff'
          },
          b: {
            height: 18,
            fontSize: 12,
            color: '#ffffff',
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: 400,
            lineHeight: 17
          }
        }
      },
      interval: 0
    },
    yAxis: {
      name: '单位（公里）',
      nameTextStyle: {
        fontSize: 12,
        color: '#ffffff'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.1)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: true
      },
      axisLabel: {
        show: true,
        color: '#FFFFFF',
        fontSize: 12,
      }
    },
    series: [{
      name: '柱顶部',
      type: 'pictorialBar',
      symbolSize: [26, 10],
      symbolOffset: [0, -5],
      z: 12,
      itemStyle: {
        color: function (params) {
          return barTopColor[params.dataIndex];
        }
      },
      label: {
        show: true,
        position: 'top',
        fontSize: 12,
        color: '#FFFFFF',
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      },
      symbolPosition: 'end',
      data: seriesData,
    }, {
      name: '柱底部',
      type: 'pictorialBar',
      symbolSize: [26, 10],
      symbolOffset: [0, 5],
      z: 12,
      itemStyle: {
        color: function (params) {
          return barTopColor[params.dataIndex];
        }
      },
      data: seriesData
    }, {
      name: '第一圈',
      type: 'pictorialBar',
      symbolSize: [40, 13],
      symbolOffset: [0, 11],
      z: 11,
      itemStyle: {
        color: 'transparent',
        borderColor: function (params) {
          return barTopColor[params.dataIndex];
        },
        borderWidth: 2
      },
      data: seriesData
    }, {
      name: '第二圈',
      type: 'pictorialBar',
      symbolSize: [50, 16],
      symbolOffset: [0, 17],
      z: 10,
      itemStyle: {
        color: 'transparent',
        borderColor: function (params) {
          return barTopColor[params.dataIndex];
        },
        borderWidth: 2
      },
      data: seriesData
    }, {
      type: 'bar',
      itemStyle: {
        color: function (params) {
          return new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [{
              offset: 0,
              color: barTopColor[params.dataIndex]
            },
            {
              offset: 1,
              color: barBottomColor[params.dataIndex]
            }
            ]
          );
        },
        opacity: 0.8
      },
      z: 16,
      silent: true,
      barWidth: 26,
      barGap: '-100%', // Make series be overlap
      data: seriesData
    }]
  }

  // 设置图表选项
  chartInstance.setOption(option)
}

// 处理管线类型变化
const handlePipeTypeChange = () => {
  console.log('供热风险管线类型变更为:', selectedPipeType.value)
  // 这里可以调用接口获取对应类型的风险数据
  fetchRiskData(selectedPipeType.value)

  // 更新图表数据
  nextTick(() => {
    updateChartData()
  })
}

// 从后端获取数据的方法
const fetchRiskData = async (pipeType) => {
  try {
    let response
    
    // 根据管线类型调用不同的API
    switch (pipeType) {
      case 'rain': // 管线
        response = await getSituationPipelineRiskStatistics()
        break
      case 'plant': // 热源厂
        response = await getSituationFactoryRiskStatistics()
        break
      case 'station': // 换热站
        response = await getSituationStationRiskStatistics()
        break
      default:
        console.warn('未知的管线类型:', pipeType)
        return
    }

    if (response.code === 200 && response.data && response.data.riskLevelStatistics) {
      // 处理API返回的数据
      const apiData = response.data.riskLevelStatistics
      
      // 初始化默认风险等级数据
      const defaultRiskLevels = [
        { type: '重大风险', count: 0, value: 0 },
        { type: '较大风险', count: 0, value: 0 },
        { type: '一般风险', count: 0, value: 0 },
        { type: '低风险', count: 0, value: 0 }
      ]
      
      // 将API数据映射到默认结构中
      apiData.forEach(item => {
        const riskLevel = defaultRiskLevels.find(level => level.type === item.name)
        if (riskLevel) {
          // 管线类型使用length字段，其他类型使用count字段
          const value = pipeType === 'rain' ? (item.length || 0) : (item.count || 0)
          riskLevel.count = value
          riskLevel.value = value
        }
      })
      
      // 更新响应式数据
      riskData.value[pipeType] = defaultRiskLevels
      
      // 更新图表数据
      if (chartInstance) {
        updateChartData()
      }
    }
  } catch (error) {
    console.error('获取风险数据失败:', error)
  }
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchRiskData(newValue)

  // 更新图表数据
  nextTick(() => {
    updateChartData()
  })
})

// 确保在窗口加载完成后初始化图表
const initChartWithRetry = () => {
  // 在窗口加载事件中确保DOM已完全渲染
  if (typeof window !== 'undefined') {
    if (document.readyState === 'complete') {
      nextTick(() => initChart())
    } else {
      window.addEventListener('load', () => {
        nextTick(() => initChart())
      })
    }
  }
}

onMounted(() => {
  // 初始化时获取数据
  fetchRiskData(selectedPipeType.value)

  // 使用nextTick等待DOM更新
  nextTick(() => {
    // 等待一段时间确保DOM完全渲染
    setTimeout(() => {
      initChart()
    }, 300)
  })

  // 备份方案：如果图表仍未初始化，重试
  setTimeout(() => {
    if (!chartInitialized && chartRef.value) {
      initChart()
    }
  }, 1000)

  // 监听窗口大小变化，重新初始化图表
  const handleWindowResize = () => {
    clearTimeout(window.resizeTimer)
    window.resizeTimer = setTimeout(() => {
      forceReinitChart()
    }, 300)
  }

  window.addEventListener('resize', handleWindowResize)

  // 组件卸载时清理
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleWindowResize)
    
    // 销毁图表实例
    if (chartInstance) {
      window.removeEventListener('resize', resizeChart)
      chartInstance.dispose()
      chartInstance = null
      chartInitialized = false
    }
  })
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.risk-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.echarts-container {
  width: 100%;
  height: 100%;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .echarts-container {
    width: 100%;
    height: 280px;
  }
}

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
  
  .echarts-container {
    width: 100%;
    height: 240px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
  }
  
  .echarts-container {
    width: 100%;
    height: 220px;
  }
}
</style>