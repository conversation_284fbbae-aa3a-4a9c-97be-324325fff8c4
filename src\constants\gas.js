/**
 * 燃气管线相关常量定义
 */

/**
 * 压力级别
 * @type {Array<{label: string, value: number}>}
 */
export const PRESSURE_LEVELS = [
  { label: '高压', value: 1003 },
  { label: '中压', value: 1002 },
  { label: '低压', value: 1001 }
];

/**
 * 埋设类型
 * @type {Array<{label: string, value: number}>}
 */
export const BURIED_TYPES = [
  { label: '地埋式铺设', value: 2001 },
  { label: '架空铺设', value: 2002 },
  { label: '水平定向钻铺设', value: 2003 },
  { label: '管道隧道铺设', value: 2004 }
];

/**
 * 管线材质
 * @type {Array<{label: string, value: number}>}
 */
export const PIPE_MATERIALS = [
  { label: '钢管', value: 4001 },
  { label: 'PE管', value: 4002 },
  { label: 'PVC管', value: 4003 },
  { label: '不锈钢管', value: 4004 },
  { label: '铸铁管', value: 4005 },
  { label: '其他', value: 4006 }
];

/**
 * 管线线型
 * @type {Array<{label: string, value: number}>}
 */
export const LINE_TYPES = [
  { label: '实线', value: 3001 },
  { label: '虚线', value: 3002 },
  { label: '点线', value: 3003 },
  { label: '双线', value: 3004 },
  { label: '箭头线', value: 3005 },
  { label: '折线', value: 3006 },
  { label: '曲线', value: 3007 }
];

/**
 * 使用状态
 * @type {Array<{label: string, value: number}>}
 */
export const USAGE_STATUS = [
  { label: '未使用', value: 5001 },
  { label: '使用中', value: 5002 },
  { label: '废弃', value: 5003 }
];

/**
 * 压力级别名称映射
 */
export const PRESSURE_LEVEL_MAP = {
  1003: '高压',
  1002: '中压',
  1001: '低压'
};

/**
 * 埋设类型名称映射
 */
export const BURIED_TYPE_MAP = {
  2001: '地埋式铺设',
  2002: '架空铺设',
  2003: '水平定向钻铺设',
  2004: '管道隧道铺设'
};

/**
 * 材质名称映射
 */
export const MATERIAL_MAP = {
  4001: '钢管',
  4002: 'PE管',
  4003: 'PVC管',
  4004: '不锈钢管',
  4005: '铸铁管',
  4006: '其他'
};

/**
 * 使用状态名称映射
 */
export const USAGE_STATUS_MAP = {
  5001: '未使用',
  5002: '使用中',
  5003: '废弃'
};

/**
 * 场站类型
 * @type {Array<{label: string, value: number}>}
 */
export const STATION_TYPES = [
  { label: '门站', value: 7001 },
  { label: '调压站', value: 7002 },
  { label: '分输站', value: 7003 },
  { label: '储配站', value: 7004 },
  { label: '充装站', value: 7005 }
];

/**
 * 场站类型名称映射
 */
export const STATION_TYPE_MAP = {
  7001: '门站',
  7002: '调压站',
  7003: '分输站',
  7004: '储配站',
  7005: '充装站'
};

/**
 * 储罐类型
 * @type {Array<{label: string, value: number}>}
 */
export const TANK_TYPES = [
  { label: '液化石油气(LPG)储罐', value: 7101 },
  { label: '液化天然气(LNG)储罐', value: 7102 },
  { label: '压缩天然气(CNG)储罐', value: 7103 },
];

/**
 * 储罐类型名称映射
 */
export const TANK_TYPE_MAP = {
  7101: '液化石油气(LPG)储罐',
  7102: '液化天然气(LNG)储罐',
  7103: '压缩天然气(CNG)储罐',
};

/**
 * 监测设备相关枚举
 */

// 监测状态
export const MONITOR_STATUS = {
  ONLINE: 9001,  // 在线
  OFFLINE: 9002  // 离线
};

export const MONITOR_STATUS_MAP = {
  [MONITOR_STATUS.ONLINE]: '在线',
  [MONITOR_STATUS.OFFLINE]: '离线'
};

// 报警级别
export const ALARM_LEVEL = {
  LEVEL_ONE: 9101,    // 一级
  LEVEL_TWO: 9102,    // 二级
  LEVEL_THREE: 9103,  // 三级
  LEVEL_FOUR: 9104    // 四级
};

export const ALARM_LEVEL_MAP = {
  [ALARM_LEVEL.LEVEL_ONE]: '一级',
  [ALARM_LEVEL.LEVEL_TWO]: '二级',
  [ALARM_LEVEL.LEVEL_THREE]: '三级',
  [ALARM_LEVEL.LEVEL_FOUR]: '四级'
};

// 报警状态
export const ALARM_STATUS = {
  PENDING_CONFIRM: 9201,  // 待确认
  FALSE_ALARM: 9202,      // 误报
  PENDING_HANDLE: 9203,   // 待处置
  HANDLING: 9204,         // 处置中
  HANDLED: 9205,          // 已处置
  ARCHIVED: 9206          // 已归档
};

export const ALARM_STATUS_MAP = {
  [ALARM_STATUS.PENDING_CONFIRM]: '待确认',
  [ALARM_STATUS.FALSE_ALARM]: '误报',
  [ALARM_STATUS.PENDING_HANDLE]: '待处置',
  [ALARM_STATUS.HANDLING]: '处置中',
  [ALARM_STATUS.HANDLED]: '已处置',
  [ALARM_STATUS.ARCHIVED]: '已归档'
};

/**
 * 管径枚举
 * @type {Array<{label: string, value: number}>}
 */
export const PIPE_DIAMETERS = [
  { label: 'DN50', value: 40050 },
  { label: 'DN80', value: 40150 },
  { label: 'DN100', value: 40200 },
  { label: 'DN150', value: 40250 },
  { label: 'DN200', value: 40300 },
  { label: 'DN300', value: 40350 },
  { label: 'DN400', value: 40400 }
];

/**
 * 管径名称映射
 */
export const PIPE_DIAMETER_MAP = {
  40050: 'DN50',
  40150: 'DN80',
  40200: 'DN100',
  40250: 'DN150',
  40300: 'DN200',
  40350: 'DN300',
  40400: 'DN400'
};

/**
 * 行政区划
 * @type {Array<{code: string, name: string, children: Array<{code: string, name: string}>}>}
 */
export const AREA_OPTIONS = [
  {
    code: '371728',
    name: '东明县',
    children: [
      { code: '371728109000', name: '沙窝镇' },
      { code: '371728103000', name: '陆圈镇' },
      { code: '371728102000', name: '刘楼镇' },
      { code: '371728204000', name: '长兴集乡' },
      { code: '371728105000', name: '三春集镇' },
      { code: '371728104000', name: '马头镇' },
      { code: '371728106000', name: '大屯镇' },
      { code: '371728101000', name: '东明集镇' },
      { code: '371728110000', name: '小井镇' },
      { code: '371728107000', name: '武胜桥镇' },
      { code: '371728108000', name: '菜园集镇' },
      { code: '371728205000', name: '焦园乡' },
      { code: '371728001000', name: '城关街道办事处' },
      { code: '371728002000', name: '渔沃街道办事处' }
    ]
  }
];

/**
 * 管点相关枚举
 */

// 管点类型
export const POINT_TYPE = {
  VALVE: 6001,           // 阀门
  BEND: 6002,            // 弯头
  DIAMETER_CHANGE: 6003, // 变径点
  SLOPE_CHANGE: 6004,    // 变材点
  MULTI_WAY: 6005,       // 多通点
  BLIND_WELL: 6006,      // 良测点
  DETECTION: 6007,       // 探测点
  RESERVED: 6008,        // 预留口
  NON_SURVEY: 6009,      // 非普查区
  UP_CHANGE: 6010,       // 变深点
  OTHER: 6011            // 其他
};

export const POINT_TYPE_MAP = {
  [POINT_TYPE.VALVE]: '阀门',
  [POINT_TYPE.BEND]: '弯头',
  [POINT_TYPE.DIAMETER_CHANGE]: '变径点',
  [POINT_TYPE.SLOPE_CHANGE]: '变材点',
  [POINT_TYPE.MULTI_WAY]: '多通点',
  [POINT_TYPE.BLIND_WELL]: '良测点',
  [POINT_TYPE.DETECTION]: '探测点',
  [POINT_TYPE.RESERVED]: '预留口',
  [POINT_TYPE.NON_SURVEY]: '非普查区',
  [POINT_TYPE.UP_CHANGE]: '变深点',
  [POINT_TYPE.OTHER]: '其他'
};

/**
 * 风险评估相关枚举
 */

// 风险等级
export const RISK_LEVEL = {
  CRITICAL_RISK: 7001, // 重大风险
  MAJOR_RISK: 7002,   // 较大风险
  NORMAL_RISK: 7003,  // 一般风险
  LOW_RISK: 7004      // 低风险
};

export const RISK_LEVEL_MAP = {
  [RISK_LEVEL.CRITICAL_RISK]: '重大风险',
  [RISK_LEVEL.MAJOR_RISK]: '较大风险',
  [RISK_LEVEL.NORMAL_RISK]: '一般风险',
  [RISK_LEVEL.LOW_RISK]: '低风险'
};

// 管控状态
export const CONTROL_STATUS = {
  NO_CONTROL_NEEDED: 8001, // 无需管控
  UNCONTROLLED: 8002,      // 未管控
  CONTROLLED: 8003         // 已管控
};

export const CONTROL_STATUS_MAP = {
  [CONTROL_STATUS.NO_CONTROL_NEEDED]: '无需管控',
  [CONTROL_STATUS.UNCONTROLLED]: '未管控',
  [CONTROL_STATUS.CONTROLLED]: '已管控'
};

// 评估记录类型
export const ASSESSMENT_TYPE = {
  SYSTEM_ASSESSMENT: 0,  // 系统评估
  RISK_MODIFICATION: 1   // 风险修改
};

export const ASSESSMENT_TYPE_MAP = {
  [ASSESSMENT_TYPE.SYSTEM_ASSESSMENT]: '系统评估',
  [ASSESSMENT_TYPE.RISK_MODIFICATION]: '风险修改'
};

/**
 * 窨井相关常量
 */

// 窨井材质
export const WELL_MATERIALS = [
  { label: '铸铁', value: 9001 },
  { label: '钢铁', value: 9002 },
  { label: '复合材料', value: 9003 },
  { label: '混凝土', value: 9004 },
  { label: '不锈钢', value: 9005 },
  { label: '其他', value: 9006 }
];

export const WELL_MATERIAL_MAP = {
  9001: '铸铁',
  9002: '钢铁',
  9003: '复合材料',
  9004: '混凝土',
  9005: '不锈钢',
  9006: '其他'
};

// 窨井形状
export const WELL_SHAPES = [
  { label: '圆形', value: 9101 },
  { label: '方形', value: 9102 },
  { label: '其他', value: 9103 }
];

export const WELL_SHAPE_MAP = {
  9101: '圆形',
  9102: '方形',
  9103: '其他'
};

/**
 * 维修结果
 * @type {Array<{label: string, value: number}>}
 */
export const REPAIR_RESULTS = [
  { label: '未完成', value: 15001 },
  { label: '已完成', value: 15002 },
];

/**
 * 传感器设备相关常量
 */

// 传感器设备类型
export const DEVICE_TYPES = [
  { label: '固定点式激光甲烷气体监测仪', value: 'laserMethane' },
  { label: '万宾固定式激光甲烷气体监测仪', value: 'wbFixedCh4GrassDetector' },
  { label: '万宾地埋式液位计', value: 'wbUgLevelGauger' },
  { label: '井盖位移传感器', value: 'manholeCoverDevice' },
  { label: '万宾井盖位移传感器', value: 'wbManholeCoverSensor' },
  { label: '水质监测仪', value: 'waterQualityMonitor' },
  { label: '澳瑞德固定点式激光甲烷气体监测仪', value: 'aoruideMethane' }
];

export const DEVICE_TYPE_MAP = {
  'laserMethane': '固定点式激光甲烷气体监测仪',
  'wbFixedCh4GrassDetector': '万宾固定式激光甲烷气体监测仪',
  'wbUgLevelGauger': '万宾地埋式液位计',
  'manholeCoverDevice': '井盖位移传感器',
  'wbManholeCoverSensor': '万宾井盖位移传感器',
  'waterQualityMonitor': '水质监测仪',
  'aoruideMethane': '澳瑞德固定点式激光甲烷气体监测仪'
};

// 监测指标
export const MONITOR_INDEXES = [
  { label: '浓度', value: '10001' },
  { label: '电量', value: '10002' },
  { label: '信号', value: '10003' }
];

export const MONITOR_INDEX_MAP = {
  '10001': '浓度',
  '10002': '电量',
  '10003': '信号'
};

// 监测对象类型
export const MONITOR_OBJECT_TYPES = [
  { label: '管网', value: '11001' },
  { label: '场站', value: '12002' },
  { label: '窨井', value: '13003' }
];

export const MONITOR_OBJECT_TYPE_MAP = {
  '11001': '管网',
  '12002': '场站',
  '13003': '窨井'
};

// 监测设备在线状态
export const DEVICE_ONLINE_STATUS = [
  { label: '在线', value: 1 },
  { label: '离线', value: 0 }
];

export const DEVICE_ONLINE_STATUS_MAP = {
  1: '在线',
  0: '离线'
};

/**
 * 维修结果
 * @type {Array<{label: string, value: number}>}
 */
export const MANAGEMENT_UNITS = [
  { label: '未完成', value: 15001 },
  { label: '已完成', value: 15002 },
];

/**
 * 管理单位名称映射
 */
export const MANAGEMENT_UNIT_MAP = {
  15001: '未完成',
  15002: '已完成',
};

/**
 * 报警来源
 * @type {Array<{label: string, value: number}>}
 */
export const ALARM_SOURCES = [
  { label: '设备监测报警', value: '设备监测报警' },
  { label: '企业自报报警', value: '企业自报报警' },
  { label: '系统监测', value: '系统监测' }
];

/**
 * 报警来源名称映射
 */
export const ALARM_SOURCE_MAP = {
  '设备监测报警': '设备监测报警',
  '企业自报报警': '企业自报报警',
  '系统监测': '系统监测'
};

/**
 * 报警类型
 * @type {Array<{label: string, value: number}>}
 */
export const ALARM_TYPES = [
  { label: '燃气泄露报警', value: 9301 },
  { label: '压力异常报警', value: 9302 },
  { label: '流量异常报警', value: 9303 }
];

/**
 * 报警类型名称映射
 */
export const ALARM_TYPE_MAP = {
  9301: '燃气泄露报警',
  9302: '压力异常报警',
  9303: '流量异常报警'
};

/**
 * 监管部门
 * @type {Array<{label: string, value: string}>}
 */
export const SUPERVISE_DEPARTMENTS = [
  { label: '应急管理局', value: 'emergency' },
  { label: '城管局', value: 'cityManagement' },
  { label: '燃气管理处', value: 'gasManagement' },
  { label: '公安局', value: 'publicSecurity' },
  { label: '消防大队', value: 'fireDepartment' }
];

/**
 * 防护目标相关接口和常量
 */

// 是否重点防护目标
export const IS_KEY_PROTECTION_OPTIONS = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
];

// 是否重点防护目标映射
export const IS_KEY_PROTECTION_MAP = {
  '1': '是',
  '0': '否'
};

/**
 * 危险源相关接口和常量
 */

// 建筑类型常量
export const BUILDING_TYPE_OPTIONS = [
  { label: '学校', value: 6003501 },
  { label: '医院', value: 6003502 },
  { label: '重大基础设施/通讯、科技、体育、文化等社会事业', value: 6003503 },
  { label: '人员密集场所/如商场等', value: 6003504 },
  { label: '交通枢纽/如车站数据', value: 6003505 },
  { label: '其他', value: 6003506 }
];

// 建筑类型映射
export const BUILDING_TYPE_MAP = {
  6003501: '学校',
  6003502: '医院',
  6003503: '重大基础设施/通讯、科技、体育、文化等社会事业',
  6003504: '人员密集场所/如商场等',
  6003505: '交通枢纽/如车站数据',
  6003506: '其他'
};

/**
 * 报告类型
 * @type {Array<{label: string, value: number}>}
 */
export const REPORT_TYPES = [
  { label: '月报', value: 14001 },
  { label: '季报', value: 14002 },
  { label: '年报', value: 14003 }
];

/**
 * 报告类型名称映射
 */
export const REPORT_TYPE_MAP = {
  14001: '月报',
  14002: '季报',
  14003: '年报'
};

/**
 * 窨井类型
 * @type {Array<{label: string, value: number}>}
 */
export const WELL_TYPES = [
  { label: '雨水井', value: 15001 },
  { label: '污水井', value: 15002 },
  { label: '合流窨井', value: 15003 },
  { label: '通信井', value: 15004 },
  { label: '电力井', value: 15005 },
  { label: '热力井', value: 15006 },
];

/**
 * 窨井类型名称映射
 */
export const WELL_TYPE_MAP = {
  15001: '雨水井',
  15002: '污水井',
  15003: '合流窨井',
  15004: '通信井',
  15005: '电力井',
  15006: '热力井',
};

/**
 * 报警确认结果
 * @type {Array<{label: string, value: number}>}
 */
export const CONFIRM_RESULTS = [
  { label: '真实报警', value: 93001 },
  { label: '误报', value: 93002 }
];

/**
 * 报警确认结果名称映射
 */
export const CONFIRM_RESULT_MAP = {
  93001: '真实报警',
  93002: '误报'
};

/**
 * 处置状态
 * @type {Array<{label: string, value: number}>}
 */
export const HANDLE_STATUS = [
  { label: '处置中', value: 94001 },
  { label: '已处置', value: 94002 }
];

/**
 * 处置状态名称映射
 */
export const HANDLE_STATUS_MAP = {
  94001: '处置中',
  94002: '已处置'
};