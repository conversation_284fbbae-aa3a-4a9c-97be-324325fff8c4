<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="role-dialog"
  >
    <!-- 角色信息标签页 -->
    <el-tabs v-model="activeTab" v-if="mode === 'permission'">
      <el-tab-pane label="基础角色" name="basic">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          :disabled="true"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="角色名称:" >
                <span>{{ formData.name || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="描述:">
                <span>{{ formData.description || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="功能权限" name="function">
        <div class="permission-container">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTreeData"
            :props="{ children: 'children', label: 'title' }"
            show-checkbox
            node-key="id"
            :default-expand-all="false"
            :check-strictly="false"
            class="permission-tree"
          />
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="数据权限" name="data">
        <div class="data-permission-container">
          <el-form
            ref="dataFormRef"
            :model="dataFormData"
            label-width="120px"
          >
            <el-form-item label="数据范围" prop="dataScope">
              <el-radio-group v-model="dataFormData.dataScope" @change="handleDataScopeChange">
                <el-radio v-for="item in dataScopeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item v-if="dataFormData.dataScope === '40'" label="指定部门">
              <el-tree
                ref="deptTreeRef"
                :data="deptTreeData"
                :props="{ children: 'children', label: 'name' }"
                show-checkbox
                node-key="id"
                :default-expand-all="false"
                :check-strictly="true"
                class="dept-tree"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="关联用户" name="users">
        <div class="users-container">
          <el-table :data="relatedUsers" style="width: 100%" max-height="400">
            <el-table-column prop="name" label="姓名" width="100" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="phone" label="联系电话" width="120" />
            <el-table-column prop="deptName" label="所属单位" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === '0' ? 'success' : 'danger'">
                  {{ row.status === '0' ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 普通表单 -->
    <el-form
      v-else
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入角色名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input 
              v-model="formData.description" 
              type="textarea" 
              :rows="4" 
              placeholder="请输入描述内容" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'view'">
        <el-col :span="12">
          <el-form-item label="创建时间">
            <span>{{ formatDateTime(formData.createTime) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="更新时间">
            <span>{{ formatDateTime(formData.updateTime) }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button 
          v-if="mode === 'permission'"
          type="primary" 
          @click="handlePermissionSubmit"
        >
          确 定
        </el-button>
        <el-button 
          v-else-if="mode !== 'view'"
          type="primary" 
          @click="handleSubmit"
        >
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import {
  saveRole,
  updateRole,
  getPermissionTree,
  distributePermission,
  distributeDataScope,
  getDeptTree,
  getRolePermissions,
  searchUsers
} from '@/api/system'
import { DATA_SCOPE_OPTIONS } from '@/constants/system'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view', 'permission'
    validator: (value) => ['add', 'edit', 'view', 'permission'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 获取用户store
const userStore = useUserStore()

// 表单引用
const formRef = ref(null)
const dataFormRef = ref(null)
const permissionTreeRef = ref(null)
const deptTreeRef = ref(null)

// 当前激活的标签页
const activeTab = ref('basic')

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增角色',
    edit: '编辑角色',
    view: '查看角色',
    permission: '权限设置'
  }
  return titles[props.mode] || '角色信息'
})

// 数据范围选项
const dataScopeOptions = DATA_SCOPE_OPTIONS

// 权限树数据
const permissionTreeData = ref([])
// 部门树数据
const deptTreeData = ref([])
// 关联用户数据
const relatedUsers = ref([])

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  description: '',
  createTime: '',
  updateTime: '',
  state: '0'
})

// 数据权限表单数据
const dataFormData = reactive({
  dataScope: '10',
  depts: []
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'state') {
      formData[key] = '0'
    } else {
      formData[key] = ''
    }
  })
  
  dataFormData.dataScope = '10'
  dataFormData.depts = []
}

// 格式化时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 监听对话框打开，初始化数据
watch(() => props.visible, async (visible) => {
  if (visible) {
    // 确保用户信息存在
    if (!userStore.userInfo) {
      await userStore.getUserInfo()
    }
    
    if (props.mode === 'permission') {
      activeTab.value = 'basic'
      await loadPermissionData()
      await loadDeptData()
      await loadRelatedUsers()
      await loadRolePermissions()
    }
  }
})

// 加载权限树数据
const loadPermissionData = async () => {
  try {
    const res = await getPermissionTree()
    if (res && res.status === 200) {
      permissionTreeData.value = res.data || []
    }
  } catch (error) {
    console.error('获取权限树失败:', error)
  }
}

// 加载部门树数据
const loadDeptData = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200) {
      deptTreeData.value = res.data || []
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 加载关联用户
const loadRelatedUsers = async () => {
  if (!formData.id) return
  
  try {
    const res = await searchUsers({ roleId: formData.id })
    if (res && res.status === 200) {
      relatedUsers.value = res.data || []
    }
  } catch (error) {
    console.error('获取关联用户失败:', error)
  }
}

// 加载角色权限
const loadRolePermissions = async () => {
  if (!formData.id) return
  
  try {
    const res = await getRolePermissions(formData.id)
    if (res && res.status === 200) {
      const data = res.data || {}
      
      // 设置功能权限
      if (data.permissions && permissionTreeRef.value) {
        setTimeout(() => {
          permissionTreeRef.value.setCheckedKeys(data.permissions)
        }, 100)
      }
      
      // 设置数据权限
      if (data.dataScope) {
        dataFormData.dataScope = data.dataScope
        if (data.depts && data.dataScope === '40') {
          dataFormData.depts = data.depts
          setTimeout(() => {
            if (deptTreeRef.value) {
              deptTreeRef.value.setCheckedKeys(data.depts)
            }
          }, 100)
        }
      }
    }
  } catch (error) {
    console.error('获取角色权限失败:', error)
  }
}

// 处理数据范围变化
const handleDataScopeChange = (value) => {
  if (value !== '40' && deptTreeRef.value) {
    deptTreeRef.value.setCheckedKeys([])
    dataFormData.depts = []
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = {
      ...formData,
      createTime: formData.createTime ? moment(formData.createTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
      updateTime: formData.updateTime ? moment(formData.updateTime).format('YYYY-MM-DD HH:mm:ss') : undefined
    }

    // 新增角色时添加当前用户的deptId
    if (props.mode === 'add' && userStore.userInfo?.deptId) {
      submitData.deptId = userStore.userInfo.deptId
    }
    console.log('submitData==',submitData,'userStore.userInfo==',userStore.userInfo)
    let res
    if (props.mode === 'add') {
      res = await saveRole(submitData)
    } else if (props.mode === 'edit') {
      res = await updateRole(submitData)
    }

    if (res && res.status === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 提交权限设置
const handlePermissionSubmit = async () => {
  try {
    // 保存功能权限
    const checkedKeys = permissionTreeRef.value?.getCheckedKeys() || []
    const halfCheckedKeys = permissionTreeRef.value?.getHalfCheckedKeys() || []
    const permissions = [...checkedKeys, ...halfCheckedKeys]
    
    const permissionRes = await distributePermission({
      permissions,
      roleId: formData.id
    })
    
    if (permissionRes && permissionRes.status !== 200) {
      ElMessage.error('设置功能权限失败')
      return
    }
    
    // 保存数据权限
    let depts = []
    if (dataFormData.dataScope === '40' && deptTreeRef.value) {
      depts = deptTreeRef.value.getCheckedKeys() || []
    }
    
    const dataScopeRes = await distributeDataScope({
      dataScope: dataFormData.dataScope,
      depts,
      roleId: formData.id
    })
    
    if (dataScopeRes && dataScopeRes.status === 200) {
      ElMessage.success('权限设置成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error('设置数据权限失败')
    }
  } catch (error) {
    console.error('权限设置失败:', error)
    ElMessage.error('权限设置失败')
  }
}
</script>

<style scoped>
.role-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.permission-container,
.data-permission-container,
.users-container {
  max-height: 400px;
  overflow-y: auto;
}

.permission-tree,
.dept-tree {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-tabs__item.is-active) {
  color: #0277FD;
}

:deep(.el-tabs__active-bar) {
  background-color: #0277FD;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

:deep(.el-radio) {
  margin-right: 0;
  white-space: nowrap;
}
</style> 