<template>
  <div class="heating-statistics-container">
    <div class="page-header">
      <h1 class="page-title">基础数据统计分析</h1>
    </div>

    <!-- 查询条件 -->
    <div class="search-form">
      <el-form :model="searchForm" ref="searchFormRef" inline>
        <el-form-item label="所属企业：">
          <el-select 
            v-model="searchForm.managementUnit" 
            placeholder="请选择所属企业"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in enterpriseList"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域：">
          <el-select 
            v-model="searchForm.town" 
            placeholder="请选择所属区域"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in areaOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <div class="statistics-card">
        <div class="card-icon heat-source">
          <el-icon><Setting /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">热源</div>
          <div class="card-value">{{ infrastructureData.factoryTotalCount || 0 }}<span class="unit">座</span></div>
        </div>
      </div>
      <div class="statistics-card">
        <div class="card-icon pipeline">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">管网</div>
          <div class="card-value">{{ infrastructureData.pipelineTotalLength || 0 }}<span class="unit">KM</span></div>
        </div>
      </div>
      <div class="statistics-card">
        <div class="card-icon station">
          <el-icon><OfficeBuilding /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">换热站</div>
          <div class="card-value">{{ infrastructureData.stationTotalCount || 0 }}<span class="unit">座</span></div>
        </div>
      </div>
      <div class="statistics-card">
        <div class="card-icon building">
          <el-icon><House /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">供热建筑</div>
          <div class="card-value">{{ infrastructureData.buildingCount || 0 }}<span class="unit">栋</span></div>
        </div>
      </div>
      <div class="statistics-card">
        <div class="card-icon user">
          <el-icon><User /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">供热用户</div>
          <div class="card-value">{{ infrastructureData.userTotalCount || 0 }}<span class="unit">家</span></div>
        </div>
      </div>
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-container">
      <!-- 管线材质统计 -->
      <div class="chart-section">
        <div class="section-header">
          <h3 class="section-title">管线材质统计</h3>
          <!-- <div class="progress-info">
            <span>占比：{{ materialProgressPercent }}%</span>
          </div> -->
        </div>
        <div class="chart-content">
          <div class="chart-main">
            <div ref="materialChart" class="chart" style="height: 300px;"></div>
          </div>
          <div class="data-table">
            <el-table :data="materialStatistics" size="small" stripe>
              <el-table-column prop="name" label="材质类型" width="100" align="center"></el-table-column>
              <el-table-column prop="length" label="长度(km)" width="100" align="center">
                <template #default="{ row }">
                  <span>{{ row.length }}km</span>
                </template>
              </el-table-column>
              <el-table-column prop="percent" label="占比" width="80" align="center">
                <template #default="{ row }">
                  <span>{{ row.percent }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 管线年龄统计 -->
      <div class="chart-section">
        <div class="section-header">
          <h3 class="section-title">管线年龄统计</h3>
        </div>
        <div class="chart-content">
          <div class="chart-main">
            <div ref="ageChart" class="chart" style="height: 300px;"></div>
          </div>
          <div class="chart-legend">
            <div
              v-for="(item, index) in ageStatistics"
              :key="item.code"
              class="legend-item"
            >
              <div class="legend-color" :style="{ backgroundColor: ageColors[index] }"></div>
              <div class="legend-text">
                <div class="legend-label">{{ item.name }}</div>
                <div class="legend-value">{{ item.length }}km</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Setting, Connection, OfficeBuilding, House, User } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { 
  getInfrastructureStatistics, 
  getPipelineMaterialStatistics, 
  getPipelineAgeStatistics,
  getAllEnterpriseList 
} from '@/api/heating'
import { AREA_OPTIONS } from '@/constants/gas'

// 响应式数据
const loading = ref(false)
const materialChart = ref(null)
const ageChart = ref(null)
const searchFormRef = ref(null)

// 查询表单
const searchForm = reactive({
  managementUnit: '',
  town: ''
})

// 统计数据
const infrastructureData = reactive({
  factoryTotalCount: 0,
  pipelineTotalLength: 0,
  stationTotalCount: 0,
  buildingCount: 0,
  userTotalCount: 0
})

const materialStatistics = ref([])
const ageStatistics = ref([])
const enterpriseList = ref([])

// 区域选项（东明县的children）
const areaOptions = ref([])

// 图表颜色配置
const ageColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']

// 计算属性
const materialProgressPercent = ref(40)

// 初始化区域数据
const initAreaOptions = () => {
  const dongmingCounty = AREA_OPTIONS.find(item => item.code === '371728')
  if (dongmingCounty && dongmingCounty.children) {
    areaOptions.value = dongmingCounty.children
  }
}

// 获取企业列表
const getEnterpriseList = async () => {
  try {
    const response = await getAllEnterpriseList()
    if (response.code === 200) {
      enterpriseList.value = response.data || []
    }
  } catch (error) {
    console.error('获取企业列表失败:', error)
  }
}

// 获取基础设施统计
const getInfrastructureData = async () => {
  try {
    const response = await getInfrastructureStatistics(searchForm)
    if (response.code === 200) {
      Object.assign(infrastructureData, response.data)
    }
  } catch (error) {
    console.error('获取基础设施统计失败:', error)
    ElMessage.error('获取基础设施统计数据失败')
  }
}

// 获取管线材质统计
const getMaterialData = async () => {
  try {
    const response = await getPipelineMaterialStatistics(searchForm)
    if (response.code === 200 && response.data.statistics) {
      materialStatistics.value = response.data.statistics
      await nextTick()
      renderMaterialChart()
    }
  } catch (error) {
    console.error('获取管线材质统计失败:', error)
    ElMessage.error('获取管线材质统计数据失败')
  }
}

// 获取管线年龄统计
const getAgeData = async () => {
  try {
    const response = await getPipelineAgeStatistics(searchForm)
    if (response.code === 200 && response.data.statistics) {
      ageStatistics.value = response.data.statistics
      await nextTick()
      renderAgeChart()
    }
  } catch (error) {
    console.error('获取管线年龄统计失败:', error)
    ElMessage.error('获取管线年龄统计数据失败')
  }
}

// 渲染管线材质柱状图
const renderMaterialChart = () => {
  if (!materialChart.value) return
  
  const chart = echarts.init(materialChart.value)
  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: materialStatistics.value.map(item => item.name),
      axisLabel: {
        color: '#666',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '长度(km)',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC'
        }
      }
    },
    series: [{
      data: materialStatistics.value.map(item => item.length),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#409EFF' },
          { offset: 1, color: '#79bbff' }
        ])
      }
    }]
  }
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => chart.resize()
  window.addEventListener('resize', resizeHandler)
}

// 渲染管线年龄饼图
const renderAgeChart = () => {
  if (!ageChart.value) return
  
  const chart = echarts.init(ageChart.value)
  const data = ageStatistics.value.map((item, index) => ({
    name: item.name,
    value: item.length,
    itemStyle: {
      color: ageColors[index]
    }
  }))
  
  const option = {
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data,
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => chart.resize()
  window.addEventListener('resize', resizeHandler)
}

// 查询操作
const handleSearch = async () => {
  loading.value = true
  try {
    await Promise.all([
      getInfrastructureData(),
      getMaterialData(),
      getAgeData()
    ])
  } finally {
    loading.value = false
  }
}

// 重置操作
const handleReset = () => {
  searchForm.managementUnit = ''
  searchForm.town = ''
  handleSearch()
}

// 页面初始化
onMounted(async () => {
  initAreaOptions()
  await getEnterpriseList()
  await handleSearch()
})
</script>

<style scoped>
.heating-statistics-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.search-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 1400px) {
  .statistics-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

.statistics-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.heat-source {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.card-icon.pipeline {
  background: linear-gradient(135deg, #4ecdc4, #44bd87);
}

.card-icon.station {
  background: linear-gradient(135deg, #45b7d1, #2980b9);
}

.card-icon.building {
  background: linear-gradient(135deg, #f9ca24, #f0932b);
}

.card-icon.user {
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.unit {
  font-size: 16px;
  color: #909399;
  margin-left: 4px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 1200px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}

.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.progress-info {
  font-size: 14px;
  color: #606266;
}

.chart-content {
  padding: 20px;
  display: flex;
  gap: 20px;
}

.chart-main {
  flex: 1;
}

.data-table {
  width: 300px;
}

.chart-legend {
  width: 200px;
  padding-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.legend-text {
  flex: 1;
}

.legend-label {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.legend-value {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .chart-content {
    flex-direction: column;
  }
  
  .data-table {
    width: 100%;
  }
  
  .chart-legend {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}
</style> 