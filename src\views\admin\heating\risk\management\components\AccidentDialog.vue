<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="accident-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件来源" prop="eventSourceName">
            <el-input v-model="formData.eventSourceName" placeholder="请输入事件来源" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件标题" prop="eventTitle">
            <el-input v-model="formData.eventTitle" placeholder="请输入事件标题" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事件描述" prop="eventDesc">
            <el-input v-model="formData.eventDesc" type="textarea" :rows="3" placeholder="请输入事件描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件分类" prop="eventType">
            <el-select v-model="formData.eventType" placeholder="请选择" class="w-full" @change="handleEventTypeChange">
              <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件分级" prop="eventLevel">
            <el-select v-model="formData.eventLevel" placeholder="请选择" class="w-full">
              <el-option v-for="item in eventLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件处置状态" prop="eventStatus">
            <el-radio-group v-model="formData.eventStatus">
              <el-radio v-for="item in eventStatusOptions" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任单位" prop="ownershipUnit">
            <el-select v-model="formData.ownershipUnit" placeholder="请选择" class="w-full" @change="handleEnterpriseChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="关联处置方案">
            <el-input v-model="emergencySchemeDisplay" placeholder="根据事件分类自动填充" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="发生时间" prop="eventTime">
            <el-date-picker
              v-model="formData.eventTime"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="接收时间" prop="receiveTime">
            <el-date-picker
              v-model="formData.receiveTime"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理完成时间" prop="handleTime">
            <el-date-picker
              v-model="formData.handleTime"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置标注">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="当前是否人员伤亡" prop="isCasualty">
            <el-radio-group v-model="formData.isCasualty">
              <el-radio v-for="item in casualtyOptions" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="死亡人数" prop="deathNum">
            <el-input-number v-model="formData.deathNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受伤人数" prop="injuredNum">
            <el-input-number v-model="formData.injuredNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="上报人员联系方式" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveAccident,
  updateAccident,
  getAllEnterpriseList,
  getEmergencySchemeByEventType
} from '@/api/heating';
import {
  EVENT_TYPE_OPTIONS,
  ACCIDENT_EVENT_LEVEL_OPTIONS,
  ACCIDENT_EVENT_STATUS_OPTIONS,
  CASUALTY_OPTIONS
} from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增事件',
    edit: '编辑事件',
    view: '事件详情'
  };
  return titles[props.mode] || '事件信息';
});

// 下拉选项数据
const eventTypeOptions = EVENT_TYPE_OPTIONS;
const eventLevelOptions = ACCIDENT_EVENT_LEVEL_OPTIONS;
const eventStatusOptions = ACCIDENT_EVENT_STATUS_OPTIONS;
const casualtyOptions = CASUALTY_OPTIONS;
const enterpriseOptions = ref([]);
const areaOptions = ref(AREA_OPTIONS);

// 关联处置方案显示
const emergencySchemeDisplay = ref('');

// 表单数据
const formData = reactive({
  id: '',
  eventSourceName: '',
  eventTitle: '',
  eventDesc: '',
  eventType: '',
  eventTypeName: '',
  eventLevel: '',
  eventLevelName: '',
  eventStatus: 2002601, // 默认未处理
  eventStatusName: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  eventTime: '',
  receiveTime: '',
  handleTime: '',
  address: '',
  longitude: '',
  latitude: '',
  isCasualty: 0, // 默认否
  deathNum: 0,
  injuredNum: 0,
  contactInfo: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  eventSourceName: [{ required: true, message: '请输入事件来源', trigger: 'blur' }],
  eventTitle: [{ required: true, message: '请输入事件标题', trigger: 'blur' }],
  eventDesc: [{ required: true, message: '请输入事件描述', trigger: 'blur' }],
  eventType: [{ required: true, message: '请选择事件分类', trigger: 'change' }],
  eventLevel: [{ required: true, message: '请选择事件分级', trigger: 'change' }],
  eventStatus: [{ required: true, message: '请选择事件处置状态', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择责任单位', trigger: 'change' }],
  eventTime: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  deathNum: [{ type: 'number', min: 0, message: '死亡人数不能小于0', trigger: 'blur' }],
  injuredNum: [{ type: 'number', min: 0, message: '受伤人数不能小于0', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'eventStatus') {
      formData[key] = 2002601;
    } else if (key === 'isCasualty') {
      formData[key] = 0;
    } else if (key === 'deathNum' || key === 'injuredNum') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  emergencySchemeDisplay.value = '';
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 事件类型
  const selectedEventType = eventTypeOptions.find(item => item.value === formData.eventType);
  if (selectedEventType) {
    formData.eventTypeName = selectedEventType.label;
  }

  // 事件分级
  const selectedEventLevel = eventLevelOptions.find(item => item.value === formData.eventLevel);
  if (selectedEventLevel) {
    formData.eventLevelName = selectedEventLevel.label;
  }

  // 事件处置状态
  const selectedEventStatus = eventStatusOptions.find(item => item.value === formData.eventStatus);
  if (selectedEventStatus) {
    formData.eventStatusName = selectedEventStatus.label;
  }

  // 责任单位
  const selectedEnterprise = enterpriseOptions.value.find(item => item.value === formData.ownershipUnit);
  if (selectedEnterprise) {
    formData.ownershipUnitName = selectedEnterprise.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 如果有事件分类，获取对应的处置方案
    if (newVal.eventType) {
      fetchEmergencyScheme(newVal.eventType);
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理事件分类变化
const handleEventTypeChange = async (value) => {
  const selected = eventTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.eventTypeName = selected.label;
    // 获取对应的处置方案
    await fetchEmergencyScheme(value);
  }
};

// 处理企业变化
const handleEnterpriseChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.ownershipUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 获取处置方案
const fetchEmergencyScheme = async (eventType) => {
  try {
    const res = await getEmergencySchemeByEventType(eventType);
    if (res && res.data && res.data.length > 0) {
      // 显示第一个匹配的处置方案
      emergencySchemeDisplay.value = res.data[0].planName || res.data[0].schemeName || '已关联处置方案';
    } else {
      emergencySchemeDisplay.value = '暂无匹配的处置方案';
    }
  } catch (error) {
    console.error('获取处置方案失败', error);
    emergencySchemeDisplay.value = '获取处置方案失败';
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveAccident(submitData);
    } else if (props.mode === 'edit') {
      res = await updateAccident(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
});
</script>

<style scoped>
.accident-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 