// import * as Cesium from "cesium";
import WKT from "terraformer-wkt-parser";
import {devicesAlarmAll, PipelineColorMap, videoMapAll} from "./gisInfo";

/**
 * 将笛卡尔坐标转换为经纬度
 * @param cartesian3
 */
export const cartesian3ToDegree = (cartesian3) => {
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
  const lng = Cesium.Math.toDegrees(cartographic.longitude);
  const lat = Cesium.Math.toDegrees(cartographic.latitude);
  return [lng, lat];
};

/**
 * 计算两个数组的交集
 * @param {Array} arr1 - 第一个数组
 * @param {Array} arr2 - 第二个数组
 * @return {Array} - 两个数组的交集
 */
export const getIntersectionArray = (arr1, arr2)=> {
  // 将第二个数组转换为 Set，提高查找效率
  const set = new Set(arr2);
  // 过滤第一个数组，只保留在 set 中存在的元素
  return arr1.filter(item => set.has(item));
}

/**
 * 将笛卡尔坐标数组转换为经纬度数组
 */
export const cartesian3sToDegrees = (cartesian3s) => {
  return cartesian3s.map((cartesian3) => cartesian3ToDegree(cartesian3));
};

export const getColorByCss = (
  CssColor,
  flash = false
) => {
  let alpha = 1;
  if (flash) {
    return new Cesium.CallbackProperty(function (time) {
      alpha =
        time.secondsOfDay % 1 < 0.5
          ? 2 * (time.secondsOfDay % 1)
          : 2 * (1 - (time.secondsOfDay % 1));
      return Cesium.Color.fromCssColorString(CssColor).withAlpha(alpha);
    }, false);
  } else {
    return Cesium.Color.fromCssColorString(CssColor);
  }
};

/**
 * 检查坐标是否在有效范围内
 * @param lng
 * @param lat
 */
export const checkCoordinate = (lng, lat) => {
  return (
    (lng &&
      parseFloat(lng.toString()) > 73 &&
      parseFloat(lng.toString()) < 136 &&
      lat &&
      parseFloat(lat.toString()) > 3.8 &&
      parseFloat(lat.toString()) < 53.6)
  );
};

/**
 * 将wkt转为geojson
 * @param wkt
 */
export const wktToGeojson = (wkt) => {
  try {
    return WKT.parse(wkt);
  } catch (error) {
    console.error("Error parsing WKT:", error);
    return null;
  }
};
/**
 * geojson转为wkt
 * @param geojson
 */
export const geojsonToWkt = (geojson) => {
  try {
    return WKT.convert(geojson);
  } catch (error) {
    console.error("Error converting GeoJSON to WKT:", error);
    return null;
  }
};

export const formatDataByField = (data, field, value) => {
  const newArr = [];
  if (Array.isArray(data)) {
    data.map((item) => {
      let iconType = ''; // 默认正常图标

      if (devicesAlarmAll.includes(value)) {
        if (item?.alarmStatus && item?.alarmStatus !== "0") {
          iconType = 2; // 报警图标
        } else if (item?.onlineStatus === "0") {
          iconType = 0; // 离线图标
        } else {
          iconType = 1; // 在线图标
        }
      } else if (videoMapAll.includes(value)) {
        iconType = item?.online ? 1 : 0; // 视频监控在线、离线图标
      }

      if (value === 'bridge_info') {
        item.name = item?.bridgeName;
      }

      if (item?.geomText) {
        const geometry = wktToGeojson(item?.geomText?.toUpperCase());
        if (checkCoordinate(geometry?.coordinates[0], geometry?.coordinates[1])) {
          item[field] = value + iconType;
          if (
              value === "gasStationRisk1" ||
              value === "gasStationRisk2" ||
              value === "gasStationRisk3" ||
              value === "gasStationRisk4" ||
              value === "misStationRisk1" ||
              value === "misStationRisk2" ||
              value === "misStationRisk3" ||
              value === "misStationRisk4"
          ) {
            item["id"] = item["stationId"];
          } else if (value === "gasEmergencyEvent") {
            item["status"] = item["dealStatus"];
          }

          // 搜索查询字段
          if (item?.stationName) {
            item["gisName"] = item["stationName"];
          } else if (item?.deviceName) {
            item["gisName"] = item["deviceName"];
          } else if (item?.companyName) {
            item["gisName"] = item["companyName"];
          } else if (item?.userName) {
            item["gisName"] = item["userName"];
          } else if (item?.bottleId) {
            item["gisName"] = item["bottleId"];
          } else if (item?.adjustName) {
            item["gisName"] = item["adjustName"];
          } else if (item?.valveId) {
            item["gisName"] = item["valveId"];
          } else if (item?.resourceName) {
            item["gisName"] = item["resourceName"];
          } else if (item?.rescueName) {
            item["gisName"] = item["rescueName"];
          } else if (item?.dangerName) {
            item["gisName"] = item["dangerName"];
          } else if (item?.protectName) {
            item["gisName"] = item["protectName"];
          } else if (item?.name) {
            item["gisName"] = item["name"];
          } else if (item?.grade) {
            item["gisName"] = item["grade"];
          } else if (item?.eventName) {
            item["gisName"] = item["eventName"];
          } else if (item?.id) {
            item["gisName"] = item["id"];
          }

          newArr.push({
            ...item,
            longitude: geometry?.coordinates[0],
            latitude: geometry?.coordinates[1],
          });
        }
      } else if (checkCoordinate(item?.longitude, item?.latitude)) {
        item[field] = value + iconType;
        if (
          value === "gasStationRisk1" ||
          value === "gasStationRisk2" ||
          value === "gasStationRisk3" ||
          value === "gasStationRisk4" ||
          value === "misStationRisk1" ||
          value === "misStationRisk2" ||
          value === "misStationRisk3" ||
          value === "misStationRisk4"
        ) {
          item["id"] = item["stationId"];
        } else if (value === "gasEmergencyEvent") {
          item["status"] = item["dealStatus"];
        }
        // 搜索查询字段
        if (item?.stationName) {
          item["gisName"] = item["stationName"];
        } else if (item?.deviceName) {
          item["gisName"] = item["deviceName"];
        }  else if (item?.companyName) {
          item["gisName"] = item["companyName"];
        } else if (item?.userName) {
          item["gisName"] = item["userName"];
        } else if (item?.bottleId) {
          item["gisName"] = item["bottleId"];
        } else if (item?.adjustName) {
          item["gisName"] = item["adjustName"];
        } else if (item?.valveId) {
          item["gisName"] = item["valveId"];
        } else if (item?.resourceName) {
          item["gisName"] = item["resourceName"];
        } else if (item?.rescueName) {
          item["gisName"] = item["rescueName"];
        } else if (item?.dangerName) {
          item["gisName"] = item["dangerName"];
        } else if (item?.protectName) {
          item["gisName"] = item["protectName"];
        } else if (item?.name) {
          item["gisName"] = item["name"];
        } else if (item?.grade) {
          item["gisName"] = item["grade"];
        } else if (item?.eventName) {
          item["gisName"] = item["eventName"];
        } else if (item?.id) {
          item["gisName"] = item["id"];
        }

        newArr.push({
          ...item
        });
      }
    });
  }
  return newArr;
};

export const formatPolylineDataByFieldAndStyle = (data, field, value) => {
  const newArr = [];
  if (Array.isArray(data)) {
    data.map((item) => {
      if (item?.geomText) {
        const geometry = wktToGeojson(item?.geomText?.toUpperCase());
        if (geometry && geometry?.coordinates) {
        /*  let degrees = [];
          geometry.coordinates.forEach((el) => {
            el.forEach((_el) => {
              degrees = degrees.concat(_el);
            });
          });
          item["degrees"] = degrees;*/

          item[field] = value;
          item["geometry"] = geometry;
          item["colorStr"] =PipelineColorMap[value] || "#DA12FF";
          // 搜索查询字段
          if (item?.riskCode) {
            item["gisName"] = item["lineId"];
          } else if(item?.pipelineCode) {
            item["gisName"] = item["pipelineCode"];
          }
          newArr.push({
            ...item,
          });
        }
      }
    });
  }
  return newArr;
};
