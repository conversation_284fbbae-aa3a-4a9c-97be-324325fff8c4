<template>
  <PanelBox title="预警处置监管">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedType" :options="typeOptions" @change="handleTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 顶部信息展示区域 -->
      <div class="info-cards">
        <div class="info-card">
          <div class="info-value info-value-red">{{ dashboardData.totalAlarms }}</div>
          <div class="info-label">预警总数</div>
        </div>
        <div class="info-card">
          <div class="info-value info-value-pink">{{ dashboardData.todayAlarms }}</div>
          <div class="info-label">今日预警</div>
        </div>
        <div class="info-card">
          <div class="info-value info-value-pink">{{ dashboardData.monthAlarms }}</div>
          <div class="info-label">本月预警</div>
        </div>
        <div class="info-card">
          <div class="info-value info-value-blue">{{ dashboardData.handledAlarms }}</div>
          <div class="info-label">已处置</div>
        </div>
        <div class="info-card">
          <div class="info-value info-value-green">{{ dashboardData.handleRate }}</div>
          <div class="info-label">处置完成率</div>
        </div>
      </div>

      <!-- 图表展示区域 -->
      <div class="charts-container">
        <div class="chart-left" ref="pieChartRef"></div>
        <div class="chart-right" ref="lineChartRef"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getWarningStatistics } from '@/api/comprehensive'

// 类型选择
const selectedType = ref('all')
const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '燃气', value: 'gas' },
  { label: '排水', value: 'water' },
  { label: '供热', value: 'heating' },
  { label: '桥梁', value: 'bridge' }
]

// 面板展示数据
const dashboardData = reactive({
  totalAlarms: 0,
  todayAlarms: 0,
  monthAlarms: 0,
  handledAlarms: 0,
  handleRate: '0%'
})

// 饼图数据
const pieChartData = reactive({
  all: [
    { name: '一级预警', value: 0, color: '#FF3636' },
    { name: '二级预警', value: 0, color: '#FFA033' },
    { name: '三级预警', value: 0, color: '#FFDE36' },
  ],
  gas: [
    { name: '一级预警', value: 0, color: '#FF3636' },
    { name: '二级预警', value: 0, color: '#FFA033' },
    { name: '三级预警', value: 0, color: '#FFDE36' },
  ],
  water: [
    { name: '一级预警', value: 0, color: '#FF3636' },
    { name: '二级预警', value: 0, color: '#FFA033' },
    { name: '三级预警', value: 0, color: '#FFDE36' },
  ],
  heating: [
    { name: '一级预警', value: 0, color: '#FF3636' },
    { name: '二级预警', value: 0, color: '#FFA033' },
    { name: '三级预警', value: 0, color: '#FFDE36' },
  ],
  bridge: [
    { name: '一级预警', value: 0, color: '#FF3636' },
    { name: '二级预警', value: 0, color: '#FFA033' },
    { name: '三级预警', value: 0, color: '#FFDE36' },
  ]
})

// 折线图数据
const lineChartData = reactive({
  all: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  },
  gas: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  },
  water: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  },
  heating: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  },
  bridge: {
    xAxis: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  }
})

// 图表实例
const pieChartRef = ref(null)
const lineChartRef = ref(null)
let pieChartInstance = null
let lineChartInstance = null

// 处理类型变化
const handleTypeChange = (value) => {
  // 获取动态数据
  fetchData(value)
}

// 获取动态数据
const fetchData = async (type) => {
  try {
    // 映射类型到relatedBusiness参数
    const businessMap = {
      'all': null,
      'gas': '7000501',
      'water': '7000502',
      'heating': '7000503',
      'bridge': '7000504'
    }

    const relatedBusiness = businessMap[type]
    const response = await getWarningStatistics(relatedBusiness)

    if (response && response.data) {
      const data = response.data

      // 更新面板数据
      dashboardData.totalAlarms = data.totalCount || 0
      dashboardData.todayAlarms = data.todayCount || 0
      dashboardData.monthAlarms = data.monthCount || 0
      dashboardData.handledAlarms = data.handledCount || 0
      dashboardData.handleRate = data.handledRate || '0%'

      // 更新饼图数据
      const currentPieData = pieChartData[type]
      if (currentPieData) {
        currentPieData[0].value = data.level1Count || 0
        currentPieData[1].value = data.level2Count || 0
        currentPieData[2].value = data.level3Count || 0
      }

      // 更新折线图数据
      const currentLineData = lineChartData[type]
      if (currentLineData && data.trends && data.trends.length > 0) {
        // 按月份顺序排序趋势数据
        const monthOrder = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
        const sortedTrends = monthOrder.map(month => {
          const trend = data.trends.find(t => t.month === month)
          return trend ? trend.count : 0
        })
        currentLineData.values = sortedTrends
      } else {
        // 如果trends为空数组，保持全0以维持图表结构
        currentLineData.values = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      }

      // 更新图表
      updateCharts()
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 更新所有图表
const updateCharts = () => {
  updatePieChart()
  updateLineChart()
}

// 更新饼图
const updatePieChart = () => {
  if (!pieChartInstance) return

  const data = pieChartData[selectedType.value] || pieChartData.all
  const option = createPieChartOption(data)
  pieChartInstance.setOption(option)
}

// 更新折线图
const updateLineChart = () => {
  if (!lineChartInstance) return

  const data = lineChartData[selectedType.value] || lineChartData.all
  const option = createLineChartOption(data)
  lineChartInstance.setOption(option)
}

// 创建饼图配置
const createPieChartOption = (data) => {
  // 检查是否所有数据都为0
  const totalValue = data.reduce((sum, item) => sum + item.value, 0)
  const isEmpty = totalValue === 0

  // 如果数据为空，创建默认显示数据
  const seriesData = isEmpty
    ? data.map(item => ({
      name: item.name,
      value: 1, // 设置为1以显示结构
      itemStyle: {
        color: item.color,
        opacity: 0.3 // 降低透明度表示无数据
      }
    }))
    : data.map(item => ({
      name: item.name,
      value: item.value,
      itemStyle: {
        color: item.color
      }
    }))

  return {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      borderWidth: 1,
      padding: [8, 12],
      textStyle: {
        color: '#fff',
        fontSize: 11
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
      formatter: isEmpty ? '暂无数据' : '{b} <br/> {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 5,
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 5
    },
    series: [
      {
        name: '预警分级',
        type: 'pie',
        radius: ['20%', '38%'],
        center: ['33%', '40%'],
        roseType: 'radius',
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 1
        },
        label: {
          show: !isEmpty,
          position: 'outside',
          formatter: '{c}',
          color: '#fff',
          fontSize: 12
        },
        emphasis: {
          label: {
            show: !isEmpty,
            fontSize: 12,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: !isEmpty,
          length: 5,
          length2: 8
        },
        data: seriesData
      }
    ]
  }
}

// 创建折线图配置
const createLineChartOption = (data) => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '5%',
      right: '5%',
      bottom: '35%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      }
    },
    xAxis: {
      type: 'category',
      data: data.xAxis,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'line',
        data: data.values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00C2FF',
          borderColor: 'rgba(215, 236, 255, 0.3)',
          borderWidth: 6
        },
        lineStyle: {
          color: '#246BFD',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(36, 107, 253, 0.4)' },
              { offset: 1, color: 'rgba(36, 107, 253, 0.01)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) return

  pieChartInstance = echarts.init(pieChartRef.value)
  const data = pieChartData[selectedType.value] || pieChartData.all
  const option = createPieChartOption(data)
  pieChartInstance.setOption(option)
}

// 初始化折线图
const initLineChart = () => {
  if (!lineChartRef.value) return

  lineChartInstance = echarts.init(lineChartRef.value)
  const data = lineChartData[selectedType.value] || lineChartData.all
  const option = createLineChartOption(data)
  lineChartInstance.setOption(option)
}

// 窗口大小变化时重置图表大小
const handleResize = () => {
  pieChartInstance && pieChartInstance.resize()
  lineChartInstance && lineChartInstance.resize()
}

// 监听类型变化
watch(selectedType, (newVal) => {
  handleTypeChange(newVal)
})

// 组件挂载后初始化
onMounted(async () => {
  await nextTick()
  // 初始化图表
  initPieChart()
  initLineChart()
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  // 获取初始数据
  fetchData(selectedType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 顶部信息展示区域 */
.info-cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.info-card {
  flex: 1;
  height: 60px;
  background: url('@/assets/images/screen/comprehensive/info_bg.png');
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 5px;
}

.info-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

.info-value-red {
  background: linear-gradient(90deg, #FFFFFF 0%, #FF0000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-value-pink {
  background: linear-gradient(90deg, #FFFFFF 0%, #FF5858 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-value-blue {
  background: linear-gradient(90deg, #E2FBFF 0%, #23CAFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-value-green {
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

/* 图表展示区域 */
.charts-container {
  flex: 1;
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.chart-left {
  flex: 1;
  height: 100%;
  min-height: 220px;
}

.chart-right {
  flex: 1;
  height: 100%;
  min-height: 220px;
}

/* 响应式适配 */
@media (min-height: 910px) and (max-height: 1050px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .info-card {
    height: 50px;
  }

  .info-value {
    font-size: 18px;
    line-height: 20px;
  }

  .info-label {
    font-size: 11px;
  }

  .charts-container {
    gap: 15px;
    margin-top: 5px;
  }
}

@media (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }

  .info-card {
    height: 45px;
    gap: 2px;
  }

  .info-value {
    font-size: 16px;
    line-height: 18px;
  }

  .info-label {
    font-size: 10px;
  }

  .charts-container {
    gap: 10px;
    margin-top: 3px;
  }
}
</style>