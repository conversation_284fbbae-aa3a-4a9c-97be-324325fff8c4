<template>
  <div class="drainage-device-container">
    <div class="content-wrapper">
      <!-- 上部分区域 -->
      <div class="top-section">
        <!-- Tab 区域 -->
        <div class="tabs-container">
          <div class="tabs">
            <div
              v-for="(tab, index) in tabs"
              :key="index"
              :class="['tab-item', { active: currentTab === tab.value }]"
              @click="handleTabChange(tab.value)"
            >
              {{ tab.label }}
            </div>
          </div>
          <div class="tabs-border"></div>
        </div>
      </div>

      <!-- 下部分区域 -->
      <div class="bottom-section">
        <!-- 内容区域 -->
        <div class="content-area">
          <component :is="currentComponent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef } from 'vue';

import DrainageDeviceSensor from './components/DrainageDeviceSensor.vue';
import DrainageDeviceVideo from './components/DrainageDeviceVideo.vue';

// 定义Tab选项
const tabs = [
  { label: '传感器信息', value: 'sensor' },
  // { label: '视频监控', value: 'video' }
];

// 当前选中的Tab
const currentTab = ref('sensor');

// 动态组件
const componentMap = {
  sensor: DrainageDeviceSensor,
  video: DrainageDeviceVideo
};

// 当前显示的组件
const currentComponent = shallowRef(componentMap.sensor);

// 处理Tab切换
const handleTabChange = (tab) => {
  currentTab.value = tab;
  currentComponent.value = componentMap[tab];
};
</script>

<style scoped>
.drainage-device-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 16px;
}

/* 上部分样式 */
.top-section {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
}

/* Tab样式 */
.tabs-container {
  width: 100%;
}

.tabs {
  display: flex;
  align-items: center;
}

.tab-item {
  position: relative;
  padding: 0 16px 8px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  cursor: pointer;
}

.tab-item.active {
  font-weight: 600;
  color: #0066FF;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  width: calc(100% - 32px);
  height: 2px;
  background: #0066FF;
}

.tabs-border {
  width: 100%;
  height: 1px;
  background: #E5E5E5;
  margin-top: -1px;
}

/* 下部分样式 */
.bottom-section {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  overflow: hidden;
  min-height: calc(100vh - 240px);
}

.content-area {
  width: 100%;
  height: 100%;
}
</style> 