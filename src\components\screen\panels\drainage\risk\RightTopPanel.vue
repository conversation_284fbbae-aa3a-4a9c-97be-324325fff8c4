<template>
  <PanelBox title="隐患信息" class="right-top-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 隐患数量统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner red"></span></span>
            <span class="stat-label">隐患总数</span>
          </div>
          <span class="stat-value red-gradient">{{ statsData.total }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner orange"></span></span>
            <span class="stat-label">未整改</span>
          </div>
          <span class="stat-value orange-gradient">{{ statsData.unhandled }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner yellow"></span></span>
            <span class="stat-label">整改中</span>
          </div>
          <span class="stat-value yellow-gradient">{{ statsData.handling }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
            <span class="stat-label">已整改</span>
          </div>
          <span class="stat-value green-gradient">{{ statsData.handled }}</span>
        </div>
      </div>

      <!-- 隐患列表区域 -->
      <div v-if="hazardList.length === 0 && !loading" class="no-data-wrapper">
        <NoData />
      </div>
      <ScrollTable
        v-else
        :columns="tableColumns"
        :data="hazardList"
        :autoScroll="true"
        :scrollSpeed="2000"
        :tableHeight="tableHeight"
        :visibleRows="2"
        :hiddenHeader="true"
      >
        <template #custom="{ row }">
          <div class="hazard-row" @click="openHazardDetail(row)">
            <div class="hazard-main">
              <span class="hazard-desc">{{ row.description }}</span>
              <div class="hazard-status">
                <span class="level-tag" :style="{ background: getLevelColor(row.level) }">{{ row.level }}</span>
                <span class="status-tag" :style="{ background: getStatusColor(row.status) }">{{ row.status }}</span>
              </div>
            </div>
            <div class="hazard-location">
              <img src="@/assets/images/screen/common/location.svg" alt="location" class="location-icon" />
              <span class="location-text">{{ row.location }}</span>
            </div>
            <div class="hazard-divider"></div>
          </div>
        </template>
      </ScrollTable>
    </div>
    
    <!-- 隐患详情弹窗 -->
    <HazardDetailModal v-model="showHazardDetail" :hazardData="currentHazard" />
  </PanelBox>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import HazardDetailModal from './HazardDetailModal.vue'
import NoData from '@/components/common/NoData.vue'
import { getHiddenDangerStatisticsScreen } from '@/api/drainage.js'

// 时间选择
const timeRange = ref(7)
const timeOptions = [
  { label: '近一周', value: 7 },
  { label: '近一月', value: 30 },
  { label: '近一年', value: 365 }
]

// 加载状态
const loading = ref(false)

// 统计数据
const statsData = ref({
  total: 0,
  unhandled: 0,
  handling: 0,
  handled: 0
})

// 隐患列表数据
const hazardList = ref([])

// 表格配置
const tableColumns = [
  { title: '隐患信息', dataIndex: 'custom', width: '100%' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  if (window.innerHeight === 910) {
    return '200px'
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '220px'
  } else if (window.innerWidth >= 2561) {
    return '260px'
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '210px'
  } else {
    return '180px'
  }
})

// 获取隐患等级对应的颜色
const getLevelColor = (level) => {
  const colorMap = {
    '重大隐患': '#EAA01B',
    '较大隐患': '#FF6D28',
    '一般隐患': '#FFC75A'
  }
  return colorMap[level] || '#3FD87C'
}

// 获取状态对应的颜色
const getStatusColor = (status) => {
  const statusMap = {
    '未整改': '#FF8628',
    '整改中': '#FF8628',
    '已整改': '#3FD87C'
  }
  return statusMap[status] || '#FF8628'
}

// 获取隐患统计数据
const fetchHiddenDangerData = async () => {
  try {
    loading.value = true
    const response = await getHiddenDangerStatisticsScreen(1, 10, timeRange.value)
    
    if (response.code === 200 && response.data) {
      const data = response.data
      
      // 更新统计数据
      statsData.value = {
        total: data.dangerCount || 0,
        unhandled: data.unRectifyCount || 0,
        handling: data.rectifyingCount || 0,
        handled: data.rectifiedCount || 0
      }
      
      // 更新隐患列表数据
      if (data.dangerList && data.dangerList.records && data.dangerList.records.length > 0) {
        hazardList.value = data.dangerList.records.map(item => ({
          id: item.id,
          description: item.dangerDesc || '暂无描述',
          level: item.dangerLevelName || '未知',
          status: item.dangerStatusName || '未知',
          location: item.address || '暂无地址信息',
          longitude: item.longitude,
          latitude: item.latitude,
          dangerType: item.dangerTypeName,
          dangerTarget: item.dangerTargetName,
          reportTime: item.reportTime,
          responsibleUser: item.responsibleUserName
        }))
      } else {
        hazardList.value = []
      }
    } else {
      // 接口返回失败时重置数据
      statsData.value = {
        total: 0,
        unhandled: 0,
        handling: 0,
        handled: 0
      }
      hazardList.value = []
    }
  } catch (error) {
    console.error('获取隐患统计数据失败:', error)
    // 错误时重置数据
    statsData.value = {
      total: 0,
      unhandled: 0,
      handling: 0,
      handled: 0
    }
    hazardList.value = []
  } finally {
    loading.value = false
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  timeRange.value = value
  fetchHiddenDangerData()
}

// 隐患详情弹窗相关
const showHazardDetail = ref(false)
const currentHazard = ref({})

// 打开隐患详情弹窗
const openHazardDetail = (row) => {
  currentHazard.value = {
    ...row,
    source: '系统上报',
    type: row.dangerType || '管道渗漏风险',
    target: row.dangerTarget || '管网（GX1129）',
    deadline: '2023年1月5日',
    responsible: row.responsibleUser || '张三'
  }
  showHazardDetail.value = true
}

// 监听时间范围变化
watch(timeRange, () => {
  fetchHiddenDangerData()
})

// 组件挂载时获取数据
onMounted(() => {
  fetchHiddenDangerData()
})
</script>

<style scoped>
.right-top-panel {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 15px 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  position: relative;
  gap: 8px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  margin-bottom: 2px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: relative;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.red { 
  background: #FC4949;
}

.stat-dot-inner.orange { 
  background: #FF6D28;
}

.stat-dot-inner.yellow { 
  background: #FFC75A;
}

.stat-dot-inner.green { 
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.red) {
  background: rgba(252, 73, 73, 0.4);
}

.stat-dot:has(.stat-dot-inner.orange) {
  background: rgba(255, 109, 40, 0.4);
}

.stat-dot:has(.stat-dot-inner.yellow) {
  background: rgba(255, 199, 90, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.red-gradient {
  background: linear-gradient(90deg, #FB3737 0%, #FEA6A6 100%);
  -webkit-background-clip: text;
}

.orange-gradient {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
}

.yellow-gradient {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
}

.green-gradient {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
}

.stat-unit {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.4);
  text-align: center;
}

/* 隐患列表样式 */
.hazard-row {
  padding: 15px 0;
  width: 100%;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.3s;
}

.hazard-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.hazard-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.hazard-desc {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hazard-status {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.level-tag {
  width: 60px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.status-tag {
  width: 60px;
  height: 24px;
  background: rgba(0, 80, 179, 0.4);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.hazard-location {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.location-icon {
  width: 12px;
  height: 12px;
}

.location-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hazard-divider {
  display: none;
}

/* 无数据样式 */
.no-data-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex: 1;
  min-height: 160px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .right-top-panel {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .right-top-panel {
    height: 310px;
  }
}

@media screen and (min-width: 2561px) {
  .right-top-panel {
    height: 310px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .right-top-panel {
    height: 310px;
  }
  
  .panel-content {
    padding: 15px;
  }
  
  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .right-top-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .right-top-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
}
</style>