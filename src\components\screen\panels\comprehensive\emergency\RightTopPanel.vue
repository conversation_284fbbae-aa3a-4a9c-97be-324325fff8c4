<template>
  <PanelBox title="应急资源统计">
    <div class="panel-content">
      <div class="stats-grid">
        <div class="stats-item">
          <div class="item-title">避难场所</div>
          <div class="item-value">{{ resourceData.shelterCount }}</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">应急队伍</div>
          <div class="item-value">{{ resourceData.teamCount }}</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">物资装备</div>
          <div class="item-value">{{ resourceData.suppliesCount }}</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">救援人员</div>
          <div class="item-value">{{ resourceData.respondersCount }}</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">医疗机构</div>
          <div class="item-value">{{ resourceData.hospitalCount }}</div>
          <div class="item-bg"></div>
        </div>
        <div class="stats-item">
          <div class="item-title">应急仓库</div>
          <div class="item-value">{{ resourceData.storeCount }}</div>
          <div class="item-bg"></div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getEmergencyResourceStatistics } from '@/api/comprehensive'

// 综合态势总览右上面板组件

// 应急资源统计数据
const resourceData = ref({
  shelterCount: 0,    // 避难场所数
  teamCount: 0,       // 应急队伍数
  suppliesCount: 0,   // 物资装备数
  respondersCount: 0, // 救援人员数
  hospitalCount: 0,   // 医疗机构数
  storeCount: 0       // 应急仓库数
})

/**
 * 获取应急资源统计数据
 */
const fetchResourceData = async () => {
  try {
    const response = await getEmergencyResourceStatistics()
    if (response.code === 200 && response.data) {
      resourceData.value = {
        shelterCount: response.data.shelterCount || 0,
        teamCount: response.data.teamCount || 0,
        suppliesCount: response.data.suppliesCount || 0,
        respondersCount: response.data.respondersCount || 0,
        hospitalCount: response.data.hospitalCount || 0,
        storeCount: response.data.storeCount || 0
      }
    }
  } catch (error) {
    console.error('获取应急资源统计数据失败:', error)
    // 保持默认值，确保页面正常显示
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchResourceData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15px;
  height: 100%;
}

.stats-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.item-title {
  position: relative;
  z-index: 2;
  width: 112px;
  height: 27px;
  background: url('@/assets/images/screen/comprehensive/title_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, "PingFang SC";
  font-weight: 500;
  font-size: 14px;
  color: #D7F2FF;
}

.item-value {
  position: relative;
  z-index: 2;
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 26px;
  color: #FFFFFF;
  margin: 0.5rem 0px 1rem 0px;;
}

.item-bg {
  position: absolute;
  bottom: 0;
  width: 108px;
  height: 69px;
  background: url('@/assets/images/screen/comprehensive/tongji_bg.png') no-repeat center center;
  background-size: 100% 100%;
  z-index: 1;
}
</style>