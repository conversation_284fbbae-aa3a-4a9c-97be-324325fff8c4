<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="alert-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="formData.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="报警描述" prop="alarmDesc">
            <el-input 
              v-model="formData.alarmDesc" 
              type="textarea" 
              :rows="4" 
              placeholder="请输入报警描述" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通报区域" prop="notifyAreaCodes">
            <el-select 
              v-model="formData.notifyAreaCodes" 
              multiple 
              placeholder="请选择通报区域" 
              class="w-full"
              @change="handleAreaChange"
            >
              <el-option 
                v-for="item in areaOptions" 
                :key="item.code" 
                :label="item.name" 
                :value="item.code" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通报方式" prop="notifySms">
            <el-checkbox v-model="formData.notifySms">短信</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input 
              v-model="formData.remark" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入备注信息" 
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveAlarmNotify,
  updateAlarmNotify
} from '@/api/comprehensive';
import { AREA_OPTIONS } from '@/constants/gas';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  };
  return titles[props.mode] || '报警通报';
});

// 获取东明县下的区域选项
const areaOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  alarmDesc: '',
  notifyArea: '',
  notifyAreaName: '',
  notifyAreaCodes: [], // 用于多选绑定
  notifySms: false,
  remark: ''
});

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  alarmDesc: [{ required: true, message: '请输入报警描述', trigger: 'blur' }],
  notifyAreaCodes: [{ 
    required: true, 
    type: 'array',
    min: 1,
    message: '请选择通报区域', 
    trigger: 'change' 
  }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'notifySms') {
      formData[key] = false;
    } else if (key === 'notifyAreaCodes') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });
};

// 初始化区域选项
const initAreaOptions = () => {
  // 找到东明县
  const dongmingCounty = AREA_OPTIONS.find(item => item.code === '371728');
  if (dongmingCounty && dongmingCounty.children) {
    areaOptions.value = dongmingCounty.children;
  }
};

// 处理区域选择变化
const handleAreaChange = (values) => {
  if (values && values.length > 0) {
    const selectedAreas = areaOptions.value.filter(area => values.includes(area.code));
    formData.notifyArea = values.join(',');
    formData.notifyAreaName = selectedAreas.map(area => area.name).join(',');
  } else {
    formData.notifyArea = '';
    formData.notifyAreaName = '';
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理通报区域的回显
    if (newVal.notifyArea) {
      formData.notifyAreaCodes = newVal.notifyArea.split(',');
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    // 移除辅助字段
    delete submitData.notifyAreaCodes;

    let res;
    if (props.mode === 'add') {
      res = await saveAlarmNotify(submitData);
    } else if (props.mode === 'edit') {
      res = await updateAlarmNotify(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  initAreaOptions();
});
</script>

<style scoped>
.alert-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input),
:deep(.el-textarea__inner) {
  border-radius: 2px;
}

:deep(.el-input__wrapper) {
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.dialog-footer {
  text-align: center;
}
</style> 