<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="pipeline-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线编码" prop="pipelineCode">
            <el-input v-model="formData.pipelineCode" placeholder="请输入管线编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管线类型" prop="pipelineType">
            <el-select v-model="formData.pipelineType" placeholder="请选择" class="w-full">
              <el-option v-for="item in PIPELINE_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋设方式" prop="buriedType">
            <el-select v-model="formData.buriedType" placeholder="请选择" class="w-full">
              <el-option v-for="item in BURIED_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="材质" prop="material">
            <el-select v-model="formData.material" placeholder="请选择" class="w-full">
              <el-option v-for="item in PIPE_MATERIALS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设计压力(Mpa)" prop="designPressure">
            <el-input v-model="formData.designPressure" placeholder="请输入设计压力" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管径(DN)" prop="pipeDiameter">
            <el-select v-model="formData.pipeDiameter" placeholder="请选择" class="w-full">
              <el-option v-for="item in PIPE_DIAMETERS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="长度(m)" prop="pipeLength">
            <el-input v-model="formData.pipeLength" placeholder="请输入管线长度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="流向" prop="flowDirection">
            <el-input v-model="formData.flowDirection" placeholder="请输入流向" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="线型" prop="pipeType">
            <el-select v-model="formData.pipeType" placeholder="请选择" class="w-full">
              <el-option v-for="item in LINE_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起点埋深(m)" prop="startPointDepth">
            <el-input v-model="formData.startPointDepth" placeholder="请输入起点埋深" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点埋深(m)" prop="endPointDepth">
            <el-input v-model="formData.endPointDepth" placeholder="请输入终点埋深" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起点高程(m)" prop="startPointDistance">
            <el-input v-model="formData.startPointDistance" placeholder="请输入起点高程" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点高程(m)" prop="endPointDistance">
            <el-input v-model="formData.endPointDistance" placeholder="请输入终点高程" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="起点经纬度">
            <div class="flex items-center">
              <el-input v-model="formData.startPointLongitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.startPointLatitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openStartMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="终点经纬度">
            <div class="flex items-center">
              <el-input v-model="formData.endPointLongitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.endPointLatitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openEndMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设时间" prop="constructionTime">
            <el-date-picker
              v-model="formData.constructionTime"
              type="date"
              placeholder="请选择建设时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus">
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in USAGE_STATUS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="所属区划">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="" prop="address" class="w-full">
            <el-input v-model="formData.address" placeholder="输入详细地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联热源" prop="factoryId">
            <el-select v-model="formData.factoryId" placeholder="请选择" class="w-full" @change="handleFactoryChange">
              <el-option v-for="item in heatFactoryOptions" :key="item.id" :label="item.factoryName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联换热站" prop="stationId">
            <el-select v-model="formData.stationId" placeholder="请选择" class="w-full" @change="handleStationChange">
              <el-option v-for="item in heatStationOptions" :key="item.id" :label="item.stationName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full" @change="handleOwnershipChange">
              <el-option v-for="item in enterpriseOptions" :key="item.id" :label="item.enterpriseName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getAllEnterpriseList, getHeatFactoryList, getAllHeatStationList, savePipelineInfo, updatePipelineInfo } from '@/api/heating';
import { PIPELINE_TYPES, PIPELINE_TYPE_MAP, BURIED_TYPES, BURIED_TYPE_MAP, PIPE_MATERIALS, PIPE_MATERIAL_MAP, LINE_TYPES, LINE_TYPE_MAP, USAGE_STATUS, USAGE_STATUS_MAP } from '@/constants/heating';
import { AREA_OPTIONS, PIPE_DIAMETERS, PIPE_DIAMETER_MAP } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增管线',
    edit: '编辑管线',
    view: '管线详情'
  };
  return titles[props.mode] || '管线信息';
});

// 企业选项
const enterpriseOptions = ref([]);
// 热源选项
const heatFactoryOptions = ref([]);
// 换热站选项
const heatStationOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  pipelineCode: '',
  pipelineType: '',
  pipelineTypeName: '',
  buriedType: '',
  buriedTypeName: '',
  material: '',
  materialName: '',
  designPressure: '',
  pipeDiameter: '',
  pipeLength: '',
  roadName: '',
  flowDirection: '',
  pipeType: '',
  pipeTypeName: '',
  startPointDepth: '',
  endPointDepth: '',
  startPointDistance: '',
  endPointDistance: '',
  startPointLongitude: '',
  startPointLatitude: '',
  endPointLongitude: '',
  endPointLatitude: '',
  constructionTime: '',
  usageStatus: '',
  usageStatusName: '',
  address: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  factoryId: '',
  factoryName: '',
  stationId: '',
  stationName: '',
  managementUnit: '',
  managementUnitName: '',
  remarks: '',
});

// 表单验证规则
const formRules = {
  pipelineCode: [{ required: true, message: '请输入管线编码', trigger: 'blur' }],
  pipelineType: [{ required: true, message: '请选择管线类型', trigger: 'change' }],
  buriedType: [{ required: true, message: '请选择埋设方式', trigger: 'change' }],
  material: [{ required: true, message: '请选择材质', trigger: 'change' }],
  pipeDiameter: [{ required: true, message: '请选择管径', trigger: 'change' }],
  pipeLength: [{ required: true, message: '请输入管线长度', trigger: 'blur' }],
  startPointDepth: [{ required: true, message: '请输入起点埋深', trigger: 'blur' }],
  endPointDepth: [{ required: true, message: '请输入终点埋深', trigger: 'blur' }],
  constructionTime: [{ required: true, message: '请选择建设时间', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  usageStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }],
};

// 获取企业列表
const fetchEnterpriseOptions = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.code === 200) {
      enterpriseOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取企业列表失败:', error);
    ElMessage.error('获取企业列表失败');
  }
};

// 获取热源列表
const fetchHeatFactoryOptions = async () => {
  try {
    const res = await getHeatFactoryList();
    if (res && res.code === 200) {
      heatFactoryOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取热源列表失败:', error);
    ElMessage.error('获取热源列表失败');
  }
};

// 获取换热站列表
const fetchHeatStationOptions = async () => {
  try {
    const res = await getAllHeatStationList();
    if (res && res.code === 200) {
      heatStationOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取换热站列表失败:', error);
    ElMessage.error('获取换热站列表失败');
  }
};

// 权属单位变更处理
const handleOwnershipChange = (value) => {
  if (value) {
    const selected = enterpriseOptions.value.find(item => item.id === value);
    if (selected) {
      formData.managementUnitName = selected.enterpriseName;
    }
  }
};

// 热源变更处理
const handleFactoryChange = (value) => {
  if (value) {
    const selected = heatFactoryOptions.value.find(item => item.id === value);
    if (selected) {
      formData.factoryName = selected.factoryName;
    }
  }
};

// 换热站变更处理
const handleStationChange = (value) => {
  if (value) {
    const selected = heatStationOptions.value.find(item => item.id === value);
    if (selected) {
      formData.stationName = selected.stationName;
    }
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理建设时间格式
    if (newVal.constructionTime) {
      if (typeof newVal.constructionTime === 'object') {
        formData.constructionTime = moment(new Date(
          newVal.constructionTime.year + 1900,
          newVal.constructionTime.month,
          newVal.constructionTime.date
        )).format('YYYY-MM-DD');
      } else if (typeof newVal.constructionTime === 'string') {
        formData.constructionTime = newVal.constructionTime;
      }
    }

    // 确保类型为数字类型
    ['pipelineType', 'buriedType', 'material', 'pipeType', 'usageStatus'].forEach(key => {
      if (formData[key] && typeof formData[key] === 'string') {
        formData[key] = Number(formData[key]);
      }
    });
  } else if (props.mode === 'add') {
    // 新增模式清空表单
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听表单字段变化，更新映射名称
watch(() => formData.pipelineType, (val) => {
  if (val) {
    formData.pipelineTypeName = PIPELINE_TYPE_MAP[val] || '';
  }
});

watch(() => formData.buriedType, (val) => {
  if (val) {
    formData.buriedTypeName = BURIED_TYPE_MAP[val] || '';
  }
});

watch(() => formData.material, (val) => {
  if (val) {
    formData.materialName = PIPE_MATERIAL_MAP[val] || '';
  }
});

watch(() => formData.pipeType, (val) => {
  if (val) {
    formData.pipeTypeName = LINE_TYPE_MAP[val] || '';
  }
});

watch(() => formData.usageStatus, (val) => {
  if (val) {
    formData.usageStatusName = USAGE_STATUS_MAP[val] || '';
  }
});

watch(() => formData.pipeDiameter, (val) => {
  if (val) {
    // 不需要处理名称，因为值本身就是标识符
  }
});

// 起点地图选点事件处理函数
const handleStartCollectLocation = (params) => {
  nextTick(() => {
    formData.startPointLongitude = params.longitude || 0;
    formData.startPointLatitude = params.latitude || 0;
  });
};

// 终点地图选点事件处理函数
const handleEndCollectLocation = (params) => {
  nextTick(() => {
    formData.endPointLongitude = params.longitude || 0;
    formData.endPointLatitude = params.latitude || 0;
  });
};

// 当前选点模式 ('start' 或 'end')
const pointMode = ref('start');

// 打开起点地图选点
const openStartMapPicker = () => {
  pointMode.value = 'start';
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation");
  // 添加新的监听器
  bus.on("getCollectLocation", handleStartCollectLocation);
};

// 打开终点地图选点
const openEndMapPicker = () => {
  pointMode.value = 'end';
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation");
  // 添加新的监听器
  bus.on("getCollectLocation", handleEndCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 确保数值类型字段为数字
    const numberFields = ['designPressure', 'pipeLength', 'startPointDepth', 'endPointDepth', 
                         'startPointDistance', 'endPointDistance', 'startPointLongitude', 
                         'startPointLatitude', 'endPointLongitude', 'endPointLatitude'];
                         
    numberFields.forEach(field => {
      if (submitData[field] !== undefined && submitData[field] !== '') {
        submitData[field] = Number(submitData[field]);
      }
    });
    
    // 处理建设时间格式转换为 yyyy-MM-dd HH:mm:ss
    if (submitData.constructionTime) {
      submitData.constructionTime = moment(submitData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await savePipelineInfo(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePipelineInfo(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation");
});

// 组件挂载时初始化
onMounted(async () => {
  await fetchEnterpriseOptions();
  await fetchHeatFactoryOptions();
  await fetchHeatStationOptions();
});
</script>

<style scoped>
.pipeline-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 