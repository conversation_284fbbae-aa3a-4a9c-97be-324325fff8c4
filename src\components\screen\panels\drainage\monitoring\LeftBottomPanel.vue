<template>
  <PanelBox title="报警信息">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 报警数量统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner red"></span></span>
            <span class="stat-label">报警总数</span>
          </div>
          <span class="stat-value red-gradient">{{ statsData.total }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner orange"></span></span>
            <span class="stat-label">待处置</span>
          </div>
          <span class="stat-value orange-gradient">{{ statsData.unhandled }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner yellow"></span></span>
            <span class="stat-label">处置中</span>
          </div>
          <span class="stat-value yellow-gradient">{{ statsData.handling }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
            <span class="stat-label">已处置</span>
          </div>
          <span class="stat-value green-gradient">{{ statsData.handled }}</span>
        </div>
      </div>

      <!-- 报警列表区域 -->
      <div v-if="alarmList.length > 0">
        <ScrollTable
          :columns="tableColumns"
          :data="alarmList"
          :autoScroll="true"
          :scrollSpeed="2000"
          :tableHeight="tableHeight"
          :visibleRows="2"
          :hiddenHeader="true"
          @row-click="openDetailModal"
        >
          <template #custom="{ row }">
            <div class="alarm-row">
              <div class="alarm-main">
                <span class="alarm-type">报警类型：{{ row.type }}</span>
                <div class="alarm-level">
                  <span class="level-tag" :style="{ background: getLevelColor(row.level) }">{{ row.level }}</span>
                </div>
              </div>
              <div class="alarm-location">
                <img src="@/assets/images/screen/common/location.svg" alt="location" class="location-icon" />
                <span class="location-text">{{ row.location }}</span>
              </div>
              <div class="alarm-time">
                <span class="time-text">{{ row.time }}</span>
              </div>
              <div class="alarm-divider"></div>
            </div>
          </template>
        </ScrollTable>
      </div>
      <div v-else :style="{ height: tableHeight }">
        <NoData />
      </div>
    </div>

      <!-- 更多按钮移到列表下方 -->
      <div v-if="alarmList.length > 0" class="more-btn-container">
        <div class="more-btn" @click="openAlarmListModal">
          更多
        </div>
      </div>

    <!-- 报警列表弹窗 -->
    <AlarmListModal v-model="showAlarmListModal" />
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import moment from 'moment'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import NoData from '@/components/common/NoData.vue'
import AlarmListModal from './AlarmListModal.vue'
import { getMonitorAnalysisStatistics, getMonitorAnalysisStatisticsCondition } from '@/api/drainage'
import bus from "@/utils/mitt";

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = ref({
  total: 0,
  unhandled: 0,
  handling: 0,
  handled: 0
})

// 加载状态
const loading = ref(false)

// 报警列表数据
const alarmList = ref([])

// 表格配置
const tableColumns = [
  { title: '报警信息', dataIndex: 'custom', width: '100%' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  if (window.innerHeight === 910) {
    return '200px'
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '220px'
  } else if (window.innerWidth >= 2561) {
    return '260px'
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '210px'
  } else {
    return '180px'
  }
})

// 打开详情弹窗
const openDetailModal = (row) => {
  console.log('打开详情弹窗:', row.originalData.deviceId, row.originalData.deviceType)
  const params = {
    specialType: 'drainage', //燃气:gas; 排水:drainage; 供暖:heating; 桥梁:bridge;
    deviceId: row.originalData.deviceId, //ff8080819648936b0196496727e4022d
    deviceType: row.originalData.deviceType, //laserMethane等;
  }
  bus.emit('screenTableRowFocusToGisPoint', params)
}

// 获取时间范围对应的天数
const getTimeRangeDays = (range) => {
  const dayMap = {
    'week': 7,
    'month': 30,
    'year': 365
  }
  return dayMap[range] || 7
}

// 获取时间区间
const getTimeRange = (range) => {
  const days = getTimeRangeDays(range)
  const endDate = moment().format('YYYY-MM-DD')
  const startDate = moment().subtract(days, 'days').format('YYYY-MM-DD')
  return { startDate, endDate }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    loading.value = true
    const dayIndex = getTimeRangeDays(timeRange.value)
    const response = await getMonitorAnalysisStatistics(dayIndex)
    if (response.code === 200 && response.data) {
      statsData.value = {
        total: response.data.totalCount || 0,
        unhandled: response.data.unhandledCount || 0,
        handling: response.data.processingCount || 0,
        handled: response.data.handledCount || 0
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取报警列表数据
const fetchAlarmList = async () => {
  try {
    const { startDate, endDate } = getTimeRange(timeRange.value)
    const params = {
      pageNum: 1,
      pageSize: 9999,
      startDate,
      endDate
    }
    const response = await getMonitorAnalysisStatisticsCondition(params)
    if (response.code === 200 && response.data) {
      const records = response.data.records || []
      alarmList.value = records.map(item => ({
        type: item.deviceTypeName || '未知类型',
        level: item.alarmLevelName ? `${item.alarmLevelName}报警` : '未知级别',
        location: item.address || '未知位置',
        time: item.alarmTime || '',
        originalData: item // 保存原始数据用于详情查看
      }))
    }
  } catch (error) {
    console.error('获取报警列表失败:', error)
    alarmList.value = []
  }
}

// 获取报警等级对应的颜色
const getLevelColor = (level) => {
  const colorMap = {
    '一级报警': '#FB3737', // 红色
    '二级报警': '#FF6D28', // 橙色
    '三级报警': '#EAA01B', // 黄色
    '四级报警': '#3FD87C'  // 绿色
  }
  return colorMap[level] || '#3FD87C'
}

// 处理时间范围变化
const handleTimeChange = async (value) => {
  console.log('时间范围变更为:', value)
  timeRange.value = value
  await Promise.all([fetchStatistics(), fetchAlarmList()])
}

// 报警列表弹窗
const showAlarmListModal = ref(false)

// 打开报警列表弹窗
const openAlarmListModal = () => {
  showAlarmListModal.value = true
}

// 组件挂载时初始化数据
onMounted(() => {
  Promise.all([fetchStatistics(), fetchAlarmList()])
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px 0px;
}

.stat-item {
  display: flex;
  align-items: center;
  position: relative;
  gap: 8px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  margin-bottom: 2px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: relative;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.red {
  background: #FC4949;
}

.stat-dot-inner.orange {
  background: #FF6D28;
}

.stat-dot-inner.yellow {
  background: #FFC75A;
}

.stat-dot-inner.green {
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.red) {
  background: rgba(252, 73, 73, 0.4);
}

.stat-dot:has(.stat-dot-inner.orange) {
  background: rgba(255, 109, 40, 0.4);
}

.stat-dot:has(.stat-dot-inner.yellow) {
  background: rgba(255, 199, 90, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.red-gradient {
  background: linear-gradient(90deg, #FB3737 0%, #FEA6A6 100%);
  -webkit-background-clip: text;
}

.orange-gradient {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
}

.yellow-gradient {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
}

.green-gradient {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
}

/* 报警列表样式 */
.alarm-row {
  padding: 5px 0;
  width: 100%;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.3s;
}

.alarm-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.alarm-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.alarm-type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alarm-level {
  display: flex;
  flex-shrink: 0;
}

.level-tag {
  width: 60px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.alarm-location {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.location-icon {
  width: 12px;
  height: 12px;
}

.location-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alarm-time {
  display: flex;
  justify-content: flex-end;
  margin-top: 0px;
}

.time-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.8;
}

.alarm-divider {
  display: none;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
}

/* 更多按钮容器样式 */
.more-btn-container {
  position: absolute;
  display: flex;
  justify-content: center;
  padding: 5px 0;
  flex-shrink: 0;
  bottom: 0;
  right: 10px;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
}

.more-btn:hover {
  color: #66B8FF;
}
</style>