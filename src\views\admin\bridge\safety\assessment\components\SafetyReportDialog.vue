<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="safety-report-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告名称" prop="reportName">
            <el-input v-model="formData.reportName" placeholder="请输入报告名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报告编号" prop="reportCode">
            <el-input v-model="formData.reportCode" placeholder="请输入报告编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告类型" prop="reportType">
            <el-select v-model="formData.reportType" placeholder="请选择报告类型" class="w-full" @change="handleReportTypeChange">
              <el-option 
                v-for="item in reportTypeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传时间" prop="reportDate">
            <el-date-picker
              v-model="formData.reportDate"
              type="date"
              placeholder="请选择上传时间"
              class="w-full"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告对象" prop="reportObjectIds">
            <el-select 
              v-model="formData.reportObjectIds" 
              placeholder="请选择维护企业" 
              class="w-full" 
              multiple
              @change="handleMaintenanceEnterpriseChange"
            >
              <el-option 
                v-for="item in maintenanceEnterpriseOptions" 
                :key="item.id" 
                :label="item.enterpriseName" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁名称" prop="bridgeId">
            <el-select 
              v-model="formData.bridgeId" 
              placeholder="请选择桥梁" 
              class="w-full" 
              multiple
              @change="handleBridgeChange"
            >
              <el-option 
                v-for="item in bridgeOptions" 
                :key="item.id" 
                :label="item.bridgeName" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="文件附件" prop="fileUrl">
            <div class="file-upload-container">
              <!-- 文件上传按钮 -->
              <div class="upload-header">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :file-list="[]"
                  :disabled="mode === 'view' || fileList.length >= 3"
                  :show-file-list="false"
                  multiple
                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                >
                  <el-button 
                    type="primary" 
                    :disabled="mode === 'view' || fileList.length >= 3"
                  >
                    上传报告
                  </el-button>
                </el-upload>
                <span class="upload-tip">大小50M以内</span>
              </div>
              
              <!-- 文件列表表格 -->
              <div class="file-list-table" v-if="fileList.length > 0">
                <div class="table-header">
                  <div class="column file-name">文件名</div>
                  <div class="column file-size">大小</div>
                  <div class="column file-actions">操作</div>
                </div>
                <div 
                  class="table-row" 
                  v-for="(file, index) in fileList" 
                  :key="file.uid || index"
                >
                  <div class="column file-name">
                    <div class="file-info">
                      <el-icon class="file-icon">
                        <Document v-if="isDocumentFile(file.name)" />
                        <Picture v-else-if="isImageFile(file.name)" />
                        <Files v-else />
                      </el-icon>
                      <span class="file-name-text">{{ file.name }}</span>
                    </div>
                  </div>
                  <div class="column file-size">
                    {{ formatFileSize(file.size) }}
                  </div>
                  <div class="column file-actions">
                    <div class="action-buttons">
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileRemove(file, index)"
                        v-if="mode !== 'view'"
                      >
                        删除
                      </el-button>
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileDownload(file)"
                        v-if="file.url && file.status === 'success'"
                      >
                        下载
                      </el-button>
                      <div class="upload-status" v-if="file.status === 'uploading'">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>上传中</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files, Loading } from '@element-plus/icons-vue'
import moment from 'moment'
import {
  saveSafetyAssessReport,
  updateSafetyAssessReport,
  getBridgeBasicInfoList,
  getMaintenanceEnterpriseList,
} from '@/api/bridge'
import { SAFETY_REPORT_TYPE_OPTIONS } from '@/constants/bridge'
import { uploadFile } from '@/api/upload'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const uploadRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增/编辑',
    edit: '新增/编辑',
    view: '安全评估报告详情'
  }
  return titles[props.mode] || '安全评估报告'
})

// 下拉选项数据
const bridgeOptions = ref([])
const maintenanceEnterpriseOptions = ref([])
const reportTypeOptions = ref(SAFETY_REPORT_TYPE_OPTIONS)

// 文件列表
const fileList = ref([])

// 表单数据
const formData = reactive({
  id: '',
  reportName: '',
  reportCode: '',
  reportType: '',
  reportTypeName: '',
  reportDate: '',
  reportObjectIds: [],
  reportObjectNames: '',
  bridgeId: [],
  bridgeName: '',
  fileUrl: ''
})

// 表单验证规则
const formRules = {
  reportName: [{ required: true, message: '请输入报告名称', trigger: 'blur' }],
  reportCode: [{ required: true, message: '请输入报告编号', trigger: 'blur' }],
  reportType: [{ required: true, message: '请选择报告类型', trigger: 'change' }],
  reportDate: [{ required: true, message: '请选择上传时间', trigger: 'change' }],
  reportObjectIds: [{ required: true, message: '请选择报告对象', trigger: 'change' }],
  bridgeId: [{ required: true, message: '请选择桥梁', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'reportObjectIds' || key === 'bridgeId') {
      formData[key] = []
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        if (key === 'reportObjectIds' && typeof newVal[key] === 'string') {
          // 处理reportObjectIds字符串转数组
          formData[key] = newVal[key] ? newVal[key].split(',').map(id => parseInt(id)) : []
        } else if (key === 'bridgeId' && typeof newVal[key] === 'string') {
          // 处理bridgeId字符串转数组  
          formData[key] = newVal[key] ? newVal[key].split(',').map(id => parseInt(id)) : []
        } else if (key === 'bridgeId' && newVal.bridgeId) {
          // 兼容处理单个bridgeId字段
          formData[key] = [parseInt(newVal.bridgeId)]
        } else {
          formData[key] = newVal[key]
        }
      }
    })
    
    // 处理文件显示
    if (newVal.fileUrl) {
      const urls = newVal.fileUrl.split(',').filter(url => url.trim())
      const existingFiles = urls.map((url, index) => ({
        name: `attachment_${index + 1}${getFileExtension(url)}`,
        url: url.trim(),
        uid: Date.now() + index,
        status: 'success',
        size: 0
      }))
      
      if (props.mode === 'edit') {
        fileList.value = [...fileList.value, ...existingFiles]
      } else {
        fileList.value = existingFiles
      }
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 获取文件扩展名
const getFileExtension = (url) => {
  const match = url.match(/\.[^.]*$/)
  return match ? match[0] : ''
}

// 处理报告类型选择变化
const handleReportTypeChange = (value) => {
  const selected = reportTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.reportTypeName = selected.label
  }
}

// 处理桥梁选择变化
const handleBridgeChange = (values) => {
  const selectedItems = bridgeOptions.value.filter(item => values.includes(item.id))
  formData.bridgeName = selectedItems.map(item => item.bridgeName).join(',')
}

// 处理维护企业选择变化
const handleMaintenanceEnterpriseChange = (values) => {
  const selectedItems = maintenanceEnterpriseOptions.value.filter(item => values.includes(item.id))
  formData.reportObjectNames = selectedItems.map(item => item.enterpriseName).join(',')
}

// 处理文件变化
const handleFileChange = async (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 3) {
    ElMessage.warning('最多只能上传3个文件')
    return false
  }

  // 检查文件大小
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过 50MB!')
    return false
  }

  // 添加到文件列表，状态为上传中
  const fileItem = {
    name: file.name,
    size: file.size,
    uid: file.uid || Date.now(),
    status: 'uploading',
    raw: file,
    url: ''
  }
  
  fileList.value.push(fileItem)

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response && response.status === 200) {
      // 更新文件状态为成功
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value[index].url = response.data.url
        fileList.value[index].status = 'success'
      }
      
      // 更新formData中的fileUrl
      updateFileUrl()
      
      ElMessage.success('文件上传成功')
    } else {
      // 上传失败，移除文件
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value.splice(index, 1)
      }
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 上传失败，移除文件
    const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
const handleFileRemove = (file, index) => {
  fileList.value.splice(index, 1)
  updateFileUrl()
  ElMessage.success('文件已移除')
}

// 处理文件下载
const handleFileDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 更新文件URL字符串
const updateFileUrl = () => {
  nextTick(() => {
    const urls = fileList.value
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    formData.fileUrl = urls.join(',')
  })
}

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList()
    if (res && res.code === 200) {
      bridgeOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取桥梁列表失败:', error)
  }
}

// 获取维护企业列表
const fetchMaintenanceEnterpriseList = async () => {
  try {
    const res = await getMaintenanceEnterpriseList({})
    if (res && res.code === 200) {
      maintenanceEnterpriseOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取维护企业列表失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }
    
    // 处理报告对象IDs
    if (submitData.reportObjectIds && Array.isArray(submitData.reportObjectIds)) {
      submitData.reportObjectIds = submitData.reportObjectIds.join(',')
    }
    
    // 处理桥梁IDs
    if (submitData.bridgeId && Array.isArray(submitData.bridgeId)) {
      submitData.bridgeId = submitData.bridgeId.join(',')
    }
    
    // 格式化时间
    if (submitData.reportDate) {
      submitData.reportDate = moment(submitData.reportDate).format('YYYY-MM-DD HH:mm:ss')
    }

    let res
    if (props.mode === 'add') {
      res = await saveSafetyAssessReport(submitData)
    } else if (props.mode === 'edit') {
      res = await updateSafetyAssessReport(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBridgeList()
  fetchMaintenanceEnterpriseList()
})
</script>

<style scoped>
.safety-report-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

/* 文件列表表格样式 */
.file-list-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f5f7fa;
}

.column {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.file-name {
  flex: 1;
  min-width: 0;
}

.file-size {
  width: 100px;
  justify-content: center;
}

.file-actions {
  width: 120px;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
}

.upload-status .el-icon {
  font-size: 14px;
}

.upload-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 