<template>
  <teleport to="body">
    <transition name="fade">
      <div class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">专家详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          
          <!-- 专家详情内容区域 -->
          <div class="modal-content">
            <!-- Loading 效果 -->
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">数据加载中...</div>
            </div>
            
            <!-- 专家详情表单 -->
            <div v-else class="expert-detail-form">
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">专家姓名:</label>
                  <div class="form-value">{{ expertDetail.expertName || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">性别:</label>
                  <div class="form-value">{{ expertDetail.expertGender || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">年龄:</label>
                  <div class="form-value">{{ expertDetail.expertAge || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">联系电话:</label>
                  <div class="form-value">{{ expertDetail.contactInfo || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item full-width">
                  <label class="form-label">所属单位:</label>
                  <div class="form-value">{{ expertDetail.belongUnitName || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item full-width">
                  <label class="form-label">专业领域:</label>
                  <div class="form-value">{{ expertDetail.professionalField || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item full-width">
                  <label class="form-label">备注信息:</label>
                  <div class="form-value remarks">{{ expertDetail.remarks || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">创建时间:</label>
                  <div class="form-value">{{ formatTime(expertDetail.createTime) }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">更新时间:</label>
                  <div class="form-value">{{ formatTime(expertDetail.updateTime) }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">创建人:</label>
                  <div class="form-value">{{ expertDetail.createBy || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">更新人:</label>
                  <div class="form-value">{{ expertDetail.updateBy || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮区域 -->
          <div class="modal-footer">
            <button @click="closeModal" class="close-btn">关闭</button>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import moment from 'moment'
import { getEmergencyExpertDetailScreen } from '@/api/comprehensive.js'

// 定义组件属性
const props = defineProps({
  expertId: {
    type: String,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['close'])

// 响应式数据
const loading = ref(false)
const expertDetail = ref({})

/**
 * 关闭弹窗
 */
const closeModal = () => {
  emit('close')
}

/**
 * 获取专家详情数据
 */
const fetchExpertDetail = async () => {
  try {
    loading.value = true
    
    const response = await getEmergencyExpertDetailScreen(props.expertId)
    
    if (response.code === 200 && response.data) {
      expertDetail.value = response.data
      console.log('专家详情获取成功:', expertDetail.value)
    } else {
      console.error('获取专家详情失败:', response.message)
    }
  } catch (error) {
    console.error('获取专家详情异常:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 格式化时间
 * @param {string} time 时间字符串
 * @returns {string} 格式化后的时间
 */
const formatTime = (time) => {
  if (!time) return '-'
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载时获取数据
onMounted(() => {
  if (props.expertId) {
    fetchExpertDetail()
  }
})
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 600px;
  max-height: 80vh;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

/* 内容区域样式 */
.modal-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Loading 效果样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #3AA1FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-text {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表单样式 */
.expert-detail-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item.full-width {
  flex: 1;
}

.form-label {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.form-value {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: #FFFFFF;
  background-color: rgba(59, 141, 242, 0.1);
  border: 1px solid rgba(59, 141, 242, 0.3);
  border-radius: 4px;
  padding: 10px 12px;
  min-height: 20px;
  line-height: 1.4;
}

.form-value.remarks {
  min-height: 60px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 底部操作区域 */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 15px 20px;
  border-top: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.close-btn {
  padding: 8px 24px;
  background-color: #3AA1FF;
  border: none;
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: #66B8FF;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .modal-container {
    width: 90%;
    max-width: 500px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 15px;
  }
}
</style>