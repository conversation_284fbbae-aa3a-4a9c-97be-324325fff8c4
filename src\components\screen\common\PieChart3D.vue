<template>
  <div class="pie3d-container">
    <div v-if="totalVisible" class="total-container">
      <div class="total-value">{{ totalValue }}</div>
      <div class="total-label">总数</div>
    </div>
    <div class="e3d-container" ref="chartContainer"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'

export default {
  name: "PieChart3D",
  props: {
    data: {
      type: Array,
      required: true
    },
    height: {
      type: [String, Number],
      default: '240px'
    },
    internalDiameterRatio: {
      type: Number,
      default: 0.6
    },
    totalVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      selectedIndex: '',
      hoveredIndex: '',
      totalValue: 0
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.updateChart()
        this.bindEvents()
      }
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    updateChart() {
      if (!this.chart) return

      // 计算总数
      this.totalValue = this.data.reduce((sum, item) => sum + item.value, 0)

      const option = this.getPie3D(this.data, this.internalDiameterRatio)
      this.chart.setOption(option, true)
    },
    bindEvents() {
      // 监听点击事件，实现选中效果（单选）
      this.chart.on('click', (params) => {
        if (params.seriesName === '') return

        const option = this.chart.getOption()
        const seriesIndex = params.seriesIndex

        // 确保series和pieStatus存在
        if (!option.series[seriesIndex] || !option.series[seriesIndex].pieStatus) return

        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
        let isSelected = !option.series[seriesIndex].pieStatus.selected
        let isHovered = option.series[seriesIndex].pieStatus.hovered
        let k = option.series[seriesIndex].pieStatus.k
        let startRatio = option.series[seriesIndex].pieData.startRatio
        let endRatio = option.series[seriesIndex].pieData.endRatio

        // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
        if (this.selectedIndex !== '' && this.selectedIndex !== seriesIndex) {
          const prevSelected = option.series[this.selectedIndex]
          if (prevSelected && prevSelected.pieStatus) {
            prevSelected.parametricEquation = this.getParametricEquation(
              prevSelected.pieData.startRatio,
              prevSelected.pieData.endRatio,
              false,
              false,
              k,
              prevSelected.pieData.value
            )
            prevSelected.pieStatus.selected = false
          }
        }

        // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
        option.series[seriesIndex].parametricEquation = this.getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[seriesIndex].pieData.value
        )
        option.series[seriesIndex].pieStatus.selected = isSelected

        // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
        this.selectedIndex = isSelected ? seriesIndex : ''

        // 使用更新后的 option，渲染图表
        this.chart.setOption(option)
      })

      // 监听 mouseover，近似实现高亮（放大）效果
      this.chart.on('mouseover', (params) => {
        if (params.seriesName === '') return

        const option = this.chart.getOption()
        const seriesIndex = params.seriesIndex

        // 确保series存在
        if (!option.series[seriesIndex] || !option.series[seriesIndex].pieStatus) return

        // 如果触发 mouseover 的扇形当前已高亮，则不做操作
        if (this.hoveredIndex === seriesIndex) {
          return
        }

        // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
        if (this.hoveredIndex !== '') {
          const prevHovered = option.series[this.hoveredIndex]
          if (prevHovered && prevHovered.pieStatus) {
            const isSelected = prevHovered.pieStatus.selected
            const k = prevHovered.pieStatus.k
            const startRatio = prevHovered.pieData.startRatio
            const endRatio = prevHovered.pieData.endRatio

            // 对当前高亮的扇形，执行取消高亮操作
            prevHovered.parametricEquation = this.getParametricEquation(
              startRatio,
              endRatio,
              isSelected,
              false,
              k,
              prevHovered.pieData.value
            )
            prevHovered.pieStatus.hovered = false
          }
          this.hoveredIndex = ''
        }

        // 设置新的高亮扇形
        const isSelected = option.series[seriesIndex].pieStatus.selected
        const k = option.series[seriesIndex].pieStatus.k
        const startRatio = option.series[seriesIndex].pieData.startRatio
        const endRatio = option.series[seriesIndex].pieData.endRatio

        option.series[seriesIndex].parametricEquation = this.getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          true,
          k,
          option.series[seriesIndex].pieData.value
        )
        option.series[seriesIndex].pieStatus.hovered = true
        this.hoveredIndex = seriesIndex

        // 使用更新后的 option，渲染图表
        this.chart.setOption(option)
      })

      // 修正取消高亮失败的 bug
      this.chart.on('globalout', () => {
        const option = this.chart.getOption()

        if (this.hoveredIndex !== '') {
          const hoveredSeries = option.series[this.hoveredIndex]
          if (hoveredSeries && hoveredSeries.pieStatus) {
            const isSelected = hoveredSeries.pieStatus.selected
            const k = hoveredSeries.pieStatus.k
            const startRatio = hoveredSeries.pieData.startRatio
            const endRatio = hoveredSeries.pieData.endRatio

            hoveredSeries.parametricEquation = this.getParametricEquation(
              startRatio,
              endRatio,
              isSelected,
              false,
              k,
              hoveredSeries.pieData.value
            )
            hoveredSeries.pieStatus.hovered = false

            this.hoveredIndex = ''
            this.chart.setOption(option)
          }
        }
      })
    },
    // 生成扇形的曲面参数方程
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      // 计算
      const midRatio = (startRatio + endRatio) / 2
      const startRadian = startRatio * Math.PI * 2
      const endRadian = endRatio * Math.PI * 2
      const midRadian = midRatio * Math.PI * 2

      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false
      }

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== 'undefined' ? k : 1 / 3

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      const hoverRate = isHovered ? 1.05 : 1

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20
        },
        x: function (u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        y: function (u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u)
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
        }
      }
    },
    // 生成模拟 3D 饼图的配置项
    getPie3D(pieData, internalDiameterRatio) {
      const series = []
      let sumValue = 0
      let startValue = 0
      let endValue = 0
      const k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value

        const seriesItem = {
          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
          type: 'surface',
          coordinateSystem: 'cartesian3D',
          parametric: true,
          wireframe: {
            show: false
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k
          }
        }

        if (typeof pieData[i].itemStyle != 'undefined') {
          const itemStyle = {}
          typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null
          typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null
          seriesItem.itemStyle = itemStyle
        } else if (typeof pieData[i].color != 'undefined') {
          seriesItem.itemStyle = {
            color: pieData[i].color
          }
        }

        series.push(seriesItem)
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value

        series[i].pieData.startRatio = startValue / sumValue
        series[i].pieData.endRatio = endValue / sumValue
        series[i].parametricEquation = this.getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          false,
          k,
          series[i].pieData.value
        )

        startValue = endValue
      }

      // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
      series.push({
        name: '',
        type: 'surface',
        coordinateSystem: 'cartesian3D',
        parametric: true,
        wireframe: {
          show: false
        },
        itemStyle: {
          opacity: 0
        },
        parametricEquation: {
          u: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20
          },
          v: {
            min: 0,
            max: Math.PI,
            step: Math.PI / 20
          },
          x: function (u, v) {
            return Math.sin(v) * Math.sin(u) + Math.sin(u)
          },
          y: function (u, v) {
            return Math.sin(v) * Math.cos(u) + Math.cos(u)
          },
          z: function (u, v) {
            return Math.cos(v) > 0 ? 0.1 : -0.1
          }
        }
      })

      // 准备待返回的配置项，把准备好的 series 传入。
      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          formatter: params => {
            if (params.seriesName !== '' && params.seriesIndex >= 0) {
              return `${params.seriesName}<br/>${series[params.seriesIndex].pieData.value}%`;
            }
          },
          confine: true,
          position: function (point, params, dom, rect, size) {
            // 确保tooltip不会被遮挡
            return [point[0] + 10, point[1] - 50]
          },
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          backgroundColor: 'rgba(0, 19, 40, 0.8)',
          borderColor: 'rgba(0, 109, 232, 0.2)',
        },
        xAxis3D: {
          type: 'value',
          min: -1,
          max: 1,
          show: false
        },
        yAxis3D: {
          type: 'value',
          min: -1,
          max: 1,
          show: false
        },
        zAxis3D: {
          type: 'value',
          min: -1,
          max: 1,
          show: false
        },
        grid3D: {
          show: false,
          boxHeight: 22,
          //   environment: '#000',
          left: '10%',
          right: 0,
          top: -5,
          bottom: 5,
          viewControl: {
            alpha: 25,
            beta: 5,
            distance: 200,
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false
          },
          postEffect: {
            enable: true,
            bloom: {
              enable: true,
              bloomIntensity: 0.2
            },
            SSAO: {
              enable: true,
              quality: 'medium',
              radius: 2
            }
          },
          light: {
            main: {
              intensity: 2,
              shadow: true
            },
            ambient: {
              intensity: 0.3
            },
            ambientCubemap: {
              texture: null,
              exposure: 1,
              diffuseIntensity: 0.6,
              specularIntensity: 1.5
            }
          }
        },
        series: series
      }

      return option
    }
  }
}
</script>

<style scoped>
.pie3d-container {
  width: 100%;
  height: v-bind(height);
  position: relative;
}

.e3d-container {
  width: 100%;
  height: 100%;
  margin-bottom: 20px;
}

.total-container {
  position: absolute;
  top: 39%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 100;
}

.total-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
  line-height: 26px;
}

.total-label {
  font-family: 'PingFangSC', 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  margin-top: 4px;
}
</style>