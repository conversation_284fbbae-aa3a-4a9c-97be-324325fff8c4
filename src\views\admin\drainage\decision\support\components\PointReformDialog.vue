<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="point-reform-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="易涝点编号" prop="pointId">
            <el-input-number v-model="formData.pointId" placeholder="请输入易涝点编号" class="w-full" :min="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="改造类型" prop="reformType">
            <el-select v-model="formData.reformType" placeholder="请选择" class="w-full" @change="handleReformTypeChange">
              <el-option v-for="item in reformTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="改造方案" prop="reformScheme">
            <el-input v-model="formData.reformScheme" type="textarea" :rows="4" placeholder="请输入改造方案详情" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  savePointReformScheme,
  updatePointReformScheme
} from '@/api/drainage';
import { POINT_REFORM_TYPE_OPTIONS } from '@/constants/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增易涝点改造方案',
    edit: '编辑易涝点改造方案',
    view: '易涝点改造方案详情'
  };
  return titles[props.mode] || '易涝点改造方案';
});

// 下拉选项数据
const reformTypeOptions = POINT_REFORM_TYPE_OPTIONS;

// 表单数据
const formData = reactive({
  id: 0,
  pointId: 0,
  reformType: 0,
  reformTypeName: '',
  reformScheme: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
});

// 表单验证规则
const formRules = {
  pointId: [{ required: true, message: '请输入易涝点编号', trigger: 'blur' }],
  reformType: [{ required: true, message: '请选择改造类型', trigger: 'change' }],
  reformScheme: [{ required: true, message: '请输入改造方案', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'id' || key === 'pointId' || key === 'reformType') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理改造类型变化
const handleReformTypeChange = (value) => {
  const selected = reformTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.reformTypeName = selected.label;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 设置改造类型名称
    const selected = reformTypeOptions.find(item => item.value === formData.reformType);
    if (selected) {
      formData.reformTypeName = selected.label;
    }

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await savePointReformScheme(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePointReformScheme(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.point-reform-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 