import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAlarmStore = defineStore('alarm', () => {
  // 全局报警消息列表
  const alarmMessages = ref([])
  
  // 是否显示全局报警弹窗
  const showGlobalAlarmModal = ref(false)
  
  // 最大保存的报警消息数量
  const maxMessages = 100
  
  // 添加报警消息
  const addAlarmMessage = (message) => {
    // 确保消息有唯一ID
    if (!message.id) {
      message.id = `alarm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
    
    // 添加到列表开头
    alarmMessages.value.unshift({
      ...message,
      isRead: false,
      receivedAt: new Date().toISOString()
    })
    
    // 限制消息数量
    if (alarmMessages.value.length > maxMessages) {
      alarmMessages.value = alarmMessages.value.slice(0, maxMessages)
    }
    
    // 自动显示全局弹窗
    showGlobalAlarmModal.value = true
    
    console.log('添加报警消息到store:', message)
  }
  
  // 标记消息为已读
  const markAsRead = (messageId) => {
    const message = alarmMessages.value.find(msg => msg.id === messageId)
    if (message) {
      message.isRead = true
    }
  }
  
  // 标记所有消息为已读
  const markAllAsRead = () => {
    alarmMessages.value.forEach(msg => {
      msg.isRead = true
    })
  }
  
  // 删除消息
  const removeMessage = (messageId) => {
    const index = alarmMessages.value.findIndex(msg => msg.id === messageId)
    if (index > -1) {
      alarmMessages.value.splice(index, 1)
    }
  }
  
  // 清空所有消息
  const clearAllMessages = () => {
    alarmMessages.value = []
  }
  
  // 关闭全局弹窗
  const closeGlobalModal = () => {
    showGlobalAlarmModal.value = false
  }
  
  // 手动显示全局弹窗
  const openGlobalModal = () => {
    showGlobalAlarmModal.value = true
  }
  
  // 计算属性：未读消息数量
  const unreadCount = computed(() => {
    return alarmMessages.value.filter(msg => !msg.isRead).length
  })
  
  // 计算属性：最新消息
  const latestMessage = computed(() => {
    return alarmMessages.value.length > 0 ? alarmMessages.value[0] : null
  })
  
  // 计算属性：按专项分组的消息
  const messagesByType = computed(() => {
    const grouped = {}
    alarmMessages.value.forEach(msg => {
      const type = getSpecialTypeByType(msg.type)
      if (!grouped[type]) {
        grouped[type] = []
      }
      grouped[type].push(msg)
    })
    return grouped
  })
  
  // 根据类型获取专项类型
  const getSpecialTypeByType = (type) => {
    if (type === 'gas_10') {
      return 'gas'
    } else if (type === 'drain_10') {
      return 'drainage'
    } else if (type === 'head_10') {
      return 'heating'
    } else if (type === 'bridge_10') {
      return 'bridge'
    }
    return null
  }
  
  // 获取专项中文名称
  const getSpecialTypeName = (type) => {
    const typeNames = {
      gas: '燃气专项',
      drainage: '排水专项',
      heating: '供热专项',
      bridge: '桥梁专项',
      comprehensive: '综合专项'
    }
    return typeNames[type] || '未知专项'
  }
  
  return {
    // 状态
    alarmMessages,
    showGlobalAlarmModal,
    
    // 计算属性
    unreadCount,
    latestMessage,
    messagesByType,
    
    // 方法
    addAlarmMessage,
    markAsRead,
    markAllAsRead,
    removeMessage,
    clearAllMessages,
    closeGlobalModal,
    openGlobalModal,
    getSpecialTypeByType,
    getSpecialTypeName
  }
})