<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="plan-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预案标题" prop="schemeTitle">
            <el-input v-model="formData.schemeTitle" placeholder="请输入预案标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="索引号" prop="indexCode">
            <el-input v-model="formData.indexCode" placeholder="请输入索引号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full" @change="handleRelatedBusinessChange">
              <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="匹配预警类型" prop="relatedWarningType">
            <el-select v-model="formData.relatedWarningType" placeholder="请选择" class="w-full" @change="handleWarningTypeChange">
              <el-option v-for="item in warningTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预案级别" prop="schemeLevel">
            <el-select v-model="formData.schemeLevel" placeholder="请选择" class="w-full" @change="handleSchemeLevelChange">
              <el-option v-for="item in schemeLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预案类别" prop="schemeType">
            <el-select v-model="formData.schemeType" placeholder="请选择" class="w-full" @change="handleSchemeTypeChange">
              <el-option v-for="item in schemeTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="主题分类" prop="themeType">
            <el-select v-model="formData.themeType" placeholder="请选择" class="w-full" @change="handleThemeTypeChange">
              <el-option v-for="item in themeTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主题词" prop="themeWord">
            <el-input v-model="formData.themeWord" placeholder="请输入主题词" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布机构" prop="issuedUnit">
            <el-input v-model="formData.issuedUnit" placeholder="请输入发布机构" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布时间" prop="issuedTime">
            <el-date-picker
              v-model="formData.issuedTime"
              type="datetime"
              placeholder="请选择发布时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="失效状态" prop="schemeStatus">
            <el-select v-model="formData.schemeStatus" placeholder="请选择" class="w-full" @change="handleSchemeStatusChange">
              <el-option v-for="item in schemeStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="适用对象" prop="applyObject">
            <el-input v-model="formData.applyObject" placeholder="请输入适用对象" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件" prop="fileUrls">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              list-type="text"
              :limit="10"
              :disabled="mode === 'view'"
              multiple
            >
              <el-button type="primary" :disabled="mode === 'view'">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持 doc、docx、pdf 等格式文件上传，最多支持10个文件，大小不超过100M
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveEmergencyScheme,
  updateEmergencyScheme
} from '@/api/comprehensive';
import { uploadFile } from '@/api/upload';
import {
  RELATED_BUSINESS_OPTIONS,
  EMERGENCY_WARNING_TYPE_OPTIONS,
  SCHEME_LEVEL_OPTIONS,
  SCHEME_TYPE_OPTIONS,
  THEME_TYPE_OPTIONS,
  SCHEME_STATUS_OPTIONS
} from '@/constants/comprehensive';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增应急预案',
    edit: '编辑应急预案',
    view: '应急预案详情'
  };
  return titles[props.mode] || '应急预案';
});

// 下拉选项数据
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS);
const warningTypeOptions = ref(EMERGENCY_WARNING_TYPE_OPTIONS);
const schemeLevelOptions = ref(SCHEME_LEVEL_OPTIONS);
const schemeTypeOptions = ref(SCHEME_TYPE_OPTIONS);
const themeTypeOptions = ref(THEME_TYPE_OPTIONS);
const schemeStatusOptions = ref(SCHEME_STATUS_OPTIONS);

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  schemeTitle: '',
  indexCode: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  relatedWarningType: '',
  relatedWarningTypeName: '',
  schemeLevel: '',
  schemeLevelName: '',
  schemeType: '',
  schemeTypeName: '',
  themeType: '',
  themeTypeName: '',
  themeWord: '',
  issuedUnit: '',
  issuedTime: '',
  schemeStatus: '',
  schemeStatusName: '',
  applyObject: '',
  remarks: '',
  fileUrls: ''
});

// 表单验证规则
const formRules = {
  schemeTitle: [{ required: true, message: '请输入预案标题', trigger: 'blur' }],
  indexCode: [{ required: true, message: '请输入索引号', trigger: 'blur' }],
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  relatedWarningType: [{ required: true, message: '请选择匹配预警类型', trigger: 'change' }],
  schemeLevel: [{ required: true, message: '请选择预案级别', trigger: 'change' }],
  schemeType: [{ required: true, message: '请选择知识类别', trigger: 'change' }],
  themeType: [{ required: true, message: '请选择主题分类', trigger: 'change' }],
  issuedUnit: [{ required: true, message: '请输入发布机构', trigger: 'blur' }],
  issuedTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }],
  schemeStatus: [{ required: true, message: '请选择失效状态', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = '';
    } else {
      formData[key] = '';
    }
  });
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理文件列表显示
    if (newVal.fileUrls) {
      const urls = newVal.fileUrls.split(',').filter(url => url.trim());
      fileList.value = urls.map((url, index) => ({
        name: url.split('/').pop() || `文件${index + 1}`,
        url: url,
        uid: Date.now() + index
      }));
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理所属行业变化
const handleRelatedBusinessChange = (value) => {
  const selected = relatedBusinessOptions.value.find(item => item.value === value);
  if (selected) {
    formData.relatedBusinessName = selected.label;
  }
};

// 处理预警类型变化
const handleWarningTypeChange = (value) => {
  const selected = warningTypeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.relatedWarningTypeName = selected.label;
  }
};

// 处理预案级别变化
const handleSchemeLevelChange = (value) => {
  const selected = schemeLevelOptions.value.find(item => item.value === value);
  if (selected) {
    formData.schemeLevelName = selected.label;
  }
};

// 处理知识类别变化
const handleSchemeTypeChange = (value) => {
  const selected = schemeTypeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.schemeTypeName = selected.label;
  }
};

// 处理主题分类变化
const handleThemeTypeChange = (value) => {
  const selected = themeTypeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.themeTypeName = selected.label;
  }
};

// 处理失效状态变化
const handleSchemeStatusChange = (value) => {
  const selected = schemeStatusOptions.value.find(item => item.value === value);
  if (selected) {
    formData.schemeStatusName = selected.label;
  }
};

// 文件选择变化处理
const handleFileChange = async (file, fileListParam) => {
  // 检查文件大小
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage.error('上传文件大小不能超过 100MB!');
    return;
  }

  // 检查文件格式
  const validFormats = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const isValidFormat = validFormats.includes(file.raw.type) || file.name.match(/\.(doc|docx|pdf)$/i);
  if (!isValidFormat) {
    ElMessage.error('只支持 doc、docx、pdf 格式的文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.fileUrls ? formData.fileUrls.split(',').filter(url => url.trim()) : [];
      urls.push(response.data.url);
      formData.fileUrls = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 文件移除处理
const handleFileRemove = (file, fileListParam) => {
  if (formData.fileUrls) {
    const urls = formData.fileUrls.split(',').filter(url => url.trim());
    const index = urls.findIndex(url => url === file.url);
    if (index > -1) {
      urls.splice(index, 1);
      formData.fileUrls = urls.join(',');
    }
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveEmergencyScheme(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEmergencyScheme(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.plan-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 