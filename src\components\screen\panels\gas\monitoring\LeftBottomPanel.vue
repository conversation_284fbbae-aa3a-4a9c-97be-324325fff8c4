<template>
  <PanelBox title="监测报警" class="gas-monitoring-left-bottom-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="alarm-stats">
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer red-dot"></div>
            <div class="dot-inner red-dot"></div>
          </div>
          <div class="stat-label">报警总数</div>
          <div class="stat-value red-value">{{ alarmStats.totalCount }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer green-dot"></div>
            <div class="dot-inner green-dot"></div>
          </div>
          <div class="stat-label">已处置</div>
          <div class="stat-value green-value">{{ alarmStats.handledCount }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer orange-dot"></div>
            <div class="dot-inner orange-dot"></div>
          </div>
          <div class="stat-label">处置中</div>
          <div class="stat-value orange-value">{{ alarmStats.processingCount }}</div>
        </div>
        <div class="stat-item">
          <div class="dot-wrapper">
            <div class="dot-outer deep-orange-dot"></div>
            <div class="dot-inner deep-orange-dot"></div>
          </div>
          <div class="stat-label">未处置</div>
          <div class="stat-value deep-orange-value">{{ alarmStats.unhandledCount }}</div>
        </div>
      </div>

      <!-- 使用滚动表格组件 -->
      <template v-if="alarmList.length > 0">
        <ScrollTable :columns="tableColumns" :data="alarmList" :autoScroll="true" :scrollSpeed="3000"
          :tableHeight="tableHeight" :visibleRows="visibleRows" @row-click="openDetailModal">
          <!-- 自定义等级列 -->
          <template #alarmLevel="{ row }">
            <div class="level-icon-wrapper" :title="row.alarmLevel">
              <!-- <SvgIcon :raw="getAlarmLevelIcon(row.alarmLevel)" :color="getAlarmLevelColor(row.alarmLevel)"
                size="16px" /> -->
                <span :style="{ color: getAlarmLevelColor(row.alarmLevel) }">{{ row.alarmLevelName }}</span>
            </div>
          </template>

          <!-- 自定义编号列 -->
          <template #alarmCode="{ row }">
            <span class="text-ellipsis" :title="row.alarmCode">{{ row.alarmCode }}</span>
          </template>

          <!-- 自定义报警来源列 -->
          <template #alarmSource="{ row }">
            <span class="text-ellipsis" :title="row.alarmSource">{{ row.alarmSource }}</span>
          </template>

          <!-- 自定义设备名称列 -->
          <template #deviceName="{ row }">
            <span class="text-ellipsis" :title="row.deviceName">{{ row.deviceName }}</span>
          </template>

          <!-- 自定义状态列 -->
          <template #handleStatusName="{ row }">
            <span :class="getStatusClass(row.handleStatusName)" :title="row.handleStatusName">{{ row.handleStatusName
              }}</span>
          </template>

          <!-- 自定义位置列 -->
          <template #address="{ row }">
            <div class="location-wrapper" :title="row.address">
              <SvgIcon :raw="locationIconSvg" color="#FF6D28" size="16px" />
            </div>
          </template>
        </ScrollTable>
      </template>
      <template v-else>
        <div :style="{ height: '11rem' }">
          <NoData />
        </div>
      </template>
      <!-- 更多按钮移到列表下方 -->
      <div v-if="alarmList.length > 0" class="more-btn-container">
        <div class="more-btn" @click="openAlarmListModal">
          更多
        </div>
      </div>
    </div>

    <!-- 设备详情弹窗 -->
    <GasMonitorDetailModal v-model="showDetailModal" :alarmId="selectedAlarmId" :deviceId="selectedDeviceId" />

    <!-- 报警列表弹窗 -->
    <AlarmListModal v-model="showAlarmListModal" />
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import GasMonitorDetailModal from './GasMonitorDetailModal.vue'
import AlarmListModal from './AlarmListModal.vue'
import NoData from '@/components/common/NoData.vue'
import { getMonitorAnalysisStatistics } from '@/api/gas'
import bus from "@/utils/mitt";
// 时间选择
const timeRange = ref('month')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到dayIndex
const timeRangeMap = {
  week: 7,
  month: 30,
  year: 365
}

// 表格列配置
const tableColumns = [
  { title: '等级', dataIndex: 'alarmLevel', width: '12%', fontSize: '13px' },
  { title: '编号', dataIndex: 'alarmCode', width: '15%', fontSize: '13px' },
  { title: '报警来源', dataIndex: 'alarmSource', width: '18%', fontSize: '13px' },
  { title: '设备名称', dataIndex: 'deviceName', width: '24%', fontSize: '13px' },
  { title: '处置状态', dataIndex: 'handleStatusName', width: '16%', fontSize: '13px', className: 'status-column' },
  { title: '位置', dataIndex: 'address', width: '15%', fontSize: '13px' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  return '200px' // 可以根据不同分辨率动态调整
})

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight)
// 全屏状态响应式变量
const isFullscreen = ref(false)

// 检查全屏状态
const checkFullscreen = () => {
  const fullscreenElement = document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  return !!fullscreenElement
}

// 动态计算可见行数
const visibleRows = computed(() => {
  // 在1920px时显示6行，其他情况显示4行
  const threshold = 1080
  return windowHeight.value >= threshold ? 6 : 4
})

// 窗口大小变化监听
const handleResize = () => {
  windowHeight.value = window.innerHeight
  isFullscreen.value = checkFullscreen()
}

// 全屏状态变化监听
const handleFullscreenChange = () => {
  isFullscreen.value = checkFullscreen()
  windowHeight.value = window.innerHeight
}

// 位置图标SVG
const locationIconSvg = `<svg viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M6.5 0C2.91 0 0 2.91 0 6.5C0 11.375 6.5 16 6.5 16C6.5 16 13 11.375 13 6.5C13 2.91 10.09 0 6.5 0ZM6.5 8.8C5.235 8.8 4.2 7.765 4.2 6.5C4.2 5.235 5.235 4.2 6.5 4.2C7.765 4.2 8.8 5.235 8.8 6.5C8.8 7.765 7.765 8.8 6.5 8.8Z" fill="currentColor"/>
</svg>`

// 报警等级图标SVG
const getAlarmLevelIcon = (level) => {
  // 使用通用的报警器图标，根据等级变换颜色
  return `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0ZM8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C11.5899 1.5 14.5 4.41015 14.5 8C14.5 11.5899 11.5899 14.5 8 14.5Z" fill="currentColor"/>
    <path d="M7.25 3.5V8.75H10.75V7.25H8.75V3.5H7.25Z" fill="currentColor"/>
  </svg>`
}

// 根据报警等级获取颜色
const getAlarmLevelColor = (level) => {
  switch (level) {
    case '9101': // 一级报警 - 红色
      return '#FC4949'
    case '9102': // 二级报警 - 橙色
      return '#FF6D28'
    case '9103': // 三级报警 - 黄色
      return '#FFC75A'
    case '9104': // 四级报警 - 蓝色
      return '#3B82F6'
    default:
      return '#FFFFFF'
  }
}

// 报警数据
const alarmList = ref([])

// 报警统计数据
const alarmStats = ref({
  totalCount: 0,
  handledCount: 0,
  processingCount: 0,
  unhandledCount: 0
})

// 获取处置状态对应的样式类名
const getStatusClass = (status) => {
  // 如果是中文状态名称，直接根据名称判断
  if (typeof status === 'string') {
    switch (status) {
      case '待确认':
        return 'status-pending'
      case '已处置':
        return 'status-resolved'
      case '处置中':
        return 'status-processing'
      case '待处置':
        return 'status-pending'
      case '误报':
        return 'status-resolved'
      case '已归档':
        return 'status-resolved'
      default:
        return ''
    }
  }

  // 如果是数字代码，根据代码判断
  switch (status) {
    case 9201: // 待确认
    case '9201':
      return 'status-pending'
    case 9202: // 误报
    case '9202':
      return 'status-resolved'
    case 9203: // 待处置
    case '9203':
      return 'status-pending'
    case 9204: // 处置中
    case '9204':
      return 'status-processing'
    case 9205: // 已处置
    case '9205':
      return 'status-resolved'
    case 9206: // 已归档
    case '9206':
      return 'status-resolved'
    default:
      return ''
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  fetchAlarmData()
}

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    const dayIndex = timeRangeMap[timeRange.value] || 30
    const response = await getMonitorAnalysisStatistics({ dayIndex, pageNum: 1, pageSize: 10 })
    if (response.code === 200) {
      const { totalCount, handledCount, processingCount, unhandledCount, alarms } = response.data

      // 更新统计数据
      alarmStats.value = {
        totalCount,
        handledCount,
        processingCount,
        unhandledCount
      }

      // 更新报警列表数据
      alarmList.value = alarms.records || []
    }
  } catch (error) {
    console.error('获取报警数据失败:', error)
  }
}

// 弹窗相关状态
const showDetailModal = ref(false)
const selectedAlarmId = ref('')
const selectedDeviceId = ref('')

// 打开详情弹窗
const openDetailModal = (row) => {
  console.log('打开详情弹窗1:', row)
  const params = {
    specialType: 'gas', //燃气:gas; 排水:drainage; 供暖:heating; 桥梁:bridge;
    deviceId: row.deviceId, //ff8080819648936b0196496727e4022d
    deviceType: row.deviceType, //laserMethane等;
  }
  bus.emit('screenTableRowFocusToGisPoint', params)
}

// 报警列表弹窗
const showAlarmListModal = ref(false)

// 打开报警列表弹窗
const openAlarmListModal = () => {
  showAlarmListModal.value = true
}

// 监听时间范围变化
watch(timeRange, () => {
  fetchAlarmData()
})

onMounted(() => {
  fetchAlarmData()
  // 初始化全屏状态
  isFullscreen.value = checkFullscreen()
  // 添加窗口resize事件监听
  window.addEventListener('resize', handleResize)
  // 添加全屏状态变化监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
})
</script>

<style scoped>
.gas-monitoring-left-bottom-panel {
  height: 350px;
  /* 调整高度以容纳更多按钮 */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 报警统计样式 */
.alarm-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  gap: 6px;
  align-items: center;
}

.dot-wrapper {
  position: relative;
  width: 9px;
  height: 9px;
}

.dot-outer {
  position: absolute;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.dot-inner {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
}

/* 红色圆点 */
.red-dot.dot-outer {
  background: rgba(252, 73, 73, 0.4);
}

.red-dot.dot-inner {
  background: #FC4949;
}

/* 绿色圆点 */
.green-dot.dot-outer {
  background: rgba(63, 216, 124, 0.4);
}

.green-dot.dot-inner {
  background: #3FD87C;
}

/* 橙色圆点 */
.orange-dot.dot-outer {
  background: rgba(255, 199, 90, 0.4);
}

.orange-dot.dot-inner {
  background: #FFC75A;
}

/* 深橙色圆点 */
.deep-orange-dot.dot-outer {
  background: rgba(255, 109, 40, 0.4);
}

.deep-orange-dot.dot-inner {
  background: #FF6D28;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
}

.red-value {
  background: linear-gradient(90deg, #FB3737 0%, #FEA6A6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.green-value {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.orange-value {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.deep-orange-value {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 等级图标样式 */
.level-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* 位置图标样式 */
.location-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* 文本省略样式 */
.text-ellipsis {
  min-width: 66px;
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

/* 状态样式 */
.status-pending {
  width: 60px;
  color: #FF6D28;
}

.status-resolved {
  width: 60px;
  color: #3FD87C;
}

.status-processing {
  width: 60px;
  color: #FFC75A;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-left-bottom-panel {
    height: 370px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-left-bottom-panel {
    height: 350px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-left-bottom-panel {
    height: 410px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .gas-monitoring-left-bottom-panel {
    height: 320px;
  }

  .panel-content {
    padding: 10px;
    gap: 0px;
  }

  .alarm-stats {
    gap: 8px;
  }

  .alarm-stat-item {
    padding: 8px;
  }

  .alarm-stat-title {
    font-size: 13px;
    margin-bottom: 3px;
  }

  .alarm-stat-value {
    font-size: 18px;
  }

  .data-table {
    margin-top: -5px;
  }
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 更多按钮容器样式 */
.more-btn-container {
  position: absolute;
  display: flex;
  justify-content: center;
  padding: 5px 0;
  flex-shrink: 0;
  bottom: 0;
  right: 10px;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 时间选择器样式 */
.com-select {
  margin-right: 20px;
}

/* 移除旧的更多按钮样式 */
.risk-more-container {
  display: none;
}

.risk-more-btn {
  display: none;
}
</style>