<template>
  <div class="maintain-data-container">
    <!-- 搜索区域 -->
    <div class="maintain-data-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属桥梁:</span>
          <el-select v-model="formData.bridgeId" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in bridgeOptions" :key="item.id" :label="item.bridgeName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">维修日期:</span>
          <el-select v-model="formData.maintainDateRange" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option label="近一周" value="week" />
            <el-option label="近一月" value="month" />
            <el-option label="近三月" value="quarter" />
            <el-option label="近一年" value="year" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.maintainName" class="form-input" placeholder="输入维修名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="maintainName" label="维修名称" min-width="120" />
      <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" />
      <el-table-column prop="maintainUnit" label="实施单位" min-width="120" />
      <el-table-column label="维修时间" min-width="120">
        <template #default="{ row }">
          {{ formatDate(row.maintainDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="maintainResult" label="维修结果" min-width="150" show-overflow-tooltip />
      <el-table-column label="操作" fixed="right" min-width="200">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
            <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            <el-button type="primary" link @click.stop="handleDownload(row)" v-if="row.fileUrl">附件下载</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <MaintainDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus'
import moment from 'moment'
import { 
  getMaintainRecordPage, 
  deleteMaintainRecord, 
  getMaintainRecordDetail,
  getBridgeBasicInfoList
} from '@/api/bridge'
import MaintainDialog from './components/MaintainDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 下拉选项数据
const bridgeOptions = ref([])

// 表单数据
const formData = ref({
  bridgeId: '',
  maintainDateRange: '',
  maintainName: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return moment(dateString).format('YYYY-MM-DD')
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchMaintainData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    bridgeId: '',
    maintainDateRange: '',
    maintainName: ''
  }
  currentPage.value = 1
  fetchMaintainData()
}

// 获取维修养护分页数据
const fetchMaintainData = async () => {
  try {
    loading.value = true
    const params = {
      bridgeId: formData.value.bridgeId,
      maintainName: formData.value.maintainName
    }
    
    // 处理日期范围查询
    if (formData.value.maintainDateRange) {
      const now = moment()
      let startDate = ''
      
      switch (formData.value.maintainDateRange) {
        case 'week':
          startDate = now.subtract(7, 'days').format('YYYY-MM-DD')
          break
        case 'month':
          startDate = now.subtract(1, 'month').format('YYYY-MM-DD')
          break
        case 'quarter':
          startDate = now.subtract(3, 'month').format('YYYY-MM-DD')
          break
        case 'year':
          startDate = now.subtract(1, 'year').format('YYYY-MM-DD')
          break
      }
      
      if (startDate) {
        params.startDate = startDate
        params.endDate = moment().format('YYYY-MM-DD')
      }
    }
    
    const res = await getMaintainRecordPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取维修养护数据失败:', error)
    ElMessage.error('获取维修养护数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList()
    if (res && res.code === 200) {
      bridgeOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取桥梁列表失败:', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchMaintainData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchMaintainData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getMaintainRecordDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取维修记录详情失败')
    }
  } catch (error) {
    console.error('获取维修记录详情失败:', error)
    ElMessage.error('获取维修记录详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getMaintainRecordDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取维修记录详情失败')
    }
  } catch (error) {
    console.error('获取维修记录详情失败:', error)
    ElMessage.error('获取维修记录详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该维修记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteMaintainRecord(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchMaintainData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除维修记录失败:', error)
      ElMessage.error('删除维修记录失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理附件下载
const handleDownload = (row) => {
  if (row.fileUrl) {
    const urls = row.fileUrl.split(',').filter(url => url.trim())
    if (urls.length === 1) {
      // 单个文件直接下载
      const link = document.createElement('a')
      link.href = urls[0].trim()
      link.download = `${row.maintainName}_附件`
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else if (urls.length > 1) {
      // 多个文件提示用户选择
      const fileNames = urls.map((url, index) => `附件${index + 1}`)
      ElMessageBox.confirm(
        `该记录包含${urls.length}个附件文件，是否全部下载？`,
        '附件下载',
        {
          confirmButtonText: '全部下载',
          cancelButtonText: '取消',
          type: 'info'
        }
      ).then(() => {
        urls.forEach((url, index) => {
          setTimeout(() => {
            const link = document.createElement('a')
            link.href = url.trim()
            link.download = `${row.maintainName}_附件${index + 1}`
            link.target = '_blank'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }, index * 100) // 延迟下载避免浏览器阻止
        })
      })
    }
  } else {
    ElMessage.warning('该记录没有附件文件')
  }
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchMaintainData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const container = document.querySelector('.maintain-data-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const searchSection = container.querySelector('.maintain-data-search');
    const headerSection = container.querySelector('.table-header');
    const paginationContainer = container.querySelector('.pagination-container');

    const searchHeight = searchSection ? searchSection.offsetHeight : 0;
    const headerHeight = headerSection ? headerSection.offsetHeight : 0;
    const paginationHeight = paginationContainer ? paginationContainer.offsetHeight : 0;
    
    const containerPadding = 16 * 2;
    const margins = 16 + 16;

    const otherElementsHeight = searchHeight + headerHeight + paginationHeight + containerPadding + margins;
    
    const containerRect = container.getBoundingClientRect();
    
    tableMaxHeight.value = window.innerHeight - containerRect.top - otherElementsHeight;
  });
};

const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchBridgeList(),
      fetchMaintainData()
    ])
    calculateTableMaxHeight();
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.maintain-data-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.maintain-data-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
