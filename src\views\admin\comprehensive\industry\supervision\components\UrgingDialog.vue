<template>
  <el-dialog
    v-model="dialogVisible"
    title="催办"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="urging-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="催办部门" prop="urgingUnit">
            <el-tree-select
              v-model="formData.urgingUnit"
              :data="departmentOptions"
              :props="deptSelectProps"
              placeholder="请选择催办部门"
              check-strictly
              class="w-full"
              @change="handleDepartmentChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="更新时间" prop="updateTime">
            <el-date-picker
              v-model="formData.updateTime"
              type="datetime"
              placeholder="请选择更新时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="推送方式" prop="notifyType">
            <el-checkbox v-model="formData.notifySms">短信</el-checkbox>
            <el-checkbox v-model="formData.notifySystem">系统</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="催办内容" prop="urgingContent">
            <el-input v-model="formData.urgingContent" type="textarea" :rows="4" placeholder="请输入催办内容" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { updateUrging } from '@/api/comprehensive'
import { getDeptTree } from '@/api/system'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  projectId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 下拉选项数据
const departmentOptions = ref([])

// 部门树选择属性
const deptSelectProps = {
  value: 'id',
  label: 'name',
  children: 'children'
}

// 表单数据
const formData = reactive({
  projectId: '',
  urgingUnit: '',
  urgingUnitName: '',
  urgingContent: '',
  updateTime: new Date(),
  notifySms: true,
  notifySystem: true,
  remark: ''
})

// 表单验证规则
const formRules = {
  urgingUnit: [{ required: true, message: '请选择催办部门', trigger: 'change' }],
  urgingContent: [{ required: true, message: '请输入催办内容', trigger: 'blur' }],
  updateTime: [{ required: true, message: '请选择更新时间', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  formData.projectId = ''
  formData.urgingUnit = ''
  formData.urgingUnitName = ''
  formData.urgingContent = ''
  formData.updateTime = new Date()
  formData.notifySms = true
  formData.notifySystem = true
  formData.remark = ''
}

// 监听props.projectId变化
watch(() => props.projectId, (newVal) => {
  if (newVal) {
    formData.projectId = newVal
  }
}, { immediate: true })

// 处理部门变化
const handleDepartmentChange = (value) => {
  const findDept = (depts, id) => {
    for (const dept of depts) {
      if (dept.id === id) {
        return dept
      }
      if (dept.children) {
        const found = findDept(dept.children, id)
        if (found) return found
      }
    }
    return null
  }

  const selected = findDept(departmentOptions.value, value)
  if (selected) {
    formData.urgingUnitName = selected.name
  }
}

// 获取部门树
const fetchDepartmentTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200 && res.data) {
      departmentOptions.value = res.data
    }
  } catch (error) {
    console.error('获取部门树失败', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    const res = await updateUrging(submitData)

    if (res && res.code === 200) {
      ElMessage.success('催办成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || '催办失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDepartmentTree()
})
</script>

<style scoped>
.urging-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 