<template>
  <el-dialog
    v-model="dialogVisible"
    title="监测曲线"
    width="900px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="monitor-curve-dialog"
  >
    <div class="curve-container">
      <!-- 顶部控制区域 -->
      <div class="control-container">
        <!-- 指标选择器 -->
        <div class="indicator-selector" v-if="monitorIndicators.length > 0">
          <el-select v-model="activeIndicatorId" placeholder="请选择监测指标" style="width: 180px">
            <el-option
              v-for="item in monitorIndicators"
              :key="item.id"
              :label="item.monitorIndexName"
              :value="item.id"
            >
              <span>{{ item.monitorIndexName }}</span>
              <span class="indicator-unit" v-if="item.measureUnit">({{ item.measureUnit }})</span>
            </el-option>
          </el-select>
        </div>
        
        <!-- 时间选项卡 -->
        <div class="time-tabs">
          <div
            v-for="(tab, index) in timeTabs"
            :key="index"
            :class="['time-tab', { active: activeTimeTab === index }]"
            @click="handleTimeTabChange(index)"
          >
            {{ tab.label }}
          </div>
        </div>
        
        <!-- 单位显示 -->
        <div class="unit-display" v-if="unitText">单位: {{ unitText }}</div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-wrapper">
        <div ref="chartRef" class="chart-container"></div>
      </div>
      
      <!-- 无数据提示 -->
      <!-- <div class="no-data-tip" v-if="chartData.length === 0 && monitorIndicators.length > 0 && activeIndicator">
        <el-empty description="暂无监测数据" />
      </div> -->
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { getMonitorCurveData, getMonitorIndicators } from '@/api/gas';
import * as echarts from 'echarts';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 图表引用
const chartRef = ref(null);
let chartInstance = null;

// 时间选项卡
const timeTabs = [
  { label: '最近24小时', days: 1 },
  { label: '最近7天', days: 7 },
  { label: '最近30天', days: 30 }
];
const activeTimeTab = ref(0);
const unitText = ref('');

// 图表数据
const chartData = ref([]);

// 监测指标数据
const monitorIndicators = ref([]);
// 当前选中的监测指标ID
const activeIndicatorId = ref(null);
// 当前选中的监测指标对象
const activeIndicator = computed(() => {
  if (!activeIndicatorId.value || monitorIndicators.value.length === 0) return null;
  return monitorIndicators.value.find(item => item.id === activeIndicatorId.value) || null;
});

// 颜色配置
const CHART_COLORS = [
  '#4278FF', // 蓝色
  '#36CE9E', // 绿色
  '#FFC543', // 黄色
  '#FF6B6B', // 红色
  '#8E65FF', // 紫色
  '#FF9A3C', // 橙色
  '#3ECBFF', // 天蓝色
  '#5D7092'  // 灰蓝色
];

// 状态颜色配置
const STATUS_COLORS = {
  normal: '#36CE9E', // 正常状态 - 绿色
  abnormal: '#FF6B6B' // 异常状态 - 红色
};

// 初始化图表
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  // 确保DOM元素已经渲染
  nextTick(() => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      updateChart();
    }
  });
};

// 更新图表
const updateChart = () => {
  if (!chartInstance || !activeIndicator.value) return;

  // 根据选中的指标字段过滤数据
  const fieldName = activeIndicator.value.monitorField;
  let validData = chartData.value.filter(item => 
    item && 
    item.monitorTime && 
    item[fieldName] !== undefined && 
    item[fieldName] !== null &&
    item[fieldName]!== -999
  );
  
  // 如果没有有效数据，生成默认的零值数据
  if (validData.length === 0) {
    console.warn('没有有效的监测数据，生成默认数据');
    const timeRange = getTimeRange();
    const defaultTimeData = generateDefaultTimeData(timeRange);
    
    validData = defaultTimeData.map(time => ({
      monitorTime: time,
      [fieldName]: 0
    }));
  }

  // 处理数据
  const xData = validData.map(item => item.monitorTime);
  const indicatorType = activeIndicator.value.type;
  const unit = activeIndicator.value.measureUnit || '';
  const minRange = parseFloat(activeIndicator.value.measureRangeLow);
  const maxRange = parseFloat(activeIndicator.value.measureRangeUp);
  
  // 设置单位文本
  unitText.value = unit;

  let option = {};

  if (indicatorType === 1) {
    // 数值型指标
    const yData = validData.map(item => {
      const value = parseFloat(item[fieldName]);
      return isNaN(value) ? null : value;
    });

    const series = [{
      name: '监测值',
      data: yData,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#409EFF'
      },
      lineStyle: {
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(64,158,255,0.2)'
          }, {
            offset: 1,
            color: 'rgba(64,158,255,0)'
          }]
        }
      }
    }];

    // 添加上限线和下限线
    if (!isNaN(maxRange)) {
      series.push({
        name: '上限值',
        data: new Array(xData.length).fill(maxRange),
        type: 'line',
        lineStyle: {
          color: '#FF0000',
          type: 'dashed',
          width: 2
        },
        symbol: 'none',
        silent: true
      });
    }

    if (!isNaN(minRange)) {
      series.push({
        name: '下限值',
        data: new Array(xData.length).fill(minRange),
        type: 'line',
        lineStyle: {
          color: '#FF0000',
          type: 'dashed',
          width: 2
        },
        symbol: 'none',
        silent: true
      });
    }

    option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let result = `${params[0].name}<br/>`;
          params.forEach(item => {
            if (item.seriesName === '监测值') {
              result += `${item.marker}${item.seriesName}：${item.value}${unit}<br/>`;
            } else {
              result += `${item.marker}${item.seriesName}：${item.value}${unit}<br/>`;
            }
          });
          return result;
        }
      },
      legend: {
        data: series.map(s => s.name)
      },
      dataZoom: [{
        type: 'slider',
        start: 20,
        end: 80,
        height: 30,
        bottom: 10,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '60%',
        handleStyle: {
          color: '#0277FD',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        textStyle: {
          color: '#333',
          fontSize: 10
        },
        borderColor: '#ddd',
        fillerColor: 'rgba(2, 119, 253, 0.2)',
        backgroundColor: '#f8f9fa',
        selectedDataBackground: {
          lineStyle: {
            color: '#0277FD'
          },
          areaStyle: {
            color: 'rgba(2, 119, 253, 0.3)'
          }
        },
        labelFormatter: function(value, valueStr) {
          const index = Math.round(value * (xData.length - 1) / 100);
          if (index >= 0 && index < xData.length) {
            const timeStr = xData[index];
            if (activeTimeTab.value === 0) {
              return timeStr.split(' ')[1] || timeStr;
            } else {
              const date = new Date(timeStr);
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hour = String(date.getHours()).padStart(2, '0');
              const minute = String(date.getMinutes()).padStart(2, '0');
              return `${month}/${day} ${hour}:${minute}`;
            }
          }
          return valueStr;
        }
      }, {
        type: 'inside',
        start: 0,
        end: 100
      }],
      grid: {
        top: 50,
        left: '8%',
        right: '10%',
        bottom: 60,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          rotate: 30,
          formatter: (value) => {
            if (activeTimeTab.value === 0) {
              return value.split(' ')[1]; // 只显示时间部分
            } else {
              // 7天和30天显示月/日 时:分格式
              const date = new Date(value);
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hour = String(date.getHours()).padStart(2, '0');
              const minute = String(date.getMinutes()).padStart(2, '0');
              return `${month}/${day} ${hour}:${minute}`;
            }
          }
        }
      },
      yAxis: {
        type: 'value',
        name: `监测值(${unit})`,
        nameTextStyle: {
          padding: [0, 30, 0, 0]
        },
        min: (() => {
          if (!isNaN(minRange)) {
            const validYData = yData.filter(v => v !== null && v !== undefined);
            if (validYData.length > 0) {
              return Math.min(minRange - (maxRange - minRange) * 0.1, Math.min(...validYData) - 5);
            } else {
              return minRange - 5;
            }
          }
          return undefined;
        })(),
        max: (() => {
          if (!isNaN(maxRange)) {
            const validYData = yData.filter(v => v !== null && v !== undefined);
            if (validYData.length > 0) {
              return Math.max(maxRange + (maxRange - minRange) * 0.1, Math.max(...validYData) + 5);
            } else {
              return maxRange + 5;
            }
          }
          return undefined;
        })(),
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series
    };
  } else {
    // 状态型指标 (type === 0)
    const statusData = validData.map(item => {
      const value = parseInt(item[fieldName]);
      return isNaN(value) ? null : value;
    });

    // 分别处理正常和异常状态的数据
    const normalData = statusData.map(value => value === 0 ? 1 : null);
    const abnormalData = statusData.map(value => value === 1 ? 1 : null);

    option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const dataIndex = params[0].dataIndex;
          const status = statusData[dataIndex];
          const statusText = status === 0 ? '正常' : status === 1 ? '异常' : '未知';
          return `${params[0].name}<br/>状态：${statusText}`;
        }
      },
      legend: {
        data: ['正常', '异常']
      },
      dataZoom: [{
        type: 'slider',
        start: 20,
        end: 80,
        height: 30,
        bottom: 10,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '60%',
        handleStyle: {
          color: '#0277FD',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        textStyle: {
          color: '#333',
          fontSize: 10
        },
        borderColor: '#ddd',
        fillerColor: 'rgba(2, 119, 253, 0.2)',
        backgroundColor: '#f8f9fa',
        selectedDataBackground: {
          lineStyle: {
            color: '#0277FD'
          },
          areaStyle: {
            color: 'rgba(2, 119, 253, 0.3)'
          }
        },
        labelFormatter: function(value, valueStr) {
          const index = Math.round(value * (xData.length - 1) / 100);
          if (index >= 0 && index < xData.length) {
            const timeStr = xData[index];
            if (activeTimeTab.value === 0) {
              return timeStr.split(' ')[1] || timeStr;
            } else {
              const date = new Date(timeStr);
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hour = String(date.getHours()).padStart(2, '0');
              const minute = String(date.getMinutes()).padStart(2, '0');
              return `${month}/${day} ${hour}:${minute}`;
            }
          }
          return valueStr;
        }
      }, {
        type: 'inside',
        start: 0,
        end: 100
      }],
      grid: {
        top: 50,
        left: '3%',
        right: '4%',
        bottom: 90,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          rotate: 30,
          formatter: (value) => {
            if (activeTimeTab.value === 0) {
              return value.split(' ')[1]; // 只显示时间部分
            } else {
              // 7天和30天显示月/日 时:分格式
              const date = new Date(value);
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hour = String(date.getHours()).padStart(2, '0');
              const minute = String(date.getMinutes()).padStart(2, '0');
              return `${month}/${day} ${hour}:${minute}`;
            }
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '状态',
        min: 0,
        max: 2,
        interval: 1,
        axisLabel: {
          formatter: (value) => {
            if (value === 0) return '';
            if (value === 1) return '';
            return '';
          }
        },
        splitLine: {
          show: false
        }
      },
      series: [{
        name: '正常',
        data: normalData,
        type: 'line',
        step: 'end',
        lineStyle: {
          color: '#409EFF',
          width: 4
        },
        symbol: 'none'
      }, {
        name: '异常',
        data: abnormalData,
        type: statusData.length === 1 && statusData[0] === 1 ? 'scatter' : 'line',
        step: 'end',
        lineStyle: {
          color: '#FF0000',
          width: 4
        },
        symbol: statusData.length === 1 && statusData[0] === 1 ? 'circle' : 'none',
        symbolSize: statusData.length === 1 && statusData[0] === 1 ? 8 : 0
      }]
    };
  }

  chartInstance.setOption(option);
};

// 格式化日期为 YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
  const pad = (num) => String(num).padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};

// 计算时间范围
const getTimeRange = () => {
  const endTime = new Date();
  let startTime = new Date();
  
  const days = timeTabs[activeTimeTab.value].days;
  startTime.setDate(endTime.getDate() - days);
  
  return {
    startTime: formatDate(startTime),
    endTime: formatDate(endTime)
  };
};

// 生成默认时间数据
const generateDefaultTimeData = (timeRange) => {
  const startTime = new Date(timeRange.startTime);
  const endTime = new Date(timeRange.endTime);
  const timeData = [];
  
  // 根据时间范围确定间隔
  let interval = 60 * 60 * 1000; // 默认1小时间隔
  if (activeTimeTab.value === 0) {
    interval = 60 * 60 * 1000; // 1小时
  } else if (activeTimeTab.value === 1) {
    interval = 6 * 60 * 60 * 1000; // 6小时
  } else if (activeTimeTab.value === 2) {
    interval = 24 * 60 * 60 * 1000; // 1天
  }
  
  let currentTime = new Date(startTime);
  while (currentTime <= endTime) {
    timeData.push(formatDate(currentTime));
    currentTime = new Date(currentTime.getTime() + interval);
  }
  
  return timeData;
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  
  // 处理后端返回的Java时间对象
  if (typeof time === 'object' && time !== null) {
    // 根据Java时间对象的结构构建日期
    const year = time.year + 1900; // Java年份从1900开始
    const month = time.month; // Java月份是0-11
    const date = time.date;
    const hours = time.hours;
    const minutes = time.minutes;
    const seconds = time.seconds;
    
    return new Date(year, month, date, hours, minutes, seconds).toISOString();
  }
  
  return time;
};

// 处理时间选项卡变化
const handleTimeTabChange = (index) => {
  activeTimeTab.value = index;
  fetchCurveData();
};

// 获取监测指标
const fetchMonitorIndicators = async () => {
  if (!props.deviceData || !props.deviceData.id) return;
  
  try {
    const res = await getMonitorIndicators(props.deviceData.id);
    if (res && res.code === 200 && res.data && res.data.length > 0) {
      monitorIndicators.value = res.data;
      // 默认选择第一个指标
      activeIndicatorId.value = res.data[0].id;
      // 获取曲线数据
      fetchCurveData();
    }
  } catch (error) {
    console.error('获取监测指标失败', error);
  }
};

// 获取曲线数据
const fetchCurveData = async () => {
  if (!props.deviceData || !props.deviceData.id || !activeIndicator.value) return;
  
  try {
    const timeRange = getTimeRange();
    const params = {
      deviceId: props.deviceData.id,
      startTime: timeRange.startTime,
      endTime: timeRange.endTime
    };
    
    const res = await getMonitorCurveData(params);
    if (res && res.code === 200) {
      chartData.value = res.data || [];
      updateChart();
    }
  } catch (error) {
    console.error('获取监测曲线数据失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 监听设备数据变化
watch(() => props.deviceData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    fetchMonitorIndicators();
  }
}, { deep: true });

// 监听对话框可见性变化
watch(() => dialogVisible.value, (val) => {
  if (val) {
    nextTick(() => {
      initChart();
    });
  }
});

// 监听当前选中的指标ID变化
watch(() => activeIndicatorId.value, () => {
  // 当指标变化时，重新获取曲线数据
  fetchCurveData();
});

// 组件挂载完成
onMounted(() => {
  if (dialogVisible.value) {
    initChart();
  }
});
</script>

<style scoped>
.monitor-curve-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.curve-container {
  width: 100%;
  height: 100%;
}

.control-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.indicator-selector {
  margin-right: 16px;
}

.indicator-unit {
  color: #909399;
  margin-left: 4px;
  font-size: 12px;
}

.time-tabs {
  display: flex;
  background-color: #F5F7FA;
  border-radius: 4px;
  overflow: hidden;
}

.time-tab {
  padding: 8px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.time-tab.active {
  background-color: #4699FF;
  color: white;
}

.unit-display {
  font-size: 14px;
  color: #909399;
  margin-left: 16px;
}

.chart-wrapper {
  width: 100%;
  height: 400px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #E4E7ED;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.no-data-tip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 1;
  pointer-events: none;
}
</style>