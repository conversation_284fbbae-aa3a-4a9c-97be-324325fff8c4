<template>
    <div
            class="echarts"
            :id="id"
            :style="{
      width: width,
      height: height
    }"
            v-echart-resize
    ></div>
</template>

<script setup>
import {defineProps, onMounted, onUnmounted, ref, watch, nextTick} from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    width: {
        type: String,
        default: '100%'
    },
    height: {
        type: String,
        default: '100%'
    },
    type: {
        type: Number,
        default: 0
    },
    value: {
        type: Number,
        default: ""
    },
    unit: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: '监测值'
    },
    max: {
        type: Number,
        default: 100
    },
    min: {
        type: Number,
        default: 0
    }
})

const chart = ref(null)

const unwarp = (obj) => obj && (obj.__v_raw || obj.valueOf() || obj)

const setOptions = () => {
    const option = {
        series: [
            {
                type: 'gauge',
                radius: '100%',
                center: ['50%', '50%'],
                startAngle: 225,
                endAngle: -45,
                min: props.min,
                max: props.max,
                progress: {
                    show: true,
                    width: 10,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                            {offset: 0, color: '#0B57A2'},
                            {offset: 1, color: '#01C2FF'}
                        ])
                    }
                },
                axisLine: {
                    lineStyle: {
                        width: 20,
                        color: [[1, 'rgba(1, 194, 255, 0.2)']]
                    }
                },
                axisTick: {
                    show: false,
                    distance: 0,
                    splitNumber: 5,
                    lineStyle: {
                        width: 2,
                        color: '#999'
                    }
                },
                splitLine: {
                    distance: -6,
                    length: 6,
                    lineStyle: {
                        width: 2,
                        color: 'rgba(1, 194, 255, 0.5)',
                        type: 'dashed'
                    }
                },
                axisLabel: {
                    show: props.type !== 0, //非状态类数值才展示刻度数
                    distance: 25,
                    color: '#999',
                    fontSize: 9,
                    formatter: function (value) {
                        return value.toFixed(0);
                    }
                },
                pointer: {
                    show: false,
                    icon: 'circle',
                    width: 9,
                    length: 28,
                    offsetCenter: [0, '-70%'],
                    itemStyle: {
                        color: '#F4CE74',
                        borderColor: '#fff',
                        borderWidth: 2
                    }
                },
                detail: {
                    valueAnimation: true,
                    formatter: function() {
                        if (props.value === null || props.value === undefined) {
                            return `{value|无数据}`;
                        }
                        if (props.type === 0 && props.value === 0) {
                            return `{value|正常}`;
                        }
                        if (props.type === 0 && props.value === 1) {
                            return `{value|异常}`;
                        }
                        if (props.type === 1) {
                            return `{value|${props.value.toFixed(1)}}{unit|${props.unit}}`;
                        }
                        return `{value|无数据}`;
                    },
                    rich: {
                        value: {
                            fontSize: 24,
                            fontWeight: 'bold',
                            color: '#FFFFFF'
                        },
                        unit: {
                            fontSize: 14,
                            color: '#FFFFFF',
                            padding: [0, 0, -10, 5]
                        }
                    },
                    offsetCenter: [0, '-10%']
                },
                title: {
                    offsetCenter: [0, '25%'],
                    fontSize: 14,
                    color: 'rgba(255, 255, 255, 0.8)'
                },
                data: [{
                    value: props.value,
                    name: props.label
                }]
            }
        ]
    };
    if (chart.value) {
        unwarp(chart.value).setOption(option, true);
    }
}

const initChart = () => {
    nextTick(() => {
        const _doc = document.getElementById(props.id)
        if (!_doc) return;
        if (chart.value) {
            chart.value.dispose()
        }
        chart.value = echarts.init(_doc)
        setOptions()
    })
}

onMounted(() => {
    setTimeout(() => {
        initChart()
    }, 100)
})

onUnmounted(() => {
    if (!chart.value) {
        return
    }
    chart.value.dispose()
    chart.value = null
})

watch(
    () => [props.value, props.unit, props.label, props.max, props.min, props.type],
    () => {
        setTimeout(() => {
            initChart()
        }, 100)
    },
    {
        deep: true,
        immediate: true,
    }
)
</script>

<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
</style>