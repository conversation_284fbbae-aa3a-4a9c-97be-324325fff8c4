<template>
    <div class="device-monitor">
        <div class="monitor-gauge">
            <GaugeChart
                    id="gaugeChart"
                    :width="'100%'"
                    :height="'100%'"
                    :value="state.chartData?.value"
                    :unit="state.chartData?.unit"
                    :type="state.chartData?.type"
                    :max="state.chartData?.max"
                    :min="state.chartData?.min"
            />
        </div>
        <div class="device-indicators">
            <el-select
                    v-model="state.selectIndicator"
                    placeholder="请选择"
                    style="min-width: 120px"
                    @change="handleJczbChange"
            >
                <el-option
                        v-for="item in state.indicators"
                        :key="item.id"
                        :label="item.monitorIndexName"
                        :value="item?.monitorField"
                />
            </el-select>
        </div>
    </div>
    <div>
        <BaseInfo :baseInfo="baseInfo"/>
    </div>
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import BaseInfo from "../../BaseInfo.vue";
import GaugeChart from "./GaugeChart.vue";
import {
    popupApiInfo,
    popupDeviceCurveApiInfo,
    popupMonitorIndicatorsApiInfo
} from "@/components/GisMap/popup/popupApi.js";
import {popupConfigInfo} from "@/components/GisMap/popup/NormalDetail/config.js";
import moment from "moment/moment.js";

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
});

const state = reactive({
    resData: [],
    chartData: [],
    title: "",
    indicators: [],
    selectIndicator: "",
});

const baseInfo = ref([]);

const getDeviceInfo = async () => {
    const {data} = await popupApiInfo[props.data?.layerId](props.data?.id);
    baseInfo.value = popupConfigInfo[props.data?.layerId].map((v) => {
        return {
            ...v,
            value: v.props === "onlineStatus" && data[v.props] === 1 ? "在线"
                : v.props === "onlineStatus" && data[v.props] === 0 ? "离线"
                    : data[v.props] || "",
        };
    });
};

const getDeviceIndicatorValue = () => {
    state.resData = [];
    const params = {
        deviceId: props.data.id,
        startTime: moment().subtract(7, "day").format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    };
    if (popupDeviceCurveApiInfo[props.data?.layerId]) {
        Promise.all([
            popupMonitorIndicatorsApiInfo[props.data?.layerId](props.data?.id),
            popupDeviceCurveApiInfo[props.data?.layerId](params)
        ]).then(([indicatorsResponse, curveResponse]) => {
            state.indicators = indicatorsResponse.data;
            state.resData = curveResponse.data;
            if (state.selectIndicator) {
                handleJczbChange(state.selectIndicator);
            } else {
                handleJczbChange(state.selectIndicator = indicatorsResponse.data?.[0]?.monitorField ?? "")
            }
        }).catch((error) => {
            console.error("Error fetching data:", error);
        });
    }
}

//指标切换
const handleJczbChange = (value) => {
    const ind = state.indicators.find((item) => item?.monitorField === value);
    const jczb = ind?.monitorIndexName ?? "";
    state.title = jczb && ind?.type === 0 ? `${jczb}` : jczb ? `${jczb}（${ind?.measureUnit}）` : "";
    let newData = state.resData
        .filter(im => im[value] !== -999 && im[value] !== null)
        .map((item) => ({
            name: item?.monitorTime,
            value: item[value],
            unit: ind?.measureUnit || "",
            max: parseFloat(ind?.measureRangeUp),
            min: parseFloat(ind?.measureRangeLow),
            type: ind?.type,
        }));
    state.chartData = newData.length > 0 ? newData[newData.length - 1] : {};
    // console.log("state.chartData---->>>gggg", state.chartData);
}

watch(
    () => props.data,
    () => {
        getDeviceInfo();
        getDeviceIndicatorValue();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style scoped>
.device-monitor {
    display: flex;
    flex-direction: row;
    justify-content: space-between; /* 两端对齐 */
    align-items: flex-start; /* 垂直居中 */
    flex-wrap: nowrap;
}

.monitor-gauge {
    margin-top: 10px;
    width: 170px;
    height: 170px;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.device-indicators {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    margin-top: 10px;
    font-size: 14px;
}

.device-base-info {
    height: 180px;
}
</style>