<template>
  <teleport to="body">
    <transition name="fade">
      <div class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">预警详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          
          <!-- 预警详情内容 -->
          <div class="modal-content" v-loading="loading" element-loading-background="rgba(0, 22, 72, 0.5)">
            <el-form :model="warningDetail" label-width="120px" class="detail-form" v-if="warningDetail">
              <!-- 基本信息 -->
              <div class="form-section">
                <div class="section-title">基本信息</div>
                <div class="form-row">
                  <el-form-item label="预警编号:" class="form-item-half">
                    <el-input v-model="warningDetail.warningCode" readonly />
                  </el-form-item>
                  <el-form-item label="预警标题:" class="form-item-half">
                    <el-input v-model="warningDetail.warningTitle" readonly />
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item label="预警类型:" class="form-item-half">
                    <el-input v-model="warningDetail.warningTypeName" readonly />
                  </el-form-item>
                  <el-form-item label="预警等级:" class="form-item-half">
                    <span :class="getLevelClass(warningDetail.warningLevelName)">
                      {{ warningDetail.warningLevelName }}
                    </span>
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item label="所属专项:" class="form-item-half">
                    <el-input v-model="warningDetail.relatedBusinessName" readonly />
                  </el-form-item>
                  <el-form-item label="预警状态:" class="form-item-half">
                    <span :class="getStatusClass(warningDetail.warningStatusName)">
                      {{ warningDetail.warningStatusName }}
                    </span>
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item label="预警时间:" class="form-item-half">
                    <el-input v-model="warningDetail.warningTime" readonly />
                  </el-form-item>
                  <el-form-item label="发布时间:" class="form-item-half">
                    <el-input v-model="warningDetail.publishTime" readonly />
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item label="发布单位:" class="form-item-full">
                    <el-input v-model="warningDetail.publishUnitName" readonly />
                  </el-form-item>
                </div>
              </div>

              <!-- 位置信息 -->
              <div class="form-section">
                <div class="section-title">位置信息</div>
                <div class="form-row">
                  <el-form-item label="预警位置:" class="form-item-half">
                    <el-input v-model="warningDetail.warningLocation" readonly />
                  </el-form-item>
                  <el-form-item label="坐标信息:" class="form-item-half">
                    <el-input :value="getCoordinateText()" readonly />
                  </el-form-item>
                </div>
              </div>

              <!-- 详细描述 -->
              <div class="form-section">
                <div class="section-title">详细描述</div>
                <div class="form-row">
                  <el-form-item label="预警描述:" class="form-item-full">
                    <el-input 
                      v-model="warningDetail.warningDesc" 
                      type="textarea" 
                      :rows="3" 
                      readonly 
                    />
                  </el-form-item>
                </div>
                <div class="form-row" v-if="warningDetail.remark">
                  <el-form-item label="备注:" class="form-item-full">
                    <el-input 
                      v-model="warningDetail.remark" 
                      type="textarea" 
                      :rows="2" 
                      readonly 
                    />
                  </el-form-item>
                </div>
              </div>

              <!-- 处置信息 -->
              <div class="form-section" v-if="warningDetail.dealUnitAndUserList && warningDetail.dealUnitAndUserList.length > 0">
                <div class="section-title">处置信息</div>
                <div class="deal-list">
                  <div 
                    v-for="(deal, index) in warningDetail.dealUnitAndUserList" 
                    :key="index" 
                    class="deal-item"
                  >
                    <div class="form-row">
                      <el-form-item label="处置单位:" class="form-item-half">
                        <el-input v-model="deal.dealUnitName" readonly />
                      </el-form-item>
                      <el-form-item label="处置人员:" class="form-item-half">
                        <el-input v-model="deal.dealUserName" readonly />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 通知设置 -->
              <div class="form-section">
                <div class="section-title">通知设置</div>
                <div class="form-row">
                  <el-form-item label="短信通知:" class="form-item-half">
                    <span :class="warningDetail.notifySms ? 'status-enabled' : 'status-disabled'">
                      {{ warningDetail.notifySms ? '已启用' : '未启用' }}
                    </span>
                  </el-form-item>
                  <el-form-item label="APP通知:" class="form-item-half">
                    <span :class="warningDetail.notifyApp ? 'status-enabled' : 'status-disabled'">
                      {{ warningDetail.notifyApp ? '已启用' : '未启用' }}
                    </span>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getWarningInfoDetail } from '@/api/comprehensive.js'

const props = defineProps({
  warningId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close'])

// 加载状态
const loading = ref(false)

// 预警详情数据
const warningDetail = ref(null)

/**
 * 关闭弹窗
 */
const closeModal = () => {
  emit('close')
}

/**
 * 获取预警详情
 */
const fetchWarningDetail = async () => {
  try {
    loading.value = true
    
    const response = await getWarningInfoDetail(props.warningId)
    
    if (response.code === 200 && response.data) {
      warningDetail.value = response.data
    }
  } catch (error) {
    console.error('获取预警详情失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 获取预警级别样式类
 */
const getLevelClass = (level) => {
  const classMap = {
    '一级预警': 'level-one',
    '二级预警': 'level-two', 
    '三级预警': 'level-three'
  }
  return classMap[level] || 'level-three'
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status) => {
  const classMap = {
    '待处置': 'status-pending',
    '处置中': 'status-processing',
    '已处置': 'status-completed',
    '已解除': 'status-resolved'
  }
  return classMap[status] || 'status-pending'
}

/**
 * 获取坐标文本
 */
const getCoordinateText = () => {
  if (!warningDetail.value) return ''
  const { longitude, latitude } = warningDetail.value
  if (longitude && latitude) {
    return `${longitude}, ${latitude}`
  }
  return '暂无坐标信息'
}

onMounted(() => {
  fetchWarningDetail()
})
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 1000px;
  max-height: 85vh;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.detail-form {
  .form-section {
    margin-bottom: 25px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 14px;
    font-weight: 500;
    color: #3B8DF2;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  }

  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-item-full {
    flex: 1;
  }

  .form-item-half {
    flex: 1;
    min-width: 0;
  }
}

.deal-list {
  .deal-item {
    padding: 15px;
    background: rgba(0, 19, 47, 0.2);
    border: 1px solid rgba(59, 141, 242, 0.2);
    border-radius: 4px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 预警等级样式
.level-one { 
  color: #FF3900; 
  font-weight: 500;
}
.level-two { 
  color: #FF6817; 
  font-weight: 500;
}
.level-three { 
  color: #FFD32E; 
  font-weight: 500;
}

// 状态样式
.status-pending { 
  color: #FF6817; 
  font-weight: 500;
}
.status-processing { 
  color: #1890FF; 
  font-weight: 500;
}
.status-completed { 
  color: #52C41A; 
  font-weight: 500;
}
.status-resolved { 
  color: #8C8C8C; 
  font-weight: 500;
}

// 通知状态样式
.status-enabled { 
  color: #52C41A; 
  font-weight: 500;
}
.status-disabled { 
  color: #8C8C8C; 
  font-weight: 500;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.el-form) {
  .el-form-item__label {
    color: #D3E5FF;
    font-weight: 400;
  }

  .el-input__wrapper {
    background-color: rgba(0, 19, 47, 0.35) !important;
    box-shadow: none !important;
    border: 1px solid rgba(59, 141, 242, 0.5);
  }

  .el-input.is-disabled .el-input__wrapper {
    background-color: rgba(0, 19, 47, 0.2) !important;
  }

  .el-input__inner {
    color: #FFFFFF;
  }

  .el-textarea__inner {
    background-color: rgba(0, 19, 47, 0.35) !important;
    border: 1px solid rgba(59, 141, 242, 0.5);
    color: #FFFFFF;
    box-shadow: none !important;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: rgba(0, 19, 47, 0.2) !important;
  }
}
</style>
