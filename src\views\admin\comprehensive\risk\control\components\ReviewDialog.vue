<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患复查"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="review-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="完成时间" prop="dealTime">
            <el-date-picker
              v-model="formData.dealTime"
              type="datetime"
              placeholder="请选择完成时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否按照计划完成" prop="onSchedule">
            <el-select v-model="formData.onSchedule" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in onScheduleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="完成情况" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入完成情况描述"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="复查人" prop="handleUserName">
            <el-input v-model="formData.handleUserName" placeholder="请输入复查人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否通过" prop="isPass">
            <el-select v-model="formData.isPass" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in reviewResultOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="复查备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入复查备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  reviewHiddenDanger,
} from '@/api/comprehensive'
import { ON_SCHEDULE_OPTIONS, REVIEW_RESULT_OPTIONS } from '@/constants/comprehensive'


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dangerId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 下拉选项数据
const onScheduleOptions = ref(ON_SCHEDULE_OPTIONS)
const reviewResultOptions = ref(REVIEW_RESULT_OPTIONS)

// 表单数据
const formData = reactive({
  dangerId: '',
  dealTime: '',
  description: '',
  handleUserName: '',
  isPass: 1,
  onSchedule: true,
  remark: ''
})

// 表单验证规则
const formRules = {
  dealTime: [{ required: true, message: '请选择完成时间', trigger: 'change' }],
  description: [{ required: true, message: '请输入完成情况描述', trigger: 'blur' }],
  handleUserName: [{ required: true, message: '请输入复查人', trigger: 'blur' }],
  isPass: [{ required: true, message: '请选择是否通过', trigger: 'change' }],
  onSchedule: [{ required: true, message: '请选择是否按照计划完成', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'isPass') {
      formData[key] = 1
    } else if (key === 'onSchedule') {
      formData[key] = true
    } else {
      formData[key] = ''
    }
  })
  formData.dangerId = props.dangerId
}

// 监听dangerId变化
watch(() => props.dangerId, (newVal) => {
  if (newVal) {
    formData.dangerId = newVal
  }
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    const res = await reviewHiddenDanger(submitData)

    if (res && res.code === 200) {
      ElMessage.success('复查成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || '复查失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style scoped>
.review-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 