<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="danger-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="危险源编码:" prop="dangerCode">
            <el-input v-model="formData.dangerCode" placeholder="请输入危险源编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="危险源名称:" prop="dangerName">
            <el-input v-model="formData.dangerName" placeholder="请输入危险源名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑类型:" prop="buildingType">
            <el-select v-model="formData.buildingType" placeholder="请选择" class="w-full" @change="handleBuildingTypeChange">
              <el-option v-for="item in buildingTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否重大危险源:" prop="isMajor">
            <el-radio-group v-model="formData.isMajor">
              <el-radio v-for="item in isMajorOptions" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="特征描述:" prop="featureDesc">
            <el-input v-model="formData.featureDesc" placeholder="请输入特征描述" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风险等级:" prop="riskLevelName">
            <el-input v-model="formData.riskLevelName" placeholder="请输入风险等级" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="财产损失:" prop="propertyLoss">
            <el-input v-model="formData.propertyLoss" placeholder="请输入财产损失" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="影响半径(KM):" prop="influenceRadius">
            <el-input-number v-model="formData.influenceRadius" :min="0" :precision="2" class="w-full" placeholder="请输入影响半径" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属单位:" prop="managementUnit">
            <el-input v-model="formData.managementUnit" placeholder="请输入所属单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人:" prop="contactUser">
            <el-input v-model="formData.contactUser" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话:" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置坐标:" prop="latitude">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置:" prop="town">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveDanger,
  updateDanger
} from '@/api/drainage';
import { DANGER_BUILDING_TYPE_OPTIONS, IS_MAJOR_DANGER_OPTIONS } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const buildingTypeOptions = DANGER_BUILDING_TYPE_OPTIONS;
const isMajorOptions = IS_MAJOR_DANGER_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  };
  return titles[props.mode] || '危险源信息';
});

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  dangerCode: '',
  dangerName: '',
  buildingType: '',
  buildingTypeName: '',
  isMajor: '',
  featureDesc: '',
  riskLevelName: '',
  propertyLoss: '',
  influenceRadius: 0,
  managementUnit: '',
  managementUnitName: '',
  contactUser: '',
  contactInfo: '',
  longitude: '',
  latitude: '',
  address: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  dangerCode: [{ required: true, message: '请输入危险源编码', trigger: 'blur' }],
  dangerName: [{ required: true, message: '请输入危险源名称', trigger: 'blur' }],
  isMajor: [{ required: true, message: '请选择是否重大危险源', trigger: 'change' }],
  featureDesc: [{ required: true, message: '请输入特征描述', trigger: 'blur' }],
  contactUser: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactInfo: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  town: [{ required: true, message: '请选择所属区域', trigger: 'change' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'influenceRadius') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });

    // 处理区域数据反显
    if (newVal.town) {
      // 查找区域的完整路径
      const foundPath = findAreaPath(areaOptions.value, newVal.town);
      if (foundPath) {
        formData.town = foundPath;
      } else {
        // 如果找不到路径，直接使用原值
        formData.town = newVal.town;
      }
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理建筑类型变化
const handleBuildingTypeChange = (value) => {
  const selected = buildingTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.buildingTypeName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    // 根据当前的AREA_OPTIONS结构（县 -> 镇），处理两级选择
    if (value.length === 1) {
      // 只选择了县级
      formData.county = value[0];
      formData.town = '';
      formData.townName = '';
      // 查找县名称
      const countyArea = areaOptions.value.find(area => area.code === value[0]);
      if (countyArea) {
        formData.countyName = countyArea.name;
      }
    } else if (value.length === 2) {
      // 选择了县和镇
      formData.county = value[0];
      formData.town = value[1];
      // 查找县和镇的名称
      const countyArea = areaOptions.value.find(area => area.code === value[0]);
      if (countyArea) {
        formData.countyName = countyArea.name;
        const townArea = countyArea.children?.find(area => area.code === value[1]);
        if (townArea) {
          formData.townName = townArea.name;
        }
      }
    }
  } else {
    // 清空选择
    formData.county = '';
    formData.countyName = '';
    formData.town = '';
    formData.townName = '';
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 查找区域的完整路径
const findAreaPath = (areas, targetCode) => {
  for (const area of areas) {
    // 检查是否是县级代码
    if (area.code === targetCode) {
      return [area.code];
    }
    // 检查是否是镇级代码
    if (area.children) {
      for (const child of area.children) {
        if (child.code === targetCode) {
          return [area.code, child.code];
        }
      }
    }
  }
  return null;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 更新建筑类型名称
    if (formData.buildingType) {
      const selected = buildingTypeOptions.find(item => item.value === formData.buildingType);
      if (selected) {
        formData.buildingTypeName = selected.label;
      }
    }

    const submitData = { ...formData };

    // 处理区域数据提交格式
    if (Array.isArray(formData.town) && formData.town.length > 0) {
      // 如果town是数组，取最后一个值作为town
      submitData.town = formData.town[formData.town.length - 1];
      // 如果有两级，第一个是县
      if (formData.town.length >= 2) {
        submitData.county = formData.town[0];
      }
    }

    let res;
    if (props.mode === 'add') {
      res = await saveDanger(submitData);
    } else if (props.mode === 'edit') {
      res = await updateDanger(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});
</script>

<style scoped>
.danger-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style>