<template>
  <div class="component-model-list">
      <div class="model_level">
          <div
                  v-for="item in modelData"
                  :key="item.title"
                  class="butt-normal"
                  :class="[state.selectedLevel === item.id ? 'butt-active' : '']"
                  @click="handleClickLevel(item.id)"
          >
              {{ item.title }}
          </div>
      </div>
  </div>
</template>

<script setup>
import bus from "@/utils/mitt.js";
import {reactive} from "vue";

const state = reactive({
    selectedLevel: 1,
});
const modelData = reactive([
    {
        id: 1,
        title: "泄漏溯源分析",
    },
    {
        id: 2,
        title: "爆管预警分析",
    },
    {
        id: 3,
        title: "爆管影响后果分析",
    },
]);

const handleClickLevel = (e) => {
    //todo 激活模型
    state.selectedLevel = e;
};
</script>

<style lang="scss" scoped>
.component-model-list {
  pointer-events: all;
  width: 178px;

  .model_level {
    width: 178px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(13, 37, 82, 0.8);
    border: 1px solid #182d54;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.8);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.8);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
