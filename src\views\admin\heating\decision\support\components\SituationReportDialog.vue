<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="situation-report-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告名称" prop="reportName">
            <el-input v-model="formData.reportName" placeholder="请输入报告名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报告用户" prop="reportUser">
            <el-input v-model="formData.reportUser" placeholder="请输入报告用户" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="分发单位" prop="issuedUnit">
            <el-select v-model="formData.issuedUnit" placeholder="请选择分发单位" class="w-full" @change="handleIssuedUnitChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件上传" prop="fileUrls">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              list-type="text"
              :limit="10"
              :disabled="mode === 'view'"
              multiple
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持多种文件格式，单个文件大小不超过20MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="4" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveSituationReport,
  updateSituationReport,
  getAllEnterpriseList
} from '@/api/heating';
import { uploadFile } from '@/api/upload';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 文件列表
const fileList = ref([]);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增态势数据报送',
    edit: '编辑态势数据报送',
    view: '态势数据报送详情'
  };
  return titles[props.mode] || '态势数据报送';
});

// 下拉选项数据
const enterpriseOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  reportName: '',
  reportUser: '',
  issuedUnit: '',
  issuedUnitName: '',
  fileUrls: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  reportName: [{ required: true, message: '请输入报告名称', trigger: 'blur' }],
  reportUser: [{ required: true, message: '请输入报告用户', trigger: 'blur' }],
  issuedUnit: [{ required: true, message: '请选择分发单位', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 处理文件显示
    if (newVal.fileUrls) {
      fileList.value = newVal.fileUrls.split(',').map((url, index) => ({
        name: `附件_${index + 1}`,
        url: url,
        uid: Date.now() + index
      }));
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理分发单位变化
const handleIssuedUnitChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.issuedUnitName = selected.label;
  }
};

// 文件选择变化处理
const handleFileChange = async (file, fileListParam) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    ElMessage.error('上传文件大小不能超过 20MB!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.fileUrls ? formData.fileUrls.split(',') : [];
      urls.push(response.data.url);
      formData.fileUrls = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 文件移除处理
const handleFileRemove = (file, fileListParam) => {
  if (formData.fileUrls) {
    const urls = formData.fileUrls.split(',');
    const fileIndex = fileList.value.findIndex(item => item.uid === file.uid);
    if (fileIndex > -1) {
      urls.splice(fileIndex, 1);
      formData.fileUrls = urls.join(',');
    }
  }
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveSituationReport(submitData);
    } else if (props.mode === 'edit') {
      res = await updateSituationReport(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
});
</script>

<style scoped>
.situation-report-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 