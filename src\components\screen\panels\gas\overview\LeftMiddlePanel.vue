<template>
  <PanelBox title="管网统计" class="gas-overview-left-middle">
    <div class="panel-content">
      <div class="container">
        <div class="content">
          <div v-for="(item, index) in chartData" :key="index" class="chart-item">
            <CircleChart :title="item.title" :data="item.data" :color-map="item.colorMap"
              :show-percentage="item.showPercentage" class="circle-chart-item" />
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
import CircleChart from '@/components/screen/charts/CircleChart.vue'
import { ref, onMounted } from 'vue'
import { getPipelineStatistics } from '@/api/gas'
// 定义图表数据
const chartData = ref([])

// 从API获取数据
const fetchChartData = async () => {
  try {
    const response = await getPipelineStatistics()
    if (response.code === 200) {
      // 转换API数据格式为组件需要的格式
      chartData.value = [
        {
          title: '材质',
          data: response.data.materialStatistics?.map(item => ({
            name: item.name,
            value: item.length.toFixed(2),
            unit: 'km'
          })) || [],
          showPercentage: false
        },
        {
          title: '管龄',
          data: response.data.ageStatistics?.map(item => ({
            name: item.name,
            value: item.length.toFixed(2),
            unit: 'km'
          })) || [],
          showPercentage: false
        },
        {
          title: '企业',
          data: response.data.enterpriseStatistics?.map(item => ({
            name: item.name,
            value: item.length.toFixed(2),
            unit: 'km'
          })) || [],
          showPercentage: false
        },
        {
          title: '压力级别',
          data: response.data.pressureLevelStatistics?.map(item => ({
            name: item.name,
            value: item.length,
            unit: 'km'
          })) || [],
          showPercentage: true
        }
      ]
    } else {
      console.error('获取管网统计数据失败:', response)
      console.error('响应消息:', response.message)
    }
  } catch (error) {
    console.error('获取管网统计数据失败:', error)
    console.error('完整错误对象:', error.response || error)
  }
}

onMounted(() => {
  fetchChartData()
})
</script>

<style scoped>
.gas-overview-left-middle {
  height: 370px;
}

.panel-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 35, 80, 0.5);
  border: 1px solid rgba(0, 242, 241, 0.2);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 242, 241, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  background: linear-gradient(90deg,
      rgba(0, 242, 241, 0.05) 0%,
      rgba(0, 242, 241, 0.2) 50%,
      rgba(0, 242, 241, 0.05) 100%);
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 242, 241, 0.2);
}

.panel-title {
  color: #00f2f1;
  font-size: 16px;
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 2px 5px 5px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  max-height: 370px;
}

.container {
  width: 100%;
  height: 100%;
}

.content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;
  max-height: 320px;
  padding: 6px;
}

.chart-item {
  width: calc(50% - 6px);
  max-height: 150px;
}

.circle-chart-item {
  width: 100%;
  height: 100%;
  max-height: 175px;
}

/* 使用更具体的选择器，确保样式覆盖 */
:deep(.circle-chart-container) {
  width: 100% !important;
  height: 100% !important;
  max-height: 175px !important;
}

:deep(.chart-content) {
  width: 100% !important;
  height: 100% !important;
  max-height: 175px !important;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px) {
  .gas-overview-left-middle {
    height: 320px;
  }

  .panel-content {
    padding: 0px 5px 2px;
    max-height: 320px;
  }

  .content {
    gap: 4px;
    max-height: 280px;
    padding: 2px;
  }

  .chart-item {
    max-height: 100px;
  }

  .circle-chart-item {
    max-height: 100px;
  }

  :deep(.circle-chart-container) {
    max-height: 100px !important;
    transform: scale(0.8);
    transform-origin: center;
  }

  :deep(.chart-content) {
    max-height: 105px !important;
  }

  :deep(.chart-title) {
    font-size: 14px !important;
    margin-bottom: 2px !important;
  }

  :deep(.legend-item) {
    font-size: 10px !important;
  }

  :deep(.value-text) {
    font-size: 13px !important;
  }
}

/* 910px左右高度的屏幕特别优化 */
@media (min-height: 900px) and (max-height: 939px) {
  .gas-overview-left-middle {
    height: 300px;
  }

  .panel-content {
    padding: 0px 5px 0px;
    max-height: 300px;
  }

  .content {
    gap: 2px;
    max-height: 270px;
    padding: 1px;
  }

  .chart-item {
    max-height: 90px;
  }

  .circle-chart-item {
    max-height: 90px;
  }

  :deep(.circle-chart-container) {
    max-height: 90px !important;
    transform: scale(0.75);
    transform-origin: top;
  }

  :deep(.chart-content) {
    max-height: 90px !important;
  }

  :deep(.chart-title) {
    font-size: 12px !important;
    margin-bottom: 1px !important;
  }

  :deep(.legend-item) {
    font-size: 9px !important;
  }

  :deep(.value-text) {
    font-size: 12px !important;
  }
}

/* 更小屏幕高度（小于900px）的特别优化 */
@media (max-height: 899px) {
  .gas-overview-left-middle {
    height: 280px;
  }

  .panel-content {
    padding: 0px 4px 0px;
    max-height: 280px;
  }

  .content {
    gap: 1px;
    max-height: 250px;
    padding: 0px;
  }

  .chart-item {
    max-height: 80px;
  }

  .circle-chart-item {
    max-height: 80px;
  }

  :deep(.circle-chart-container) {
    max-height: 80px !important;
    transform: scale(0.65);
    transform-origin: center;
  }

  :deep(.chart-content) {
    max-height: 80px !important;
  }

  :deep(.chart-title) {
    font-size: 11px !important;
    margin-bottom: 0px !important;
  }

  :deep(.legend-item) {
    font-size: 8px !important;
  }

  :deep(.value-text) {
    font-size: 11px !important;
  }
}
</style>