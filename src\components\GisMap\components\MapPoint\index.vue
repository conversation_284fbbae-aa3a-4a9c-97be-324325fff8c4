<template>
  <div class="map">
    <InitMap />
    <div class="coordinateLine" v-if="longitude">
      位置 ：经度：&nbsp;{{ longitude }} &nbsp; 纬度：&nbsp;{{ latitude }}
    </div>
  </div>
</template>

<script setup>
import InitMap from "@/components/GisMap/InitMap.vue";
import { collectLocation } from "@/hooks/gishooks";
import { checkCoordinate } from "@/components/GisMap/common/gisUtil";
import {
  position0,
  PolylineFlowMaterialProperty,
} from "@/components/GisMap/common/earth";
import { glslFlow } from "@/components/GisMap/common/shader";
import { mapStates } from "@/components/GisMap/mapStates";
import {onMounted, ref} from "vue";
const props = defineProps({
    point: {
        type: Array,
        required: true
    },
    startPoint: {
        type: Array,
        required: true
    },
    endPoint: {
        type: Array,
        required: true
    },
    lineColor: {
        type: String,
        required: true
    }
});
const longitude = ref(position0[0]);
const latitude = ref(position0[1]);

const materialRoadH1 = new PolylineFlowMaterialProperty({
  options: {},
  type: "roadH1",
  image: new URL(`./images/line-flow-yellow.png`, import.meta.url).href,
  imageW: 90.0, //图片尺寸,大则贴图个数少
  source: glslFlow, //glsl shader源代码
  time: 30, //流动时间，大则慢
});

const emits = defineEmits(['getCoordinate']);

const handlePickAnything = (degree) => {
  //1.清除上一个点
  mapStates.viewer.entities.removeAll();
  //2.存储坐标
  longitude.value = parseFloat(degree[0].toFixed(6));
  latitude.value = parseFloat(degree[1].toFixed(6));
  //3.画一个现在的点
  mapStates.earth.entity.addPoint(
    degree.map((item) => parseFloat(String(item)))
  );
  collectLocation.value = {
    longitude: degree[0].toFixed(6),
    latitude: degree[1].toFixed(6),
  };
};

const optimizeImageryLayer = () => {
  // 获取当前的图层
  let layer = mapStates.viewer.scene.imageryLayers.get(0);
  // 改变当前地图的组织结构
  layer.minificationFilter = Cesium.TextureMinificationFilter.NEAREST;
  layer.magnificationFilter = Cesium.TextureMagnificationFilter.NEAREST;
};

const InitMapPoint = async () => {
    longitude.value = "";
    latitude.value = "";
  //1.加载地图
    mapStates.earth.basemap.add("tdt_vec");
    mapStates.earth.basemap.add("tdt_cva");
  optimizeImageryLayer();
  if (
    props.point.length > 0 &&
    checkCoordinate(props.point[0].toString(), props.point[1].toString())
  ) {
    //1.初始位置
    mapStates.earth.camera.setView({
      lon: props.point[0],
      lat: props.point[1],
      height: position0[2],
    });
    //2.初始红点
    mapStates.earth.entity.addPoint(props.point);
    longitude.value = props.point[0];
    latitude.value = props.point[1];
  } else if (
    props.startPoint.length > 0 &&
    checkCoordinate(
      props.startPoint[0].toString(),
      props.startPoint[1].toString()
    ) &&
    props.endPoint.length > 0 &&
    checkCoordinate(props.endPoint[0].toString(), props.endPoint[1].toString())
  ) {
    mapStates.earth.entity.clearDataSourcesEntitiesByLayerId("misPipePosition");
    const newLine = {
      id: "temp_pipe_001",
      colorStr: props.lineColor,
      gisType: "misPipeRisk1",
      degrees: [
        parseFloat(String(props.startPoint[0])),
        parseFloat(String(props.startPoint[1])),
        parseFloat(String(props.endPoint[0])),
        parseFloat(String(props.endPoint[1])),
      ],
    };
    //画线
    mapStates.earth.entity.addPolylineGeometryFromDegrees({
      layerId: "misPipePosition",
      data: [newLine],
      material: materialRoadH1,
      width: 4,
    });
    //定位线的中点
    mapStates.earth.camera.flyTo({
      lon:
        (parseFloat(String(props.startPoint[0])) +
          parseFloat(String(props.endPoint[0]))) /
        2,
      lat:
        (parseFloat(String(props.startPoint[1])) +
          parseFloat(String(props.endPoint[1]))) /
          2 +
        0.0012,
      height: 1200,
      orientation: {
        heading: 0,
        pitch: -45,
        roll: 0,
      },
    });
  } else {
    mapStates.earth.camera.setView({
      lon: position0[0],
      lat: position0[1],
      height: position0[2],
    });
      //启动坐标拾取
      //地图要素点击事件，[点击事件]，[悬停事件]
      mapStates.earth.event.activatePickHandler(
          [
              () => {}, //点-billboard
              () => {}, //线-polyline
              () => {}, //文本-label
              handlePickAnything, // handlePickAnything, //所有情况
              () => {}, // handlepickNothing //点击空白处
              () => {}, // 3dtiles 管道模型
          ],
          [() => {}] //handleHoverPolyline]
      );
  }
};

onMounted(() => {
  InitMapPoint();
});

defineExpose({});
</script>

<style lang="scss" scoped>
.map {
  position: relative;
  /*height: 600px;*/
  width: 100%;
  height: 100%;

  .coordinateLine {
    position: absolute;
    z-index: 100;
    left: 0;

    bottom: 0px;
    height: 40px;
    background: #feffff;
    border-radius: 3px 3px 3px 3px;
    font-size: 15px;
    font-weight: 400;
    padding: 0 20px;
    color: #000000;
    line-height: 40px;
  }
}
</style>
