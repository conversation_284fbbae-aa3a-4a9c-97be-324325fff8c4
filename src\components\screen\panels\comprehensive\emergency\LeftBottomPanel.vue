<template>
  <PanelBox title="事件发展趋势">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-icon total"></span>
          <span class="legend-text">燃气</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-one"></span>
          <span class="legend-text">排水</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-two"></span>
          <span class="legend-text">供热</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-three"></span>
          <span class="legend-text">桥梁</span>
        </div>
      </div>
      <div class="chart-title-container">
        <span class="unit-label">单位（个）</span>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import moment from 'moment'
import { getEmergencyEventTrendStatisticsScreen } from '@/api/comprehensive'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 预设颜色
const colors = {
  total: '#055ADB',
  levelOne: '#FF311D',
  levelTwo: '#FF6817',
  levelThree: '#FFD32E'
}

// 图表数据
const chartData = ref({
  xAxis: [],
  series: [
    {
      name: '燃气',
      data: [],
      color: colors.total
    },
    {
      name: '排水',
      data: [],
      color: colors.levelOne
    },
    {
      name: '供热',
      data: [],
      color: colors.levelTwo
    },
    {
      name: '桥梁',
      data: [],
      color: colors.levelThree
    }
  ]
})

// 计算时间范围
const getTimeRange = (type) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime = ''

  switch (type) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }

  return { startTime, endTime }
}

// 从后端获取趋势数据
const fetchTrendData = async (type) => {
  try {
    const { startTime, endTime } = getTimeRange(type)
    const response = await getEmergencyEventTrendStatisticsScreen({ startTime, endTime })

    if (response.code === 200 && response.data) {
      // 转换接口数据为图表数据格式
      const chartDataResult = transformApiDataToChartData(response.data, type)

      // 更新图表数据
      chartData.value.xAxis = chartDataResult.xAxis
      chartData.value.series[0].data = chartDataResult.series[0].data
      chartData.value.series[1].data = chartDataResult.series[1].data
      chartData.value.series[2].data = chartDataResult.series[2].data
      chartData.value.series[3].data = chartDataResult.series[3].data

      updateChart()
    }
    console.log(`获取${type}趋势数据完成`)
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    // 发生错误时使用模拟数据
    generateMockData()
  }
}

// 转换API数据为图表数据格式
const transformApiDataToChartData = (apiData, type) => {
  // 根据时间范围确定数据点数量和标签格式
  let dataPoints = 7
  let labelFormat = 'day'

  if (type === 'month') {
    dataPoints = 30
    labelFormat = 'day'
  } else if (type === 'year') {
    dataPoints = 12
    labelFormat = 'month'
  }

  // 初始化数据数组
  const xAxisData = []
  const gasData = new Array(dataPoints).fill(0)
  const drainData = new Array(dataPoints).fill(0)
  const heatData = new Array(dataPoints).fill(0)
  const bridgeData = new Array(dataPoints).fill(0)

  // 生成时间轴标签
  for (let i = 0; i < dataPoints; i++) {
    let label = ''
    if (type === 'week') {
      label = i === 6 ? '周日' : `周${i + 1}`
    } else if (type === 'month') {
      label = `${i + 1}日`
    } else {
      label = `${i + 1}月`
    }
    xAxisData.push(label)
  }

  // 处理燃气数据
  if (apiData.gasTrendResponse && apiData.gasTrendResponse.length > 0) {
    apiData.gasTrendResponse.forEach(item => {
      const index = getDataIndex(item.date, type, dataPoints)
      if (index >= 0 && index < dataPoints) {
        gasData[index] += item.count || 0
      }
    })
  }

  // 处理排水数据
  if (apiData.drainTrendResponse && apiData.drainTrendResponse.length > 0) {
    apiData.drainTrendResponse.forEach(item => {
      const index = getDataIndex(item.date, type, dataPoints)
      if (index >= 0 && index < dataPoints) {
        drainData[index] += item.count || 0
      }
    })
  }

  // 处理供热数据
  if (apiData.heatTrendResponse && apiData.heatTrendResponse.length > 0) {
    apiData.heatTrendResponse.forEach(item => {
      const index = getDataIndex(item.date, type, dataPoints)
      if (index >= 0 && index < dataPoints) {
        heatData[index] += item.count || 0
      }
    })
  }

  // 处理桥梁数据
  if (apiData.bridgeTrendResponse && apiData.bridgeTrendResponse.length > 0) {
    apiData.bridgeTrendResponse.forEach(item => {
      const index = getDataIndex(item.date, type, dataPoints)
      if (index >= 0 && index < dataPoints) {
        bridgeData[index] += item.count || 0
      }
    })
  }

  return {
    xAxis: xAxisData,
    series: [
      { data: gasData },
      { data: drainData },
      { data: heatData },
      { data: bridgeData }
    ]
  }
}

// 根据日期获取数据在数组中的索引
const getDataIndex = (dateStr, type, dataPoints) => {
  const date = moment(dateStr)
  const now = moment()

  if (type === 'week') {
    const dayOfWeek = date.day() // 0=周日, 1=周一, ..., 6=周六
    return dayOfWeek === 0 ? 6 : dayOfWeek - 1 // 转换为 0=周一, ..., 6=周日
  } else if (type === 'month') {
    return date.date() - 1 // 1日对应索引0
  } else if (type === 'year') {
    return date.month() // 0=1月, 11=12月
  }

  return -1
}

// 生成测试数据（备用）
const generateMockData = () => {
  // 根据时间范围生成不同数量的数据点
  let dataPoints = 7 // 默认一周
  if (timeRange.value === 'month') {
    dataPoints = 30
  } else if (timeRange.value === 'year') {
    dataPoints = 12
  }

  const xAxisData = []
  const totalData = []
  const levelOneData = []
  const levelTwoData = []
  const levelThreeData = []

  // 基础数值，用于生成随机数据
  const baseValue = {
    total: 50,
    levelOne: 30,
    levelTwo: 40,
    levelThree: 35
  }

  for (let i = 1; i <= dataPoints; i++) {
    let label = ''
    if (timeRange.value === 'week') {
      label = i == 7 ? '周日' : `周${i}`
    } else if (timeRange.value === 'month') {
      label = `${i}日`
    } else {
      label = `${i}月`
    }
    xAxisData.push(label)

    // 生成随机值，模拟波动
    const random = (min, max) => Math.floor(Math.random() * (max - min + 1) + min)

    // 模拟桥梁数据
    const levelThreeValue = random(baseValue.levelThree - 25, baseValue.levelThree + 45)
    levelThreeData.push(levelThreeValue)

    // 模拟供热数据
    const levelTwoValue = random(baseValue.levelTwo - 15, baseValue.levelTwo + 30)
    levelTwoData.push(levelTwoValue)

    // 模拟排水数据
    const levelOneValue = random(baseValue.levelOne - 15, baseValue.levelOne + 35)
    levelOneData.push(levelOneValue)

    // 燃气略高于所有报警之和
    const totalValue = levelOneValue + levelTwoValue + random(0, 10)
    totalData.push(totalValue)
  }

  // 更新图表数据
  chartData.value.xAxis = xAxisData
  chartData.value.series[0].data = totalData
  chartData.value.series[1].data = levelOneData
  chartData.value.series[2].data = levelTwoData
  chartData.value.series[3].data = levelThreeData
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchTrendData(value)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '燃气',
        type: 'line',
        data: chartData.value.series[0].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.total
        },
        lineStyle: {
          color: colors.total,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(5, 90, 219, 0.5)' },
              { offset: 1, color: 'rgba(5, 90, 219, 0)' }
            ]
          }
        }
      },
      {
        name: '排水',
        type: 'line',
        data: chartData.value.series[1].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelOne
        },
        lineStyle: {
          color: colors.levelOne,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 49, 29, 0.5)' },
              { offset: 1, color: 'rgba(255, 49, 29, 0)' }
            ]
          }
        }
      },
      {
        name: '供热',
        type: 'line',
        data: chartData.value.series[2].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelTwo
        },
        lineStyle: {
          color: colors.levelTwo,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 104, 23, 0.5)' },
              { offset: 1, color: 'rgba(255, 104, 23, 0)' }
            ]
          }
        }
      },
      {
        name: '桥梁',
        type: 'line',
        data: chartData.value.series[3].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelThree
        },
        lineStyle: {
          color: colors.levelThree,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 219, 35, 0.5)' },
              { offset: 1, color: 'rgba(255, 196, 35, 0)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: chartData.value.xAxis
    },
    series: [
      { data: chartData.value.series[0].data },
      { data: chartData.value.series[1].data },
      { data: chartData.value.series[2].data },
      { data: chartData.value.series[3].data }
    ]
  })
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  fetchTrendData(timeRange.value)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.com-select {
  margin-right: 20px;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 20px;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  padding: 1px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 2px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.level-one {
  background: #FF311D;
}

.legend-icon.level-two {
  background: #FF6817;
}

.legend-icon.level-three {
  background: #FFD32E;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  min-height: 150px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }

  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .chart-wrapper {
    min-height: 170px;
  }
}
</style>