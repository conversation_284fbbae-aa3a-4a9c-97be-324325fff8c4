<template>
  <div class="gas-network-line">
    <!-- 搜索区域 -->
    <div class="search-section">
      <GasNetworkSearch @search="handleSearch" @reset="handleReset" />
    </div>
    
    <!-- 按钮和总长度区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
      <div class="total-info">
        管线总长度: {{ totalLength }}
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :max-height="tableMaxHeight"
      :fit="true"
      :scrollbar-always-on="true"
      style="width: 100%"
      v-loading="loading"
      empty-text="暂无数据"
    >
      <el-table-column label="序号" min-width="60" fixed="left">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="pipelineCode" label="管线编码" min-width="100" />
      <el-table-column prop="pressureLevelName" label="压力级别" min-width="80" />
      <el-table-column prop="buriedTypeName" label="埋设类型" min-width="100" />
      <el-table-column prop="designPressure" label="设计压力(Mpa)" min-width="120" />
      <el-table-column prop="materialName" label="材质" min-width="80" />
      <el-table-column prop="pipeDiameter" label="管径(DN)" min-width="100" />
      <el-table-column prop="pipeLength" label="长度(KM)" min-width="100" />
      <el-table-column prop="roadName" label="所在道路" min-width="120" />
      <el-table-column prop="startPointDepth" label="起点埋深(m)" min-width="120" />
      <el-table-column prop="endPointDepth" label="终点埋深(m)" min-width="120" />
      <el-table-column prop="constructionTime" label="建设时间" min-width="170" />
      <el-table-column prop="managementUnitName" label="权属单位" min-width="220" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.managementUnitName" class="text-ellipsis">
            {{ scope.row.managementUnitName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="usageStatusName" label="使用状态" min-width="80" />
      <el-table-column label="操作" min-width="220" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
            </div>
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 - 固定在底部 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 弹窗组件 -->
    <GasNetworkLineDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { getGasPipelinePage, getGasPipelineDetail, deleteGasPipeline } from '@/api/gas';
import GasNetworkSearch from './GasNetworkSearch.vue';
import GasNetworkLineDialog from './GasNetworkLineDialog.vue';
import { misPosition } from '@/hooks/gishooks';


const props = defineProps({
  tableData: {
    type: Array,
    required: true
  },
  totalLength: {
    type: String,
    default: '0KM'
  }
});

// 表格最大高度相关
const tableMaxHeight = ref(500);
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const totalLength = ref('0KM');

// 查询参数（可扩展为表单联动）
const queryParams = ref({});

// 计算表格高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.gas-network-line');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  fetchPipelineData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchPipelineData();
};

// 获取管网分页数据
const fetchPipelineData = async () => {
  loading.value = true;
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...queryParams.value
  };
  const res = await getGasPipelinePage(params);
  if (res && res.data) {
    loading.value = false;
    tableData.value = res.data.records || [];
    total.value = res.data.total || 0;
    totalLength.value = res.data.totalLength ? res.data.totalLength + 'KM' : '0KM';
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchPipelineData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchPipelineData();
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 打开新增弹窗
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = async (row) => {
  try {
    const res = await getGasPipelineDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取详情失败', error);
  }
};

// 打开详情弹窗
const handleDetail = async (row) => {
  try {
    const res = await getGasPipelineDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取详情失败', error);
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该管线信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteGasPipeline(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPipelineData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败', error);
    }
  }).catch(() => {});
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.startPointLongitude &&
    row.startPointLongitude != '' &&
    row.startPointLatitude &&
    row.startPointLatitude != ''
  ) {
    misPosition.value = {
      longitude: row.startPointLongitude, //经度
      latitude: row.startPointLatitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理导入
const handleImport = () => {
  console.log('导入');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
};

// 弹窗提交成功回调
const handleDialogSuccess = () => {
  fetchPipelineData();
};

// 在组件挂载后设置表格高度
onMounted(() => {
  fetchPipelineData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
});

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.gas-network-line {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 8px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

.total-info {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #282828;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
