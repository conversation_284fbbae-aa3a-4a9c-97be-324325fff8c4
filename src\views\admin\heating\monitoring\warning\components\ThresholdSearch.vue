<template>
  <div class="threshold-search-container">
    <el-form ref="searchFormRef" :model="searchForm" inline>
      <el-form-item label="规则名称:">
        <el-input v-model="searchForm.ruleName" placeholder="请输入规则名称" clearable />
      </el-form-item>
      <el-form-item label="是否生效:">
        <el-select v-model="searchForm.isEnabled" placeholder="是" clearable style="width: 100px;">
          <el-option
            v-for="item in enableOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ENABLE_STATUS } from '@/constants/heating';

const emit = defineEmits(['search', 'reset']);

// 搜索表单
const searchFormRef = ref(null);
const searchForm = reactive({
  ruleName: '',
  isEnabled: ''
});

// 是否生效选项
const enableOptions = ENABLE_STATUS;

// 查询
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
  searchForm.ruleName = '';
  searchForm.isEnabled = '';
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  emit('reset');
};
</script>

<style scoped>
.threshold-search-container {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
}
</style>