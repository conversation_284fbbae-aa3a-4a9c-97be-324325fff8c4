<template>
  <PanelBox title="桥梁监控" class="left-bottom-panel">
    <!-- <template #extra>
      <div class="more-btn" @click="openVideoListModal">
        更多
      </div>
    </template> -->
    <div class="panel-content">
      <!-- 4路视频播放网格 -->
      <div class="video-grid">
        <div class="video-item" v-for="(video, index) in videoList" :key="index">
          <div class="video-title" :title="video.title">{{ video.title }}</div>
          <VideoPlayer 
            :src="video.src" 
            :muted="true" 
            :autoplay="true"
            :showControls="true"
            :ref="el => { if (el) playerRefs[index] = el }"
            class="video-player"
          />
        </div>
      </div>
    </div>
  </PanelBox>
  <!-- 视频列表弹窗 -->
  <VideoListModal v-model="showVideoListModal" />
</template>

<script setup>
// 综合态势总览左下面板组件
import { ref, onMounted, onUnmounted } from 'vue';
import PanelBox from '@/components/screen/PanelBox.vue'
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue';
import SimpleRtspPlayer from '@/components/rtsp/SimpleRtspPlayer.vue';
import VideoListModal from './VideoListModal.vue';

// 定义Props - 允许外部传入WebSocket URL
const props = defineProps({
  wsUrl: {
    type: String,
    default: 'ws://**************:32021/basic/jsmpeg2'
  },
  wsUrl2: {
    type: String,
    default: 'ws://**************:32021/basic/jsmpeg'
  }
});

// 播放器引用集合
const playerRefs = ref([]);

// 视频列表数据 - 恢复到4路播放
const videoList = ref([
{
    title: 'B_南华路南护城河桥北(西侧)第一立杆_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000024/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'B_南华路南护城河桥南(东侧)立杆球机_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000023/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'B_东明湖大桥南(路西)立杆球机_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000020/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'B_城北路西护城河桥东（路南）立杆球机_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000021/hls.m3u8?originTypeStr=rtp_push'
  }
]);

// 处理播放器错误
const handlePlayerError = (index, error) => {
  console.error(`🔍 [4路播放] 播放器 ${index} (${videoList.value[index].videoId}) 发生错误:`, error);
  videoList.value[index].error = error.message || '播放失败';
  
  // 10秒后自动清除错误，尝试重新连接
  setTimeout(() => {
    console.log(`🔍 [4路播放] 播放器 ${index} 自动重试连接`);
    videoList.value[index].error = null;
    if (playerRefs.value[index]) {
      playerRefs.value[index].play();
    }
  }, 10000);
};

// 视频列表弹窗显示状态
const showVideoListModal = ref(false);

// "更多"按钮点击事件
const openVideoListModal = () => {
  showVideoListModal.value = true;
};

// 组件挂载时初始化
onMounted(() => {
  console.log('🔍 [4路播放] 桥梁监控面板初始化完成，使用修复后的MultiRtspPlayer');
  console.log('🔍 [4路播放] 视频列表:', videoList.value.map(v => v.videoId));
});

// 组件卸载时处理
onUnmounted(() => {
  console.log('🔍 [4路播放] 销毁所有播放器');
  // 销毁播放器，释放资源
  playerRefs.value.forEach((player, index) => {
    if (player && player.destroy) {
      console.log(`🔍 [4路播放] 销毁播放器 ${index}`);
      player.destroy();
    }
  });
});
</script>

<style scoped>
.left-bottom-panel {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  padding: 15px 15px 10px 15px; /* 底部保留10px边距 */
  display: flex;
  flex-direction: column;
  gap: 0px;
  height: 100%; /* 占满容器高度 */
  box-sizing: border-box; /* 确保padding计算在内 */
}

/* 视频网格布局 - 2列2行 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  height: 100%;
  width: 100%;
  flex: 1; /* 确保占满剩余空间 */
  min-height: 0; /* 允许网格收缩 */
}

.video-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 242, 241, 0.2);
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 32px;
  background: #000000;
  opacity: 0.4;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 10px;
  color: #FFFFFF;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: #FF6B6B;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 20;
  text-align: center;
  max-width: 80%;
}

/* 视频播放器响应式调整 */
.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  margin-right: 30px;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 响应式布局 */
:root {
  --vh-ratio: 1;
}

@media screen and (max-height: 940px) {
  :root {
    --vh-ratio: 0.85;
  }

  .panel-content {
    padding: 12px 12px 10px 12px; /* 减少内边距但保持底部10px */
  }

  .video-grid {
    gap: 8px; /* 减少间距以节省空间 */
  }

  .video-title {
    height: 24px;
    font-size: 11px;
    width: 100%;
  }

  /* 移除固定高度，让网格自适应 */
}

@media screen and (min-height: 1080px) {
  :root {
    --vh-ratio: 1.15;
  }

  .video-title {
    height: 36px;
    font-size: 12px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-height: 800px) {
  .panel-content {
    padding: 8px 8px 10px 8px; /* 保持底部10px边距 */
  }

  .video-grid {
    gap: 6px;
  }

  .video-title {
    height: 20px;
    width: 160px;
    font-size: 10px;
  }
}

/* 针对910px左右高度的特殊适配 */
@media screen and (min-height: 900px) and (max-height: 950px) {
  .panel-content {
    padding: 8px 8px 10px 8px; /* 减少padding，底部保留10px */
  }

  .video-grid {
    gap: 6px; /* 减少间距以节省空间 */
  }

  .video-title {
    height: 24px;
    font-size: 10px;
    width: 100%;
  }
}
</style>