<template>
  <div class="side-menu-container">
    <el-menu
      ref="menuRef"
      :key="menuKey"
      :default-active="activeMenu"
      class="el-menu-vertical"
      :collapse="isCollapse"
      :unique-opened="false"
      :router="true"
      :default-openeds="defaultOpeneds"
      @open="handleOpen"
      @close="handleClose"
    >
      <template v-for="(item, index) in menuList" :key="index">
        <!-- 这些是3级菜单项 -->
        <el-sub-menu 
          :index="item.path" 
          class="third-level-menu"
          :data-path="item.path"
        >
          <template #title>
            <el-icon v-if="item.meta && item.meta.icon">
              <component :is="item.meta.icon" />
            </el-icon>
            <span>{{ item.meta ? item.meta.title : '未命名菜单' }}</span>
          </template>

          <!-- 第四级菜单 -->
          <template v-for="(fourthItem, fourthIndex) in item.children" :key="fourthIndex">
            <el-menu-item :index="fourthItem.path" class="fourth-level-menu">
              <el-icon v-if="fourthItem.meta && fourthItem.meta.icon">
                <component :is="fourthItem.meta.icon" />
              </el-icon>
              <template #title>
                <span>{{ fourthItem.meta ? fourthItem.meta.title : '未命名菜单' }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 接收父组件传递的属性
const props = defineProps({
  // 菜单列表
  menuList: {
    type: Array,
    default: () => []
  },
  // 是否折叠菜单
  isCollapse: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()
const router = useRouter()
const menuRef = ref(null)
const menuKey = ref(0)

// 计算当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 处理菜单打开事件
function handleOpen(index) {
  console.log('菜单打开:', index)
}

// 处理菜单关闭事件
function handleClose(index) {
  console.log('菜单关闭:', index)
}

// 清除localStorage中的关闭菜单状态，确保所有菜单默认展开
onMounted(() => {
  try {
    localStorage.removeItem('closedMenus')
    console.log('已清除菜单关闭状态，所有菜单将默认展开')
  } catch (e) {
    console.error('清除菜单状态失败:', e)
  }
})

// 收集所有三级菜单路径（只有sub-menu需要在default-openeds中）
const getAllSubMenuPaths = () => {
  const paths = []
  
  props.menuList.forEach(item => {
    if (item && typeof item.path === 'string') {
      paths.push(item.path)
    }
  })
  
  return paths
}

// 用于获取应该默认展开的菜单路径
const defaultOpeneds = computed(() => {
  try {
    // 如果菜单处于折叠状态，不展开任何菜单
    if (props.isCollapse) {
      return []
    }
    
    // 获取所有三级菜单路径，默认全部展开
    const allSubMenuPaths = getAllSubMenuPaths()
    
    console.log("默认展开菜单路径:", allSubMenuPaths)
    return allSubMenuPaths
  } catch (e) {
    console.error('获取默认展开菜单路径出错:', e)
    return []
  }
})



// 监听折叠状态变化
watch(
  () => props.isCollapse,
  (newVal) => {
    if (!newVal) {
      // 从折叠状态展开时，确保菜单正确展开
      nextTick(() => {
        const paths = defaultOpeneds.value
        console.log('折叠状态变化后确保菜单展开:', paths)
      })
    }
  }
)

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    // 路由变化时，确保菜单状态正确
    nextTick(() => {
      // 触发默认展开菜单的重新计算
      const paths = defaultOpeneds.value
      console.log('路由变化后确保菜单展开:', paths)
    })
  }
)

// 监听菜单列表变化，强制重新渲染菜单组件
watch(
  () => props.menuList,
  (newMenuList, oldMenuList) => {
    // 当菜单列表发生变化时，更新key强制重新渲染
    menuKey.value++
    console.log('菜单列表变化，强制重新渲染菜单，新的menuKey:', menuKey.value)
    console.log('新菜单列表:', newMenuList)
    
    nextTick(() => {
      // 确保菜单重新渲染后，所有三级菜单都展开
      const paths = defaultOpeneds.value
      console.log('菜单重新渲染后，默认展开的菜单路径:', paths)
      
      // 如果菜单引用存在，手动打开所有子菜单
      if (menuRef.value && paths.length > 0) {
        paths.forEach(path => {
          try {
            menuRef.value.open(path)
            console.log('手动打开菜单:', path)
          } catch (e) {
            console.warn('打开菜单失败:', path, e)
          }
        })
      }
    })
  },
  { deep: true, immediate: false }
)
</script>

<style scoped>
.side-menu-container {
  height: 100%;
  border-right: 1px solid #e6e6e6;
  width: 240px; /* 修改侧边栏宽度为240px */
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 240px; /* 修改菜单宽度为240px */
  min-height: 400px;
}

.el-menu-vertical {
  height: 100%;
  border-right: none;
}

/* 三级菜单样式 */
:deep(.third-level-menu .el-sub-menu__title) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #000000;
  padding-left: 16px !important;
  height: 40px;
  line-height: 40px;
}

/* 三级菜单选中状态 */
:deep(.third-level-menu.is-active .el-sub-menu__title) {
  color: #0277FD !important;
}

/* 确保三级菜单和四级菜单之间的间距统一为16px */
:deep(.el-menu--inline) {
  padding-top: 16px !important;
  padding-bottom: 0 !important;
}

/* 四级菜单样式 */
:deep(.fourth-level-menu) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  height: 40px;
  line-height: 40px;
  padding-left: 37px !important;
  margin-bottom: 16px; /* 添加四级菜单之间的间距 */
}

/* 最后一个四级菜单不需要底部间距 */
:deep(.el-menu--inline > .fourth-level-menu:last-child) {
  margin-bottom: 0;
}

/* 四级菜单选中状态 */
:deep(.fourth-level-menu.is-active) {
  color: #0277FD !important;
  position: relative;
  background: linear-gradient(270deg, rgba(2,119,253,0) 0%, rgba(2,119,253,0.16) 100%) !important;
  border-radius: 24px 0px 0px 24px !important;
  margin-left: 16px !important;
  margin-right: 16px !important;
  width: calc(100% - 32px) !important;
  padding-left: 21px !important; /* 37px - 16px = 21px, 确保文字位置不变 */
}

/* 重置默认的选中背景色 */
:deep(.el-menu-item.is-active) {
  background-color: transparent;
}

/* 覆盖 ElementPlus 默认的选中颜色 */
:deep(.el-menu-item.is-active),
:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: #0277FD !important;
}

/* 覆盖折叠状态的宽度 */
:deep(.el-menu--collapse) {
  width: 64px;
}

/* 确保菜单项文本不会被截断 */
:deep(.el-menu-item span), 
:deep(.el-sub-menu__title span) {
  white-space: normal;
  word-break: break-word;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 170px; /* 对应减少文本区域宽度 */
}

/* 覆盖ElementPlus的原生样式，确保间距一致 */
:deep(.el-sub-menu__icon-arrow) {
  right: 16px;
}

/* 强制重置菜单底部间距 */
:deep(.el-sub-menu.is-opened) {
  margin-bottom: 0 !important;
}

/* 确保菜单展开箭头不会影响间距 */
:deep(.el-sub-menu__title) {
  margin-bottom: 0 !important;
}
</style>