import {
    postBridgeMapDevicePointsList,
    postBridgeUsmBridgeBasicList,
    postComUsmEmergencyEventList,
    postComUsmEmergencyHospitalList,
    postComUsmEmergencyRespondersList,
    postComUsmEmergencyShelterList,
    postComUsmEmergencyStoreList,
    postComUsmEmergencySuppliesList,
    postComUsmEmergencyTeamList,
    postComUsmRiskHiddenDangerList, postComUsmWarningList,
    postDrainUsmBasicFloodPointList,
    postDrainUsmBasicPipelineList,
    postDrainUsmBasicPointList,
    postDrainUsmBasicWellList,
    postDrainUsmMonitorDeviceList,
    postDrainUsmRiskDangerList,
    postDrainUsmRiskFactoryList,
    postDrainUsmRiskHiddenDangerList,
    postDrainUsmRiskPipelineList,
    postDrainUsmRiskProtectList,
    postDrainUsmRiskStationList,
    postHeat<PERSON>sm<PERSON>asic<PERSON>nterpriseList,
    postHeat<PERSON>sm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>actoryList,
    postHeatUsmBasicHeatStationList,
    postHeatUsmBasicPipelineList,
    postHeatUsmBasicPointList,
    postHeatUsmBasicUserList,
    postHeatUsmBasicWellList,
    postHeatUsmMonitorDeviceList,
    postHeatUsmRiskDangerList,
    postHeatUsmRiskFactoryList,
    postHeatUsmRiskHiddenDangerList,
    postHeatUsmRiskPipelineList,
    postHeatUsmRiskProtectList,
    postHeatUsmRiskStationList,
    postUsmBasicDrainOutletList,
    postUsmBasicPumpStationList,
    postUsmBasicSewageFactoryList,
    postUsmMonitorDeviceList,
    postUsmVideoStreamPointList,
    postUsmZyGasDangerList,
    postUsmZyGasPipelineList,
    postUsmZyGasPointList,
    postUsmZyGasProtectList,
    postUsmZyGasRiskPipelineList,
    postUsmZyGasRiskStationList,
    postUsmZyGasStationList,
    postUsmZyGasWellList
} from "@/api/layerData.js";

export const defaultShowLayersMap = {
    "/comprehensive/overview": [
        'com_warning_level1', 'com_warning_level2', 'com_warning_level3',
    ], // 综合管理-总览
    "/comprehensive/risk": [
        'com_warning_level1', 'com_warning_level2', 'com_warning_level3',
    ], // 综合管理-风险隐患
    "/comprehensive/monitoring": [
        'com_warning_level1', 'com_warning_level2', 'com_warning_level3',
    ], // 综合管理-运行监测
    "/comprehensive/coordination": [
        'com_warning_level1', 'com_warning_level2', 'com_warning_level3',
    ], // 综合管理-协同指挥
    "/comprehensive/emergency": [
        'com_warning_level1', 'com_warning_level2', 'com_warning_level3',
    ], // 综合管理-应急辅助决策
    "/gas/overview": [
        'gas_alarm1', 'gas_alarm2', 'gas_alarm3'
    ], // 燃气管理-总览
    "/gas/network-risk": [
        'gas_alarm1', 'gas_alarm2', 'gas_alarm3'
    ], // 燃气管理-管线风险
    "/gas/monitoring": [
        'gas_alarm1', 'gas_alarm2', 'gas_alarm3'
    ], // 燃气管理-运行监测
    "/gas/decision-screen": [
        'gas_alarm1', 'gas_alarm2', 'gas_alarm3'
    ], // 燃气管理-辅助决策
    "/drainage/overview": [
        'drainage_alarm1', 'drainage_alarm2', 'drainage_alarm3'
    ], // 排水管理-总览
    "/drainage/risk": [
        'drainage_alarm1', 'drainage_alarm2', 'drainage_alarm3'
    ], // 排水管理-风险隐患
    "/drainage/flooding-risk": [
        'drainage_alarm1', 'drainage_alarm2', 'drainage_alarm3'
    ], // 排水管理-易涝点风险
    "/drainage/monitoring": [
        'drainage_alarm1', 'drainage_alarm2', 'drainage_alarm3'
    ], // 排水管理-运行监测
    "/drainage/decision": [
        'drainage_alarm1', 'drainage_alarm2', 'drainage_alarm3'
    ], // 排水管理-辅助决策
    "/heating/overview": [
        'heating_alarm1', 'heating_alarm2', 'heating_alarm3'
    ], // 供热管理-总览
    "/heating/risk": [
        'heating_alarm1', 'heating_alarm2', 'heating_alarm3'
    ], // 供热管理-风险隐患
    "/heating/monitoring": [
        'heating_alarm1', 'heating_alarm2', 'heating_alarm3'
    ], // 供热管理-运行监测
    "/heating/decision": [
        'heating_alarm1', 'heating_alarm2', 'heating_alarm3'
    ], // 供热管理-辅助决策
    "/bridge": [
        'bridge_alarm1', 'bridge_alarm2', 'bridge_alarm3', 'bridge_info', 'bridge_safety_rating'
    ], // 桥梁管理-总览
    "/bridge/overview": [
        'bridge_alarm1', 'bridge_alarm2', 'bridge_alarm3', 'bridge_info', 'bridge_safety_rating'
    ], // 桥梁管理-总览
    "/bridge/risk": [
        'bridge_alarm1', 'bridge_alarm2', 'bridge_alarm3', 'bridge_info', 'bridge_safety_rating'
    ], // 桥梁管理-风险隐患
    "/bridge/monitoring": [
        'bridge_alarm1', 'bridge_alarm2', 'bridge_alarm3', 'bridge_info', 'bridge_safety_rating'
    ], // 桥梁管理-运行监测
    "/bridge/decision": [
        'bridge_alarm1', 'bridge_alarm2', 'bridge_alarm3', 'bridge_info', 'bridge_safety_rating'
    ], // 桥梁管理-辅助决策
};

//请求数据的图层对应的入参
export const requestDataMap = {
    high_pressure_gas_pipeline: {
        api: postUsmZyGasPipelineList,
        params: {
            pressureLevel: 1003, // 高压管道,
        },
    },
    mid_pressure_gas_pipeline: {
        api: postUsmZyGasPipelineList,
        params: {
            pressureLevel: 1002, // 中压管道,
        },
    },
    low_pressure_gas_pipeline: {
        api: postUsmZyGasPipelineList,
        params: {
            pressureLevel: 1001, // 低压管道,
        },
    },
    gas_pipeline_point: {
        api: postUsmZyGasPointList,
        params: {},
    }, // 燃气管线点
    gas_station: {
        api: postUsmZyGasStationList,
        params: {},
    },
    gas_well: {
        api: postUsmZyGasWellList,
        params: {},
    },
    gas_dangerous_source: {
        api: postUsmZyGasDangerList,
        params: {},
    },
    gas_protection_target: {
        api: postUsmZyGasProtectList,
        params: {},
    },
    gas_video: {
        api: postUsmVideoStreamPointList,
        params: {},
    },
    gas_combustible: {
        api: postUsmMonitorDeviceList,
        params: {
            deviceTypes: ['laserMethane', 'wbFixedCh4GrassDetector', 'aoruideMethane'],
        },
    },
    gas_manhole_cover: {
        api: postUsmMonitorDeviceList,
        params: {
            deviceTypes: ['manholeCoverDevice', 'wbManholeCoverSensor', 'thManholeCover'],
        },
    },
    gas_pipeline_risk1: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7001, //重大风险
        },
    },
    gas_pipeline_risk2: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7002, //较大风险
        },
    },
    gas_pipeline_risk3: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7003, //一般风险
        },
    },
    gas_pipeline_risk4: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7004, //低风险
        },
    },
    gas_station_risk1: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7001, //重大风险
        },
    },
    gas_station_risk2: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7002, //较大风险
        },
    },
    gas_station_risk3: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7003, //一般风险
        },
    },
    gas_station_risk4: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7004, //低风险
        },
    },
    drainage_rain_pipeline: {
        api: postDrainUsmBasicPipelineList,
        params: {},
    },
    drainage_sewage_pipeline: {
        api: postDrainUsmBasicPipelineList,
        params: {},
    },
    drainage_rainAndSewage_pipeline: {
        api: postDrainUsmBasicPipelineList,
        params: {},
    },
    drainage_pump_station: {
        api: postUsmBasicPumpStationList,
        params: {},
    },
    drainage_sewage_works: {
        api: postUsmBasicSewageFactoryList,
        params: {},
    },
    drainage_water_outlet: {
        api: postUsmBasicDrainOutletList,
        params: {},
    },
    drainage_pipeline_point: {
        api: postDrainUsmBasicPointList,
        params: {},
    },
    drainage_well: {
        api: postDrainUsmBasicWellList,
        params: {},
    },
    drainage_flooding_point: {
        api: postDrainUsmBasicFloodPointList,
        params: {},
    },
    drainage_dangerous_source: {
        api: postDrainUsmRiskDangerList,
        params: {},
    },
    drainage_protection_target: {
        api: postDrainUsmRiskProtectList,
        params: {},
    },
    drainage_video: {
        api: postUsmVideoStreamPointList,
        params: {},
    },
    drainage_combustible: {
        api: postDrainUsmMonitorDeviceList,
        params: {
            deviceTypes: ['laserMethane', 'aoruideMethane', 'wbFixedCh4GrassDetector'],
        },
    },
    drainage_manhole_cover: {
        api: postDrainUsmMonitorDeviceList,
        params: {
            deviceTypes: ['manholeCoverDevice', 'wbManholeCoverSensor', 'thManholeCover'],
        },
    },
    drainage_level: {
        api: postDrainUsmMonitorDeviceList,
        params: {
            deviceTypes: ['wbUgLevelGauger', 'loraWaterMonitor'],
        },
    },
    drainage_water_quality: {
        api: postDrainUsmMonitorDeviceList,
        params: {
            deviceTypes: ['waterQualityMonitor', 'wbWaterQualityMonitor'],
        },
    },
    drainage_pipeline_risk1: {
        api: postDrainUsmRiskPipelineList,
        params: {
            riskLevel: 3002401, //重大风险
        },
    },
    drainage_pipeline_risk2: {
        api: postDrainUsmRiskPipelineList,
        params: {
            riskLevel: 3002402, //较大风险
        },
    },
    drainage_pipeline_risk3: {
        api: postDrainUsmRiskPipelineList,
        params: {
            riskLevel: 3002403, //一般风险
        },
    },
    drainage_pipeline_risk4: {
        api: postDrainUsmRiskPipelineList,
        params: {
            riskLevel: 3002404, //低风险
        },
    },
    drainage_sewage_risk1: {
        api: postDrainUsmRiskFactoryList,
        params: {
            riskLevel: 3002401, //重大风险
        },
    },
    drainage_sewage_risk2: {
        api: postDrainUsmRiskFactoryList,
        params: {
            riskLevel: 3002402, //较大风险
        },
    },
    drainage_sewage_risk3: {
        api: postDrainUsmRiskFactoryList,
        params: {
            riskLevel: 3002403, //一般风险
        },
    },
    drainage_sewage_risk4: {
        api: postDrainUsmRiskFactoryList,
        params: {
            riskLevel: 3002404, //低风险
        },
    },
    drainage_pump_risk1: {
        api: postDrainUsmRiskStationList,
        params: {
            riskLevel: 3002401, //重大风险
        },
    },
    drainage_pump_risk2: {
        api: postDrainUsmRiskStationList,
        params: {
            riskLevel: 3002402, //较大风险
        },
    },
    drainage_pump_risk3: {
        api: postDrainUsmRiskStationList,
        params: {
            riskLevel: 3002403, //一般风险
        },
    },
    drainage_pump_risk4: {
        api: postDrainUsmRiskStationList,
        params: {
            riskLevel: 3002404, //低风险
        },
    },
    drainage_hidden_risk1: {
        api: postDrainUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 3002701, //重大隐患
        },
    },
    drainage_hidden_risk2: {
        api: postDrainUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 3002702, //较大隐患
        },
    },
    drainage_hidden_risk3: {
        api: postDrainUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 3002703, //一般隐患
        },
    },
    heating_pipeline_1: {
        api: postHeatUsmBasicPipelineList,
        params: {
            pipelineType: 2000201, //一次网
        },
    },
    heating_pipeline_2: {
        api: postHeatUsmBasicPipelineList,
        params: {
            pipelineType: 2000202, //二次网
        },
    },
    heating_pipeline_point: {
        api: postHeatUsmBasicPointList,
        params: {},
    },
    heating_well: {
        api: postHeatUsmBasicWellList,
        params: {},
    },
    heating_enterprise: {
        api: postHeatUsmBasicEnterpriseList,
        params: {},
    },
    heating_source_works: {
        api: postHeatUsmBasicHeatFactoryList,
        params: {},
    },
    heating_station: {
        api: postHeatUsmBasicHeatStationList,
        params: {},
    },
    heating_user: {
        api: postHeatUsmBasicUserList,
        params: {},
    },
    heating_dangerous_source: {
        api: postHeatUsmRiskDangerList,
        params: {},
    },
    heating_protection_target: {
        api: postHeatUsmRiskProtectList,
        params: {},
    },
    heating_video: {
        api: postUsmVideoStreamPointList,
        params: {},
    },
    heating_combustible: {
        api: postHeatUsmMonitorDeviceList,
        params: {
            deviceTypes: ['laserMethane', 'wbFixedCh4GrassDetector', 'aoruideMethane'],
        },
    },
    heating_manhole_cover: {
        api: postHeatUsmMonitorDeviceList,
        params: {
            deviceTypes: ['manholeCoverDevice', 'wbManholeCoverSensor', 'thManholeCover'],
        },
    },
    heating_temperature: {
        api: postHeatUsmMonitorDeviceList,
        params: {
            deviceTypes: ['wbManholeThSensor'],
        },
    },
    heating_pipeline_risk1: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002801, //重大风险
        },
    },
    heating_pipeline_risk2: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002802, //较大风险
        },
    },
    heating_pipeline_risk3: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002803, //一般风险
        },
    },
    heating_pipeline_risk4: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002804, //低风险
        },
    },
    heating_source_risk1: {
        api: postHeatUsmRiskFactoryList,
        params: {
            riskLevel: 3002401, //重大风险
        },
    },
    heating_source_risk2: {
        api: postHeatUsmRiskFactoryList,
        params: {
            riskLevel: 3002402, //较大风险
        },
    },
    heating_source_risk3: {
        api: postHeatUsmRiskFactoryList,
        params: {
            riskLevel: 3002403, //一般风险
        },
    },
    heating_source_risk4: {
        api: postHeatUsmRiskFactoryList,
        params: {
            riskLevel: 3002404, //低风险
        },
    },
    heating_station_risk1: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002801, //重大风险
        },
    },
    heating_station_risk2: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002802, //较大风险
        },
    },
    heating_station_risk3: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002803, //一般风险
        },
    },
    heating_station_risk4: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002804, //低风险
        },
    },
    heating_hidden_risk1: {
        api: postHeatUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 2002401, //重大隐患
        },
    },
    heating_hidden_risk2: {
        api: postHeatUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 2002402, //较大隐患
        },
    },
    heating_hidden_risk3: {
        api: postHeatUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 2002403, //一般隐患
        },
    },
    bridge_info: {
        api: postBridgeUsmBridgeBasicList,
        params: {
            isShowScore: '0' // 0显示评分,1不显示评分
        },
    },
    bridge_safety_rating: {
        api: postBridgeUsmBridgeBasicList,
        params: {
            isShowScore: '0' // 0显示评分,1不显示评分
        },
    },
    bridge_temperature: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeGxWd', 'bridgeJkWd', 'bridgeZxWd'],
        },
    },
    bridge_displacement: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeGxWy', 'bridgeJkWy', 'bridgeZxWy'],
        },
    },
    bridge_static_level: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeGxJl', 'bridgeJkJl', 'bridgeZxJl'],
        },
    },
    bridge_crack_sensor: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeGxLf', 'bridgeJkLf', 'bridgeZxLf'],
        },
    },
    bridge_strain: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeGxYb', 'bridgeJkYb', 'bridgeZxYb'],
        },
    },
    bridge_tilt: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeGxQj', 'bridgeJkQj', 'bridgeZxQj'],
        },
    },
    bridge_cable_tension: {
        api: postBridgeMapDevicePointsList,
        params: {
            deviceTypes: ['bridgeJkSl'],
        },
    },
    bridge_video: {
        api: postUsmVideoStreamPointList,
        params: {},
    },
    com_warning_level1: {
        api: postComUsmWarningList,
        params: {
            warningLevel: 7002201, // 一级预警
        },
    },
    com_warning_level2: {
        api: postComUsmWarningList,
        params: {
            warningLevel: 7002202, // 二级预警
        },
    },
    com_warning_level3: {
        api: postComUsmWarningList,
        params: {
            warningLevel: 7002203, // 三级预警
        },
    },
    com_gas_event: {
        api: postComUsmEmergencyEventList,
        params: {
            relatedBusiness: 7000501, // 燃气管道应急事件
        },
    },
    com_drainage_event: {
        api: postComUsmEmergencyEventList,
        params: {
            relatedBusiness: 7000502, // 排水管道应急事件
        },
    },
    com_heating_event: {
        api: postComUsmEmergencyEventList,
        params: {
            relatedBusiness: 7000503, // 供热管道应急事件
        },
    },
    com_bridge_event: {
        api: postComUsmEmergencyEventList,
        params: {
            relatedBusiness: 7000504, // 桥梁应急事件
        },
    },
    com_shelter: {
        api: postComUsmEmergencyShelterList,
        params: {},
    },
    com_emergency_team: {
        api: postComUsmEmergencyTeamList,
        params: {},
    },
    com_emergency_material: {
        api: postComUsmEmergencySuppliesList,
        params: {},
    },
    com_rescue_personnel: {
        api: postComUsmEmergencyRespondersList,
        params: {},
    },
    com_medical_institution: {
        api: postComUsmEmergencyHospitalList,
        params: {},
    },
    com_emergency_warehouse: {
        api: postComUsmEmergencyStoreList,
        params: {},
    },
    com_gas_hidden: {
        api: postComUsmRiskHiddenDangerList,
        params: {
            relatedBusinessType: 7000501, // 燃气管道隐患
        },
    },
    com_drainage_hidden: {
        api: postComUsmRiskHiddenDangerList,
        params: {
            relatedBusinessType: 7000502, // 排水管道隐患
        },
    },
    com_heating_hidden: {
        api: postComUsmRiskHiddenDangerList,
        params: {
            relatedBusinessType: 7000503, // 供热管道隐患
        },
    },
    com_bridge_hidden: {
        api: postComUsmRiskHiddenDangerList,
        params: {
            relatedBusinessType: 7000504, // 桥梁隐患
        },
    }
};

export const basicPipelineMap = {
    gas_pipeline: ['high_pressure_gas_pipeline', 'mid_pressure_gas_pipeline', 'low_pressure_gas_pipeline'],
    gas_pipeline_risk: ['gas_pipeline_risk1', 'gas_pipeline_risk2', 'gas_pipeline_risk3', 'gas_pipeline_risk4'],
    drainage_pipeline: ['drainage_rain_pipeline', 'drainage_sewage_pipeline', 'drainage_rainAndSewage_pipeline'],
    drainage_pipeline_risk: ['drainage_pipeline_risk1', 'drainage_pipeline_risk2', 'drainage_pipeline_risk3', 'drainage_pipeline_risk4'],
    heating_pipeline: ['heating_pipeline_1', 'heating_pipeline_2'],
    heating_pipeline_risk: ['heating_pipeline_risk1', 'heating_pipeline_risk2', 'heating_pipeline_risk3', 'heating_pipeline_risk4'],
}

export const AllPipelineMap = [
    ...basicPipelineMap.gas_pipeline,
    ...basicPipelineMap.gas_pipeline_risk,
    ...basicPipelineMap.drainage_pipeline,
    ...basicPipelineMap.drainage_pipeline_risk,
    ...basicPipelineMap.heating_pipeline,
    ...basicPipelineMap.heating_pipeline_risk,
]

export const ALLRiskPipelineMap = [
    ...basicPipelineMap.gas_pipeline_risk,
    ...basicPipelineMap.drainage_pipeline_risk,
    ...basicPipelineMap.heating_pipeline_risk,
]

export const PipelineColorMap = {
    // 燃气管线
    high_pressure_gas_pipeline: "#FF00FF",
    mid_pressure_gas_pipeline: "#FF00FF",
    low_pressure_gas_pipeline: "#FF00FF",
    // 燃气管线风险
    gas_pipeline_risk1: "#D9001B",
    gas_pipeline_risk2: "#F59A23",
    gas_pipeline_risk3: "#FFFF00",
    gas_pipeline_risk4: "#0972F4",
    // 排水管线
    drainage_rain_pipeline: "#906C4A",
    drainage_sewage_pipeline: "#906C4A",
    drainage_rainAndSewage_pipeline: "#906C4A",
    // 排水管线风险
    drainage_pipeline_risk1: "#D9001B",
    drainage_pipeline_risk2: "#F59A23",
    drainage_pipeline_risk3: "#FFFF00",
    drainage_pipeline_risk4: "#0972F4",
    // 供热管线
    heating_pipeline_1: "#820AFF",
    heating_pipeline_2: "#820AFF",
    // 供热管线风险
    heating_pipeline_risk1: "#D9001B",
    heating_pipeline_risk2: "#F59A23",
    heating_pipeline_risk3: "#FFFF00",
    heating_pipeline_risk4: "#0972F4",
}

export const offsetPopupList = [
    'gas_flowmeter', // 流量计
    'gas_manometer', // 压力计
    'gas_combustible', // 可燃气体探测器
    'gas_temperature', // 温度计
    'gas_manhole_cover', // 井盖传感器
    'drainage_level', // 水位计
    'drainage_flowmeter', // 水流计
    'drainage_rain', // 雨量计
    'drainage_water_quality', // 水质计
    'drainage_combustible', // 可燃气体探测器
    'drainage_manhole_cover', // 井盖传感器
    'heating_level', // 水位计
    'heating_flowmeter', // 水流计
    'heating_rain', // 雨量计
    'heating_water_quality', // 水质计
    'heating_combustible', // 可燃气体探测器
    'heating_temperature', // 温度计
    'heating_manhole_cover', // 井盖传感器
    'bridge_wind_speed', // 风速计
    'bridge_static_level', // 静态水准仪
    'bridge_temperature', // 温度计
    'bridge_humidity', // 湿度计
    'bridge_vibration', // 振动传感器
    'bridge_dynamic_weight', // 动静称重计
    'bridge_displacement', // 位移计
    'bridge_tilt', // 倾角计
    'bridge_deflection', // 挠度计
    'bridge_strain', // 应变计
    'bridge_load_cell', // 负载传感器
    'bridge_acceleration', // 加速度计
    'bridge_crack', // 车船撞击传感器
    'bridge_tilt_alarm', // 倾角报警
    'bridge_crack_sensor', // 裂缝传感器
    'bridge_vibration_sensor', // 振动传感器
    'bridge_cable_tension', // 索力传感器
]

export const associationLayerMap = {
    gas_alarm: [
        'gas_flowmeter',
        'gas_manometer',
        'gas_combustible',
        'gas_temperature',
        'gas_manhole_cover',
    ],
    drainage_alarm: [
        'drainage_level',
        'drainage_flowmeter',
        // 'drainage_rain',
        'drainage_water_quality',
        'drainage_combustible',
        'drainage_manhole_cover',
    ],
    heating_alarm: [
        // 'heating_level',
        'heating_flowmeter',
        // 'heating_rain',
        // 'heating_water_quality',
        'heating_combustible',
        'heating_temperature',
        'heating_manhole_cover',
    ],
    bridge_alarm: [
        'bridge_wind_speed', // 风速计
        'bridge_static_level', // 静态水准仪
        'bridge_temperature', // 温度计
        'bridge_humidity', // 湿度计
        'bridge_vibration', // 振动传感器
        'bridge_dynamic_weight', // 动静称重计
        'bridge_displacement', // 位移计
        'bridge_tilt', // 倾角计
        'bridge_deflection', // 挠度计
        'bridge_strain', // 应变计
        'bridge_load_cell', // 负载传感器
        'bridge_acceleration', // 加速度计
        'bridge_crack', // 车船撞击传感器
        'bridge_tilt_alarm', // 倾角报警
        'bridge_crack_sensor', // 裂缝传感器
        'bridge_vibration_sensor', // 振动传感器
        'bridge_cable_tension', // 索力传感器
    ],
}

export const videoMapAll = [
    'gas_video',
    'drainage_video',
    'heating_video',
    'bridge_video',
]

export const videoMapMisAll = [
    'gas_video0',
    'gas_video1',
]

export const devicesAlarmAll = [
    ...associationLayerMap.gas_alarm,
    ...associationLayerMap.drainage_alarm,
    ...associationLayerMap.heating_alarm,
    ...associationLayerMap.bridge_alarm,
]

export const alarmLayerMap = {
    gas_alarm: ['gas_alarm1', 'gas_alarm2', 'gas_alarm3'],
    drainage_alarm: ['drainage_alarm1', 'drainage_alarm2', 'drainage_alarm3'],
    heating_alarm: ['heating_alarm1', 'heating_alarm2', 'heating_alarm3'],
    bridge_alarm: ['bridge_alarm1', 'bridge_alarm2', 'bridge_alarm3'],
}

export const alarmStatusMap = {
    gas_alarm1: '1',
    gas_alarm2: '2',
    gas_alarm3: '3',
    drainage_alarm1: '1',
    drainage_alarm2: '2',
    drainage_alarm3: '3',
    heating_alarm1: '1',
    heating_alarm2: '2',
    heating_alarm3: '3',
    bridge_alarm1: '1',
    bridge_alarm2: '2',
    bridge_alarm3: '3',
}

export const pipeModelData = {
    high_pressure_gas_pipeline: ['dongMingTRContainer', 'dongMingTRWell', 'dongMingTRJoint'],
    mid_pressure_gas_pipeline: ['dongMingTRContainer', 'dongMingTRWell', 'dongMingTRJoint'],
    low_pressure_gas_pipeline: ['dongMingTRContainer', 'dongMingTRWell', 'dongMingTRJoint'],
    drainage_rain_pipeline: ['dongMingYSContainer', 'dongMingYSWell', 'dongMingYSJoint'],
    drainage_sewage_pipeline: ['dongMingWSContainer', 'dongMingWSWell', 'dongMingWSJoint'],
    drainage_rainAndSewage_pipeline: [
        'dongMingWSContainer', 'dongMingWSWell', 'dongMingWSJoint',
        'dongMingYSContainer', 'dongMingYSWell', 'dongMingYSJoint'
    ],
    heating_pipeline_1: ['dongMingRSContainer', 'dongMingRSWell', 'dongMingRSJoint'],
    heating_pipeline_2: ['dongMingRSContainer', 'dongMingRSWell', 'dongMingRSJoint'],
}
export const bridgeModelData = {
    bridge_info: [
        'dongMingBridgeModel1', 'dongMingBridgeModel2', 'dongMingBridgeModel3',
        'dongMingBridgeModel4', 'dongMingBridgeModel5', 'dongMingBridgeModel6',
        'dongMingBridgeModel7', 'dongMingBridgeModel8'
    ]
}

export const allSpecialModelData = [
    ...pipeModelData.high_pressure_gas_pipeline,
    ...pipeModelData.mid_pressure_gas_pipeline,
    ...pipeModelData.low_pressure_gas_pipeline,
    ...pipeModelData.drainage_rain_pipeline,
    ...pipeModelData.drainage_sewage_pipeline,
    ...pipeModelData.drainage_rainAndSewage_pipeline,
    ...pipeModelData.heating_pipeline_1,
    ...pipeModelData.heating_pipeline_2,
    ...bridgeModelData.bridge_info
]