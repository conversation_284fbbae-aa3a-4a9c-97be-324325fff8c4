
<template>
  <div class="equipment-monitor-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="stat-card">
        <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
        <div class="stat-label">设备总数</div>
      </div>
      <div class="stat-card online">
        <div class="stat-number">{{ statistics.onlineCount || 0 }}</div>
        <div class="stat-label">在线设备</div>
      </div>
      <div class="stat-card offline">
        <div class="stat-number">{{ statistics.offlineCount || 0 }}</div>
        <div class="stat-label">离线设备</div>
      </div>
      <div class="stat-card rate">
        <div class="stat-number">{{ statistics.onlineRate || '0.00%' }}</div>
        <div class="stat-label">设备在线率</div>
      </div>
      <div class="stat-card report">
        <div class="stat-number">{{ statistics.reportCount || 0 }}</div>
        <div class="stat-label">监测数据上报总数</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="equipment-monitor-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属专项:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="全部" @change="handleRelatedBusinessChange">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">设备类型:</span>
          <el-select v-model="formData.deviceType" class="form-input" placeholder="全部" :loading="deviceTypeLoading">
            <el-option label="全部" value="" />
            <el-option v-for="item in deviceTypeOptions" :key="item.deviceType" :label="item.deviceTypeName" :value="item.deviceType" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">设备状态:</span>
          <el-select v-model="formData.onlineStatus" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in onlineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.deviceName" class="form-input" placeholder="输入设备名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="设备名称" min-width="200" />
      <el-table-column prop="deviceTypeName" label="设备类型" min-width="120" />
      <el-table-column prop="relatedBusinessName" label="所属专项" min-width="100" />
      <el-table-column label="设备状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="row.onlineStatus === 1 ? 'success' : 'danger'" size="small">
            {{ row.onlineStatus === 1 ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="ownershipUnitName" label="权属单位" min-width="150" />
      <el-table-column prop="address" label="位置" min-width="200" show-overflow-tooltip />
      <el-table-column prop="time" label="数据采集时间" min-width="160" />
      <el-table-column label="操作" fixed="right" min-width="120" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleViewData(row)">查看数据</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 设备监控弹窗 -->
    <MonitorDeviceDialog
      v-model:visible="dialogVisible"
      :device-data="selectedDevice"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElTag } from 'element-plus'
import { getMonitorDevicePage } from '@/api/comprehensive'
import { RELATED_BUSINESS_OPTIONS, DEVICE_ONLINE_STATUS_OPTIONS } from '@/constants/comprehensive'
import { getDeviceType as getGasDeviceType } from '@/api/gas'
import { getDeviceType as getDrainageDeviceType } from '@/api/drainage'
import { getDeviceType as getHeatingDeviceType } from '@/api/heating'
import { getDeviceType as getBridgeDeviceType } from '@/api/bridge'
import MonitorDeviceDialog from './components/MonitorDeviceDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 统计数据
const statistics = ref({
  totalCount: 0,
  onlineCount: 0,
  offlineCount: 0,
  onlineRate: '0.00%',
  reportCount: 0
})

// 下拉选项数据
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS
const onlineStatusOptions = DEVICE_ONLINE_STATUS_OPTIONS
const deviceTypeOptions = ref([])
const deviceTypeLoading = ref(false)

// 设备类型缓存
const deviceTypeCache = ref({
  gas: [],
  drainage: [],
  heating: [],
  bridge: []
})

// 表单数据
const formData = ref({
  relatedBusiness: '',
  deviceType: '',
  onlineStatus: '',
  deviceName: ''
})

// 弹窗相关
const dialogVisible = ref(false)
const selectedDevice = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchMonitorDeviceData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    relatedBusiness: '',
    deviceType: '',
    onlineStatus: '',
    deviceName: ''
  }
  currentPage.value = 1
  // 重置设备类型选项为全部专项的合并结果
  updateDeviceTypeOptions('')
  fetchMonitorDeviceData()
}

// 获取各专项设备类型数据
const fetchDeviceTypes = async () => {
  try {
    // 并行获取所有专项的设备类型
    const [gasRes, drainageRes, heatingRes, bridgeRes] = await Promise.allSettled([
      getGasDeviceType(),
      getDrainageDeviceType(),
      getHeatingDeviceType(),
      getBridgeDeviceType()
    ])

    // 处理燃气专项设备类型
    if (gasRes.status === 'fulfilled' && gasRes.value?.code === 200) {
      deviceTypeCache.value.gas = gasRes.value.data || []
    }

    // 处理排水专项设备类型
    if (drainageRes.status === 'fulfilled' && drainageRes.value?.code === 200) {
      deviceTypeCache.value.drainage = drainageRes.value.data || []
    }

    // 处理供热专项设备类型
    if (heatingRes.status === 'fulfilled' && heatingRes.value?.code === 200) {
      deviceTypeCache.value.heating = heatingRes.value.data || []
    }

    // 处理桥梁专项设备类型
    if (bridgeRes.status === 'fulfilled' && bridgeRes.value?.code === 200) {
      deviceTypeCache.value.bridge = bridgeRes.value.data || []
    }

    // 初始化设备类型选项（全部专项的合并结果）
    updateDeviceTypeOptions('')
  } catch (error) {
    console.error('获取设备类型失败:', error)
  }
}

// 根据所属专项更新设备类型选项
const updateDeviceTypeOptions = (relatedBusiness) => {
  if (!relatedBusiness) {
    // 全部专项：合并所有专项的设备类型
    const allDeviceTypes = [
      ...deviceTypeCache.value.gas,
      ...deviceTypeCache.value.drainage,
      ...deviceTypeCache.value.heating,
      ...deviceTypeCache.value.bridge
    ]

    // 去重处理（基于deviceType字段）
    const uniqueDeviceTypes = allDeviceTypes.reduce((acc, current) => {
      const existing = acc.find(item => item.deviceType === current.deviceType)
      if (!existing) {
        acc.push(current)
      }
      return acc
    }, [])

    deviceTypeOptions.value = uniqueDeviceTypes
  } else {
    // 根据专项值获取对应的设备类型
    const businessMap = {
      7000501: 'gas',      // 燃气
      7000502: 'drainage', // 排水
      7000503: 'heating',  // 供热
      7000504: 'bridge'    // 桥梁
    }

    const businessKey = businessMap[relatedBusiness]
    deviceTypeOptions.value = businessKey ? deviceTypeCache.value[businessKey] : []
  }
}

// 处理所属专项变化
const handleRelatedBusinessChange = (value) => {
  // 重置设备类型选择
  formData.value.deviceType = ''
  // 更新设备类型选项
  updateDeviceTypeOptions(value)
}

// 获取监测设备分页数据
const fetchMonitorDeviceData = async () => {
  loading.value = true;
  try {
    const params = {
      relatedBusiness: formData.value.relatedBusiness,
      deviceType: formData.value.deviceType,
      onlineStatus: formData.value.onlineStatus,
      deviceName: formData.value.deviceName
    }

    const res = await getMonitorDevicePage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      // 更新统计数据
      statistics.value = {
        totalCount: res.data.totalCount || 0,
        onlineCount: res.data.onlineCount || 0,
        offlineCount: res.data.offlineCount || 0,
        onlineRate: res.data.onlineRate || '0.00%',
        reportCount: res.data.reportCount || 0
      }

      // 更新表格数据
      const devicePage = res.data.usmMonitorDevicePage || {}
      tableData.value = devicePage.records || []
      total.value = devicePage.total || 0
    }
  } catch (error) {
    console.error('获取监测设备数据失败:', error)
    ElMessage.error('获取监测设备数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchMonitorDeviceData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchMonitorDeviceData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理查看数据
const handleViewData = (row) => {
  selectedDevice.value = row
  dialogVisible.value = true
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.equipment-monitor-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const statisticsSection = container.querySelector('.statistics-section');
    const statisticsHeight = statisticsSection ? statisticsSection.offsetHeight : 0;
    const searchSection = container.querySelector('.equipment-monitor-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - statisticsHeight - searchHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 450;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(() => {
  // 先获取设备类型数据，再获取监测设备数据
  fetchDeviceTypes().then(() => {
    fetchMonitorDeviceData()
  })

  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.equipment-monitor-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 180px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-card.online {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.offline {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card.rate {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.stat-card.report {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.equipment-monitor-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-section {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-item {
    width: 100%;
    margin-right: 0;
  }
  
  .form-input {
    width: 100%;
  }
}
</style>
