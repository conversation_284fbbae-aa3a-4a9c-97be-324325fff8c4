<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="risk-assessment-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险区域名称" prop="divisionId">
            <el-select v-model="formData.divisionId" placeholder="请选择" class="w-full" @change="handleRegionChange">
              <el-option v-for="item in regionOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="responsibleUser">
            <el-input v-model="formData.responsibleUser" placeholder="请输入负责人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风险等级" prop="riskLevel">
            <el-select v-model="formData.riskLevel" placeholder="请选择" class="w-full" @change="handleRiskLevelChange">
              <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="风险描述" prop="riskDesc">
            <el-input v-model="formData.riskDesc" type="textarea" :rows="3" placeholder="请输入风险描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveRiskRegionSign,
  updateRiskRegionSign,
  getRiskRegionDivisionList
} from '@/api/comprehensive';
import { RISK_LEVEL_OPTIONS } from '@/constants/comprehensive';

// 使用从常量文件导入的选项
const riskLevelOptions = RISK_LEVEL_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增风险评估标识',
    edit: '编辑风险评估标识',
    view: '风险评估标识详情'
  };
  return titles[props.mode] || '风险评估标识';
});

// 下拉选项数据
const regionOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  divisionId: '',
  regionName: '',
  responsibleUser: '',
  contactInfo: '',
  riskLevel: '',
  riskLevelName: '',
  riskDesc: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  divisionId: [{ required: true, message: '请选择风险区域名称', trigger: 'change' }],
  responsibleUser: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  contactInfo: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
  riskDesc: [{ required: true, message: '请输入风险描述', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理风险区域变化
const handleRegionChange = (value) => {
  const selected = regionOptions.value.find(item => item.value === value);
  if (selected) {
    formData.regionName = selected.label;
  }
};

// 处理风险等级变化
const handleRiskLevelChange = (value) => {
  const selected = riskLevelOptions.find(item => item.value === value);
  if (selected) {
    formData.riskLevelName = selected.label;
  }
};

// 获取风险区域列表
const fetchRegionList = async () => {
  try {
    const res = await getRiskRegionDivisionList({});
    if (res && res.data) {
      regionOptions.value = res.data.map(item => ({
        label: item.regionName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取风险区域列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 确保名称字段已设置
    const selectedRegion = regionOptions.value.find(item => item.value === formData.divisionId);
    if (selectedRegion) {
      formData.regionName = selectedRegion.label;
    }
    
    const selectedRisk = riskLevelOptions.find(item => item.value === formData.riskLevel);
    if (selectedRisk) {
      formData.riskLevelName = selectedRisk.label;
    }

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveRiskRegionSign(submitData);
    } else if (props.mode === 'edit') {
      res = await updateRiskRegionSign(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchRegionList();
});
</script>

<style scoped>
.risk-assessment-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 