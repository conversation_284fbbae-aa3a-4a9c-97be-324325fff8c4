<template>
  <PanelBox title="隐患整改分析" class="right-bottom-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-color" style="background: #1B50FF;"></span>
          <span class="legend-text">隐患总数</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: #FFD512;"></span>
          <span class="legend-text">未整改</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: #41C4A3;"></span>
          <span class="legend-text">已整改</span>
        </div>
      </div>
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'
import { getHiddenDangerRectifyAnalysis } from '@/api/drainage.js'
import { HIDDEN_DANGER_LEVEL_MAP } from '@/constants/drainage.js'

// 时间选择
const timeRange = ref(7)
const timeOptions = [
  { label: '近一周', value: 7 },
  { label: '近一月', value: 30 },
  { label: '近一年', value: 365 }
]

// 加载状态和数据
const loading = ref(false)
const rectifyData = ref([])

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 当前数据
const currentData = ref({
  categories: ['重大隐患', '较大隐患', '一般隐患'],
  series: [
    { name: '隐患总数', data: [0, 0, 0] },
    { name: '未整改', data: [0, 0, 0] },
    { name: '已整改', data: [0, 0, 0] }
  ]
})

// 获取隐患整改分析数据
const fetchRectifyData = async () => {
  try {
    loading.value = true
    const response = await getHiddenDangerRectifyAnalysis(timeRange.value)

    if (response.code === 200) {
      rectifyData.value = response.data || []

      // 处理数据，确保包含所有隐患等级
      const processedData = processRectifyData(rectifyData.value)
      currentData.value = processedData
    } else {
      console.error('获取隐患整改分析数据失败:', response.message)
      resetData()
    }
  } catch (error) {
    console.error('获取隐患整改分析数据异常:', error)
    resetData()
  } finally {
    loading.value = false
  }
}

// 处理API返回的数据
const processRectifyData = (data) => {
  const categories = ['重大隐患', '较大隐患', '一般隐患']
  const levelKeys = [3002701, 3002702, 3002703] // 对应重大、较大、一般隐患

  const totalData = [0, 0, 0]
  const unRectifyData = [0, 0, 0]
  const rectifiedData = [0, 0, 0]

  // 创建数据映射
  const dataMap = {}
  data.forEach(item => {
    dataMap[item.dangerLevel] = item
  })

  // 按照固定顺序填充数据
  levelKeys.forEach((levelKey, index) => {
    const item = dataMap[levelKey]
    if (item) {
      totalData[index] = item.totalCount || 0
      unRectifyData[index] = item.unRectifyCount || 0
      rectifiedData[index] = item.rectifiedCount || 0
    }
  })

  return {
    categories,
    series: [
      { name: '隐患总数', data: totalData },
      { name: '未整改', data: unRectifyData },
      { name: '已整改', data: rectifiedData }
    ]
  }
}

// 重置数据
const resetData = () => {
  rectifyData.value = []
  currentData.value = {
    categories: ['重大隐患', '较大隐患', '一般隐患'],
    series: [
      { name: '隐患总数', data: [0, 0, 0] },
      { name: '未整改', data: [0, 0, 0] },
      { name: '已整改', data: [0, 0, 0] }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 确保容器已渲染完成并且有尺寸
  setTimeout(() => {
    chartInstance = echarts.init(chartRef.value)
    updateChartData()

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', resizeChart)
  }, 100)
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance) return

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
      formatter: function (params) {
        const time = {
          7: '近一周',
          30: '近一月',
          365: '近一年'
        }[timeRange.value]

        const category = params[0].name
        let html = `<div style="color: #fff;">${time}${category}</div>`

        params.forEach(item => {
          const color = {
            '隐患总数': '#1B50FF',
            '未整改': '#FFD512',
            '已整改': '#41C4A3'
          }[item.seriesName]

          html += `<div style="display: flex; align-items: center; margin-top: 5px;">
                    <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; margin-right: 5px;"></span>
                    <span>${item.seriesName}: ${item.value}个</span>
                  </div>`
        })

        return html
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '35%',
      top: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentData.value.categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      }
    },
    yAxis: {
      type: 'value',
      name: '隐患数（个）',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400,
        padding: [0, 0, 0, 0]
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      }
    },
    series: [
      {
        name: '隐患总数',
        type: 'pictorialBar',
        barWidth: '30%',
        barCategoryGap: '-100%',
        symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
        z: 1,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(27, 80, 255, 0.9)' },
            { offset: 1, color: 'rgba(27, 80, 255, 0.1)' }
          ])
        },
        data: currentData.value.series[0].data
      },
      {
        name: '未整改',
        type: 'pictorialBar',
        symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
        barWidth: '20%',
        barCategoryGap: '-100%',
        z: 2,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 213, 18, 0.9)' },
            { offset: 1, color: 'rgba(255, 213, 18, 0.1)' }
          ])
        },
        data: currentData.value.series[1].data
      },
      {
        name: '已整改',
        type: 'pictorialBar',
        symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
        barWidth: '10%',
        barCategoryGap: '-100%',
        z: 3,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(65, 196, 163, 0.9)' },
            { offset: 1, color: 'rgba(65, 196, 163, 0.1)' }
          ])
        },
        data: currentData.value.series[2].data
      }
    ]
  }

  chartInstance.setOption(option)
}

// 响应窗口大小变化
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  timeRange.value = value
  fetchRectifyData()
}

// 监听时间范围变化
watch(timeRange, () => {
  fetchRectifyData()
})

// 监听数据变化，更新图表
watch(currentData, () => {
  updateChartData()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  // 使用setTimeout确保DOM已经渲染完毕
  setTimeout(() => {
    initChart()
    fetchRectifyData()
  }, 300)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.right-bottom-panel {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.chart-legend {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-container {
  width: 100%;
  flex: 1;
  min-height: 250px;
}

.com-select {
  margin-right: 20px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }

  .chart-container {
    height: 340px;
  }
}

@media screen and (max-width: 1919px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 8px;
  }

  .chart-container {
    height: 300px;
  }
}

@media screen and (min-width: 2561px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 15px;
  }

  .chart-container {
    height: 380px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 15px;
  }

  .chart-container {
    height: 360px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }

  .chart-container {
    height: 320px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .right-bottom-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
  }

  .chart-container {
    height: 280px;
  }
}
</style>