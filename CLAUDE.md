# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Build for development environment
npm run build:dev

# Preview production build
npm run preview
```

### Architecture Overview

This is a Vue 3 city lifeline safety monitoring platform with dual interfaces:

**Screen Interface** (`/screen/*`): Large-screen data visualization dashboard with immersive map-overlay design
**Admin Interface** (`/admin/*`): Traditional management interface with deep nested routing

### Key Technologies
- **Vue 3** with Composition API and `<script setup>`
- **Element Plus** for UI components
- **Pinia** for state management
- **Cesium** for 3D GIS mapping
- **ECharts** for data visualization
- **Vite** for build tooling

### Domain Structure
The application is organized around city lifeline domains:
- **Comprehensive** (综合专项): Cross-domain coordination
- **Gas** (燃气专项): Gas pipeline monitoring and safety
- **Drainage** (排水专项): Drainage system and flood control  
- **Heating** (供热专项): District heating management
- **Bridge** (桥梁专项): Bridge structural health monitoring

### Component Architecture

**Screen Components** (`src/components/screen/`):
- `panels/`: Domain-specific panel components for each lifeline domain
- `charts/`: Reusable chart components
- `common/`: Shared components (ScrollTable, VideoPlayer)
- `ChartBox.vue`: ECharts wrapper with auto-resize
- `PanelBox.vue`: Standard panel container with consistent styling
- `StatCard.vue`: Statistics display cards

**GIS Components** (`src/components/GisMap/`):
- `InitMap.vue`: Cesium 3D map initialization
- `components/`: Map-specific UI components
- `popup/`: Interactive map popup components
- `Tools/`: Map interaction and analysis tools

### State Management (Pinia)
- `user.js`: Authentication, user info, encrypted password handling
- `bridge.js`: Bridge-specific monitoring state
- Uses Composition API pattern with reactive state

### API Organization
Modular API structure in `src/api/`:
- `gas.js`, `drainage.js`, `heating.js`, `bridge.js`: Domain-specific APIs
- `comprehensive.js`: Cross-domain APIs
- `user.js`: Authentication APIs
- Unified request interceptor with automatic token injection

### Routing System
- **Dynamic routing**: `/:primaryTab/:secondaryTab` pattern
- **Admin routes**: 4-level deep nested structure
- **Authentication guards**: Token-based protection
- Bridge domain uses custom routing without secondary navigation

### Screen Adaptation
Advanced responsive design supporting multiple display ratios:
- **16:9 screens**: 380px side panels
- **21:9 ultra-wide**: 420px side panels  
- **32:9 dual-wide**: 450px side panels
- Uses CSS Grid with `minmax(0, 1fr)` for adaptive center content

### Table Component Optimization Patterns
When optimizing table components, follow this established pattern:

1. **Container Structure**:
   ```vue
   <!-- Main container with flex layout -->
   <div class="component-name">
     <!-- Search section with flex-shrink: 0 -->
     <div class="search-section">
       <SearchComponent />
     </div>
     
     <!-- Table container with flex: 1 -->
     <div class="table-container" ref="tableContainerRef">
       <el-table :height="tableHeight" />
     </div>
     
     <!-- Pagination with flex-shrink: 0 -->
     <div class="pagination-container">
       <el-pagination />
     </div>
   </div>
   ```

2. **CSS Pattern**:
   ```css
   .component-name {
     width: 100%;
     height: 100%;
     display: flex;
     flex-direction: column;
     padding: 8px; /* Reduced from 16px for more space */
     box-sizing: border-box;
     overflow: hidden;
   }
   
   .table-container {
     flex: 1;
     min-height: 0;
     overflow: hidden;
     display: flex;
     flex-direction: column;
   }
   
   :deep(.el-table) {
     height: 100% !important;
     display: flex;
     flex-direction: column;
   }
   
   :deep(.el-table__header-wrapper) {
     flex-shrink: 0;
     overflow-x: hidden !important; /* Hide header scrollbar */
   }
   
   :deep(.el-table__body-wrapper) {
     flex: 1;
     overflow: auto !important;
   }
   ```

3. **JavaScript Pattern**:
   ```javascript
   // Table height calculation
   const tableContainerRef = ref(null);
   const tableHeight = ref(400);
   
   const calculateTableHeight = () => {
     nextTick(() => {
       if (tableContainerRef.value) {
         const containerHeight = tableContainerRef.value.offsetHeight;
         if (containerHeight > 0) {
           tableHeight.value = Math.max(containerHeight, 500);
         } else {
           tableHeight.value = 600;
           setTimeout(calculateTableHeight, 100);
         }
       }
     });
   };
   
   // Scroll synchronization for horizontal scrolling
   const setupScrollSync = () => {
     // Implementation for syncing header and body scroll
   };
   ```

### Environment Configuration
Uses environment-specific configuration:
- `.env.development`: Development API endpoints
- `.env.production`: Production settings
- Proxy configuration in `vite.config.js` for API forwarding

### Authentication System
- **RSA encryption**: Client-side password encryption with JSEncrypt
- **JWT tokens**: Stored in localStorage with automatic injection
- **Session management**: Token expiration and refresh handling

### GIS Integration
- **Cesium 3D mapping**: Custom initialization with performance optimizations
- **Layered design**: Map background + UI overlay with CSS pointer-events
- **Real-time positioning**: Device location and status visualization
- **Custom styling**: Entities with dynamic animations and popups

### Code Generation
Template-based code generator using Handlebars:
- Located in `src/views/admin/system/tools/`
- Generates complete CRUD page sets
- Visual configuration interface
- Multi-file download capability

### Build Configuration
- **Development**: Hot reload with proxy
- **Production**: Terser minification, console removal
- **Asset handling**: Cesium resources and WASM files
- **Chunk optimization**: 2MB warning limit

### Performance Considerations
- Component lazy loading for route-based code splitting
- ECharts auto-resize with requestAnimationFrame
- Memory cleanup in component unmount hooks
- CSS variables for consistent theming
- Optimized table rendering with virtual scrolling patterns