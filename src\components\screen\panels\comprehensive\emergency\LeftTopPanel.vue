<template>
  <PanelBox title="应急事件">
    <div class="panel-content">
      <!-- 事件统计区域 -->
      <div class="event-stats-container">
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-value blue-gradient">{{ statistics.totalCount }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner blue"></span></span>
              <span class="stat-label">事件总数</span> 
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-value green-gradient">{{ statistics.handled }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
              <span class="stat-label">已处置</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-value yellow-gradient">{{ statistics.handling }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner yellow"></span></span>
              <span class="stat-label">处置中</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-value orange-gradient">{{ statistics.pendingHandle }}</div>
            <div class="stat-info">
              <span class="stat-dot"><span class="stat-dot-inner orange"></span></span>
              <span class="stat-label">未处置</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件列表区域 -->
      <div class="event-list">
        <ScrollTable 
          :columns="tableColumns" 
          :data="eventList" 
          :autoScroll="true" 
          :scrollSpeed="3000"
          :tableHeight="tableHeight" 
          :visibleRows="5"
        >
          <!-- 自定义序号列
          <template #id="{ row }">
            <div class="event-number">{{ row.id }}</div>
          </template> -->
          
          <!-- 专项标签列的自定义渲染 -->
          <template #tag="{ row }">
            <div :class="['event-tag', getTagClass(row.tagType)]">{{ row.tag }}</div>
          </template>
          
          <!-- 状态列的自定义渲染 -->
          <template #status="{ row }">
            <span :class="getStatusClass(row.status)">{{ row.status }}</span>
          </template>
        </ScrollTable>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import { getEmergencyEventStatistics, getEmergencyEventList } from '@/api/comprehensive'

// 表格列配置
const tableColumns = [
  { title: '序号', dataIndex: 'id', width: '5%', slot: 'id' },
  { title: '  ', dataIndex: 'tag', width: '12%', slot: 'tag' },
  { title: '事件名称', dataIndex: 'name', width: '18%' },
  { title: '发生时间', dataIndex: 'time', width: '32%' },
  { title: '分类', dataIndex: 'type', width: '21%' },
  { title: '状态', dataIndex: 'status', width: '15%', slot: 'status' }
]

// 统计数据
const statistics = ref({
  totalCount: 0,
  pendingHandle: 0,
  handling: 0,
  handled: 0
})

// 事件列表数据
const eventList = ref([])

// 加载状态
const loading = ref(false)

// 表格高度
const tableHeight = computed(() => {
  // 根据不同分辨率动态调整表格高度
  if (window.innerHeight >= 900 && window.innerHeight <= 940) {
    return '200px' // 较小屏幕
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '220px' // 中等屏幕
  } else if (window.innerHeight >= 1056) {
    return '260px' // 较大屏幕
  } else {
    return '220px' // 默认高度
  }
})

// 根据状态获取对应的样式类
const getStatusClass = (status) => {
  switch (status) {
    case '未处理':
      return 'status-pending'
    case '已处置':
      return 'status-handled'
    case '处置中':
      return 'status-in-progress'
    default:
      return 'status-default'
  }
}

// 根据专项类型获取标签样式类
const getTagClass = (type) => {
  switch (type) {
    case 'gas':
      return 'tag-gas'
    case 'water':
      return 'tag-water'
    case 'heating':
      return 'tag-heating'
    case 'bridge':
      return 'tag-bridge'
    default:
      return 'tag-default'
  }
}

// 根据专项名称获取标签类型
const getTagTypeByName = (relatedBusinessName) => {
  switch (relatedBusinessName) {
    case '燃气':
      return 'gas'
    case '排水':
      return 'water'
    case '供热':
      return 'heating'
    case '桥梁':
      return 'bridge'
    default:
      return 'default'
  }
}

// 获取应急事件统计数据
const fetchStatistics = async () => {
  try {
    const response = await getEmergencyEventStatistics()
    if (response.code === 200 && response.data) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取应急事件统计数据失败:', error)
  }
}

// 获取应急事件列表数据
const fetchEventList = async () => {
  try {
    loading.value = true
    const response = await getEmergencyEventList({})
    if (response.code === 200 && response.data) {
      // 转换数据格式以适配表格组件
      eventList.value = response.data.map((item, index) => ({
        id: index + 1, // 序号
        name: item.eventTitle, // 事件名称
        tag: item.relatedBusinessName, // 专项标签
        tagType: getTagTypeByName(item.relatedBusinessName), // 标签类型
        time: item.eventTime, // 发生时间
        type: item.eventTypeName, // 分类
        status: item.eventStatusName // 状态
      }))
    }
  } catch (error) {
    console.error('获取应急事件列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化数据
const initData = async () => {
  await Promise.all([
    fetchStatistics(),
    fetchEventList()
  ])
}

// 组件挂载时获取数据
onMounted(() => {
  initData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 事件统计样式 */
.event-stats-container {
  margin: 5px 0 5px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.blue { 
  background: #3AA1FF;
}

.stat-dot-inner.orange { 
  background: #FF6D28;
}

.stat-dot-inner.yellow { 
  background: #FFC75A;
}

.stat-dot-inner.green { 
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.blue) {
  background: rgba(58, 161, 255, 0.4);
}

.stat-dot:has(.stat-dot-inner.orange) {
  background: rgba(255, 109, 40, 0.4);
}

.stat-dot:has(.stat-dot-inner.yellow) {
  background: rgba(255, 199, 90, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.blue-gradient {
  background: linear-gradient(90deg, #3AA1FF 0%, #A6D6FE 100%);
  -webkit-background-clip: text;
}

.orange-gradient {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
}

.yellow-gradient {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
}

.green-gradient {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
}

/* 事件列表样式 */
.event-list {
  flex: 1;
  overflow: hidden;
}

/* 事件标签样式 */
.event-tag {
  display: inline-block;
  width: 38px;
  height: 20px;
  line-height: 20px;
  border-radius: 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #DEEDFF;
  text-align: center;
}

.tag-gas {
  background: rgba(255, 175, 64, 0.3);
  border: 1px solid #FFAF40;
}

.tag-water {
  background: rgba(42, 185, 255, 0.3);
  border: 1px solid #2AB9FF;
}

.tag-heating {
  background: rgba(31, 203, 161, 0.3);
  border: 1px solid #1FCBA1;
}

.tag-bridge {
  background: rgba(138, 93, 255, 0.3);
  border: 1px solid #8A5DFF;
}

/* 序号样式 */
.event-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  background: #232253;
  color: #fff;
  font-size: 12px;
}

/* 状态样式 */
.status-pending {
  color: #FF6D28;
}

.status-handled {
  color: #3FD87C;
}

.status-in-progress {
  color: #FFC75A;
}

/* 响应式布局 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 5px;
  }
  
  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  :deep(.scroll-table td) {
    font-size: 12px;
    height: 24px;
    line-height: 24px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stat-value {
    font-size: 22px;
    line-height: 24px;
  }
}

@media screen and (min-height: 1056px) {
  .panel-content {
    padding: 15px;
    gap: 10px;
  }
  
  .stat-value {
    font-size: 26px;
    line-height: 28px;
  }
  
  .stat-label {
    font-size: 13px;
  }
  :deep(.scroll-table td) {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
  }
}
</style>