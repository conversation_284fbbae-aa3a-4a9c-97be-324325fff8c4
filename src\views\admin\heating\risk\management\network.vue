<template>
  <div class="heating-risk-network-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="stats-card red">
        <div class="stats-number">{{ statisticsData.majorCount || 0 }}km</div>
        <div class="stats-label">重大风险</div>
      </div>
      <div class="stats-card yellow">
        <div class="stats-number">{{ statisticsData.largeCount || 0 }}km</div>
        <div class="stats-label">较大风险</div>
      </div>
      <div class="stats-card orange">
        <div class="stats-number">{{ statisticsData.normalCount || 0 }}km</div>
        <div class="stats-label">一般风险</div>
      </div>
      <div class="stats-card blue">
        <div class="stats-number">{{ statisticsData.lowCount || 0 }}km</div>
        <div class="stats-label">低风险</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">管网类型:</span>
          <el-select v-model="formData.pipelineType" class="form-input" placeholder="全部">
            <el-option v-for="item in pipelineTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">风险等级:</span>
          <el-select v-model="formData.riskLevel" class="form-input" placeholder="全部">
            <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管控状态:</span>
          <el-select v-model="formData.pipelineStatus" class="form-input" placeholder="全部">
            <el-option v-for="item in controlStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.pipelineCode" class="form-input" placeholder="输入管线编码" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="warning" class="operation-btn" @click="handleExport">导出</el-button>
        <!-- <el-button type="primary" class="operation-btn" @click="handleAssessmentConfig">评估指标配置</el-button> -->
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="riskCode" label="风险编码" min-width="120" />
      <el-table-column prop="pipelineCode" label="管线编码" min-width="120" />
      <el-table-column label="管网类型" min-width="100">
        <template #default="{ row }">
          {{ getPipelineTypeName(row.pipelineType) }}
        </template>
      </el-table-column>
      <el-table-column label="管径(mm)" min-width="100">
        <template #default="{ row }">
          {{ row.diameter }}
        </template>
      </el-table-column>
      <el-table-column label="管材" min-width="100">
        <template #default="{ row }">
          {{ row.material }}
        </template>
      </el-table-column>
      <el-table-column label="建设时间" min-width="120">
        <template #default="{ row }">
          {{ row.constructionTime }}
        </template>
      </el-table-column>
      <el-table-column label="所在道路" min-width="120">
        <template #default="{ row }">
          {{ row.location }}
        </template>
      </el-table-column>
      <el-table-column label="风险等级" min-width="100">
        <template #default="{ row }">
          <el-tag
            :type="getRiskLevelType(row.riskLevel)"
            class="risk-tag"
          >
            {{ getRiskLevelName(row.riskLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="assessmentDate" label="评估日期" min-width="120">
        <template #default="{ row }">
          {{ formatDate(row.assessmentDate) }}
        </template>
      </el-table-column>
      <el-table-column label="管控状态" min-width="80">
        <template #default="{ row }">
          {{ getControlStatusName(row.pipelineStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="220">
        <template #default="{ row }">
          <div class="operation-btns">
            <!-- <el-button type="primary" link @click.stop="handleAssessmentRecord(row)">评估记录</el-button> -->
            <el-button type="primary" link @click.stop="handleEdit(row)">修改</el-button>
            <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 修改对话框 -->
    <NetworkAssessmentDialog
      v-model:visible="dialogVisible"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  getPipelineRiskAssessmentStatistics,
  getPipelineRiskAssessmentPage,
  getPipelineRiskAssessmentDetail
} from '@/api/heating';
import { 
  PIPELINE_TYPES,
  PIPELINE_RISK_LEVEL_OPTIONS,
  PIPELINE_CONTROL_STATUS_OPTIONS,
  PIPELINE_RISK_LEVEL_MAP,
  PIPELINE_CONTROL_STATUS_MAP,
  PIPELINE_TYPE_MAP
} from '@/constants/heating';
import { misPosition } from '@/hooks/gishooks';
import NetworkAssessmentDialog from './components/NetworkAssessmentDialog.vue';

// 供热管网风险信息组件

// 统计数据
const statisticsData = ref({
  majorCount: 0,
  largeCount: 0,
  normalCount: 0,
  lowCount: 0
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 下拉选项数据
const pipelineTypeOptions = PIPELINE_TYPES;
const riskLevelOptions = PIPELINE_RISK_LEVEL_OPTIONS;
const controlStatusOptions = PIPELINE_CONTROL_STATUS_OPTIONS;

// 表单数据
const formData = ref({
  pipelineType: '',
  riskLevel: '',
  pipelineStatus: '',
  pipelineCode: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchRiskAssessmentData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    pipelineType: '',
    riskLevel: '',
    pipelineStatus: '',
    pipelineCode: ''
  };
  currentPage.value = 1;
  fetchRiskAssessmentData();
};

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getPipelineRiskAssessmentStatistics();
    if (res && res.code === 200) {
      statisticsData.value = res.data || {
        majorCount: 0,
        largeCount: 0,
        normalCount: 0,
        lowCount: 0
      };
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
  }
};

// 获取风险评估分页数据
const fetchRiskAssessmentData = async () => {
  try {
    loading.value = true;
    const params = {
      pipelineType: formData.value.pipelineType,
      riskLevel: formData.value.riskLevel,
      pipelineStatus: formData.value.pipelineStatus,
      pipelineCode: formData.value.pipelineCode
    };
    
    const res = await getPipelineRiskAssessmentPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取风险评估数据失败:', error);
    ElMessage.error('获取风险评估数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchRiskAssessmentData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchRiskAssessmentData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理修改
const handleEdit = async (row) => {
  try {
    const res = await getPipelineRiskAssessmentDetail(row.id);
    if (res && res.code === 200) {
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取风险评估详情失败');
    }
  } catch (error) {
    console.error('获取风险评估详情失败:', error);
    ElMessage.error('获取风险评估详情失败');
  }
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理评估记录
const handleAssessmentRecord = (row) => {
  console.log('评估记录', row);
  ElMessage.info('评估记录功能待实现');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理评估指标配置
const handleAssessmentConfig = () => {
  console.log('评估指标配置');
  ElMessage.info('评估指标配置功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchRiskAssessmentData();
  fetchStatistics();
};

// 获取管线类型名称
const getPipelineTypeName = (type) => {
  return PIPELINE_TYPE_MAP[type] || '未知';
};

// 获取风险等级名称
const getRiskLevelName = (level) => {
  return PIPELINE_RISK_LEVEL_MAP[level] || '未知';
};

// 获取风险等级标签类型
const getRiskLevelType = (level) => {
  const typeMap = {
    2002801: 'danger',  // 重大风险
    2002802: 'warning', // 较大风险
    2002803: '',        // 一般风险
    2002804: 'success'  // 低风险
  };
  return typeMap[level] || '';
};

// 获取管控状态名称
const getControlStatusName = (status) => {
  return PIPELINE_CONTROL_STATUS_MAP[status] || '未管控';
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '2023年1月5日';
  return dateStr.split(' ')[0]; // 只显示日期部分
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.heating-risk-network-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 180;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatistics(),
      fetchRiskAssessmentData()
    ]);
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.heating-risk-network-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 数据统计区域样式 */
.statistics-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.stats-card {
  flex: 1;
  height: 80px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
}

.stats-card.red {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}

.stats-card.yellow {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
}

.stats-card.orange {
  background: linear-gradient(135deg, #fa8c16 0%, #ffb946 100%);
}

.stats-card.blue {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

.risk-tag {
  font-weight: bold;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 