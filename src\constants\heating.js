/**
 * 热源类型
 * @type {Array<{label: string, value: number}>}
 */
export const HEAT_TYPES = [
  { label: '燃煤热电厂', value: 2000101 },
  { label: '区域锅炉房', value: 2000102 },
  { label: '地热工业余热', value: 2000103 },
  { label: '垃圾焚烧厂', value: 2000104 },
  { label: '核能', value: 2000105 },
  { label: '太阳能', value: 2000106 },
  { label: '热泵', value: 2000107 },
  { label: '其他', value: 2000108 }
];

/**
 * 热源类型映射
 */
export const HEAT_TYPE_MAP = {
  2000101: '燃煤热电厂',
  2000102: '区域锅炉房',
  2000103: '地热工业余热',
  2000104: '垃圾焚烧厂',
  2000105: '核能',
  2000106: '太阳能',
  2000107: '热泵',
  2000108: '其他'
};

/**
 * 审核状态
 * @type {Array<{label: string, value: number}>}
 */
export const AUDIT_STATUS_OPTIONS = [
  { label: '待审核', value: 0 },
  { label: '已审核', value: 1 }
];

/**
 * 审核状态映射
 */
export const AUDIT_STATUS_MAP = {
  0: '待审核',
  1: '已审核'
};

// 机组类型（2000801：背压式供热机组，2000802：抽汽式供热机组，2000803：凝汽式供热机组，2000804：其他）
export const UNIT_TYPE_OPTIONS = [
  { label: '背压式供热机组', value: 2000801 },
  { label: '抽汽式供热机组', value: 2000802 },
  { label: '凝汽式供热机组', value: 2000803 },
  { label: '其他', value: 2000804 }
];

// 机组类型映射
export const UNIT_TYPE_MAP = {
  2000801: '背压式供热机组',
  2000802: '抽汽式供热机组',
  2000803: '凝汽式供热机组',
  2000804: '其他'
};

/**
 * 管线类型
 * @type {Array<{label: string, value: number}>}
 */
export const PIPELINE_TYPES = [
  { label: '一次网', value: 2000201 },
  { label: '二次网', value: 2000202 }
];

/**
 * 管线类型映射
 */
export const PIPELINE_TYPE_MAP = {
  2000201: '一次网',
  2000202: '二次网'
};

/**
 * 埋设方式
 * @type {Array<{label: string, value: number}>}
 */
export const BURIED_TYPES = [
  { label: '地埋式铺设', value: 2000301 },
  { label: '架空铺设', value: 2000302 },
  { label: '水平定向钻铺设', value: 2000303 },
  { label: '管道隧道铺设', value: 2000304 }
];

/**
 * 埋设方式映射
 */
export const BURIED_TYPE_MAP = {
  2000301: '地埋式铺设',
  2000302: '架空铺设',
  2000303: '水平定向钻铺设',
  2000304: '管道隧道铺设'
};

/**
 * 管材
 * @type {Array<{label: string, value: number}>}
 */
export const PIPE_MATERIALS = [
  { label: 'PE管', value: 2000401 },
  { label: '螺旋焊管', value: 2000402 },
  { label: '球墨铸铁管', value: 2000403 },
  { label: '钢管', value: 2000404 },
  { label: '其他', value: 2000405 }
];

/**
 * 管材映射
 */
export const PIPE_MATERIAL_MAP = {
  2000401: 'PE管',
  2000402: '螺旋焊管',
  2000403: '球墨铸铁管',
  2000404: '钢管',
  2000405: '其他'
};

/**
 * 线型
 * @type {Array<{label: string, value: number}>}
 */
export const LINE_TYPES = [
  { label: '非空管', value: 2000501 },
  { label: '空管', value: 2000502 },
  { label: '井内连线', value: 2000503 },
  { label: '垂直管线段', value: 2000504 },
  { label: '过河的架空线段', value: 2000505 },
  { label: '非开挖管(顶管)线段', value: 2000506 },
  { label: '顶管', value: 2000507 }
];

/**
 * 线型映射
 */
export const LINE_TYPE_MAP = {
  2000501: '非空管',
  2000502: '空管',
  2000503: '井内连线',
  2000504: '垂直管线段',
  2000505: '过河的架空线段',
  2000506: '非开挖管(顶管)线段',
  2000507: '顶管'
};

/**
 * 使用状态
 * @type {Array<{label: string, value: number}>}
 */
export const USAGE_STATUS = [
  { label: '使用中', value: 2000601 },
  { label: '报废', value: 2000602 },
  { label: '未使用', value: 2000603 }
];

/**
 * 使用状态映射
 */
export const USAGE_STATUS_MAP = {
  2000601: '使用中',
  2000602: '报废',
  2000603: '未使用'
};

/**
 * 管点类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const POINT_TYPE_OPTIONS = [
  { label: '阀门', value: 2000701 },
  { label: '弯头', value: 2000702 },
  { label: '变径点', value: 2000703 },
  { label: '变材点', value: 2000704 },
  { label: '多通点', value: 2000705 },
  { label: '良测点', value: 2000706 },
  { label: '探测点', value: 2000707 },
  { label: '预留口', value: 2000708 },
  { label: '非普查区', value: 2000709 },
  { label: '变深点', value: 2000710 },
  { label: '其他', value: 2000711 }
];

/**
 * 管点类型映射
 */
export const POINT_TYPE_MAP = {
  2000701: '阀门',
  2000702: '弯头',
  2000703: '变径点',
  2000704: '变材点',
  2000705: '多通点',
  2000706: '良测点',
  2000707: '探测点',
  2000708: '预留口',
  2000709: '非普查区',
  2000710: '变深点',
  2000711: '其他'
};

/**
 * 管点使用状态选项
 * @type {Array<{label: string, value: number}>}
 */
export const POINT_USAGE_STATUS_OPTIONS = [
  { label: '未使用', value: 5001 },
  { label: '使用中', value: 5002 },
  { label: '废弃', value: 5003 }
];

/**
 * 管点使用状态映射
 */
export const POINT_USAGE_STATUS_MAP = {
  5001: '未使用',
  5002: '使用中',
  5003: '废弃'
};

/**
 * 设备类型
 * @type {Array<{label: string, value: number}>}
 */
export const DEVICE_TYPES = [
  { label: '管网流量', value: 2001301 },
  { label: '管网热量', value: 2001302 },
  { label: '管网温度', value: 2001303 },
  { label: '用户室温', value: 2001304 },
  { label: '管网压力', value: 2001305 }
];

/**
 * 设备类型映射
 */
export const DEVICE_TYPE_MAP = {
  2001301: '管网流量',
  2001302: '管网热量',
  2001303: '管网温度',
  2001304: '用户室温',
  2001305: '管网压力'
};

/**
 * 监测指标
 * @type {Array<{label: string, value: number}>}
 */
export const MONITOR_INDEXES = [
  { label: '管网流量监测（m3/h）', value: 2001401 },
  { label: '管网热量监测（热量：MW）', value: 2001402 },
  { label: '管网温度监测（温度：℃）', value: 2001405 },
  { label: '用户室温监测（温度：℃）', value: 2001406 },
  { label: '管网压力监测（压力：Mpa）', value: 2001407 }
];

/**
 * 监测指标映射
 */
export const MONITOR_INDEX_MAP = {
  2001401: '管网流量监测（m3/h）',
  2001402: '管网热量监测（热量：MW）',
  2001405: '管网温度监测（温度：℃）',
  2001406: '用户室温监测（温度：℃）',
  2001407: '管网压力监测（压力：Mpa）'
};

/**
 * 生效状态
 * @type {Array<{label: string, value: boolean}>}
 */
export const ENABLE_STATUS = [
  { label: '否', value: false },
  { label: '是', value: true }
];

/**
 * 生效状态映射
 */
export const ENABLE_STATUS_MAP = {
  false: '否',
  true: '是'
};

/**
 * 设备在线状态
 * @type {Array<{label: string, value: number}>}
 */
export const DEVICE_STATUS_OPTIONS = [
  { label: '在线', value: 1 },
  { label: '离线', value: 0 }
];

/**
 * 设备在线状态映射
 */
export const DEVICE_STATUS_MAP = {
  1: '在线',
  0: '离线'
};

/**
 * 监测对象类型
 * @type {Array<{label: string, value: number}>}
 */
export const MONITOR_TARGET_OPTIONS = [
  { label: '管线', value: 2001501 },
  { label: '热源', value: 2001502 },
  { label: '换热站', value: 2001503 },
  { label: '用户', value: 2001504 },
  { label: '窨井', value: 2001505 }
];

/**
 * 监测对象类型映射
 */
export const MONITOR_TARGET_MAP = {
  2001501: '管线',
  2001502: '热源',
  2001503: '换热站',
  2001504: '用户',
  2001505: '窨井'
};

// ============ 管网运行监测预警管理相关常量 ============

// 报警级别选项
export const HEATING_ALARM_LEVEL_OPTIONS = [
  { label: '全部', value: '' },
  { label: '一级', value: 2001701 },
  { label: '二级', value: 2001702 },
  { label: '三级', value: 2001703 },
  { label: '四级', value: 2001704 }
];

// 报警级别映射
export const HEATING_ALARM_LEVEL_MAP = {
  2001701: '一级',
  2001702: '二级',
  2001703: '三级',
  2001704: '四级'
};

// 报警状态选项
export const HEATING_ALARM_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '待确认', value: 2001801 },
  { label: '误报', value: 2001802 },
  { label: '待处置', value: 2001803 },
  { label: '处置中', value: 2001804 },
  { label: '已处置', value: 2001805 },
  { label: '已归档', value: 2001806 }
];

// 报警状态映射
export const HEATING_ALARM_STATUS_MAP = {
  2001801: '待确认',
  2001802: '误报',
  2001803: '待处置',
  2001804: '处置中',
  2001805: '已处置',
  2001806: '已归档'
};

// 报警类型选项（复制自排水模块）
export const HEATING_ALARM_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '管网流量监测报警', value: 2003101 },
  { label: '管网温度监测报警', value: 2003102 },
  { label: '管网压力监测报警', value: 2003103 },
  { label: '用户室温监测报警', value: 2003104 },
  { label: '管网热量监测报警', value: 2003105 }
];

// 报警类型映射
export const HEATING_ALARM_TYPE_MAP = {
  2003101: '管网流量监测报警',
  2003102: '管网温度监测报警',
  2003103: '管网压力监测报警',
  2003104: '用户室温监测报警',
  2003105: '管网热量监测报警'
};

// 报警来源选项
export const HEATING_ALARM_SOURCE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '设备监测报警', value: '设备监测报警' },
  { label: '企业自报报警', value: '企业自报报警' },
  { label: '系统监测', value: '系统监测' }
];

// 报警来源映射
export const HEATING_ALARM_SOURCE_MAP = {
  '设备监测报警': '设备监测报警',
  '企业自报报警': '企业自报报警',
  '系统监测': '系统监测'
};

// 确认结果选项
export const HEATING_CONFIRM_RESULT_OPTIONS = [
  { label: '真实报警', value: 2002001 },
  { label: '误报', value: 2002002 }
];

// 确认结果映射
export const HEATING_CONFIRM_RESULT_MAP = {
  2002001: '真实报警',
  2002002: '误报'
};

// 处置状态选项
export const HEATING_HANDLE_STATUS_OPTIONS = [
  { label: '处置中', value: 2002101 },
  { label: '处置完成', value: 2002102 }
];

// 处置状态映射
export const HEATING_HANDLE_STATUS_MAP = {
  2002101: '处置中',
  2002102: '处置完成'
};

// 报警处置列表报警状态选项
export const HEATING_ALARM_PROCESS_STATUS_OPTIONS = [
  { label: '发生报警', value: 2001901 },
  { label: '确认报警', value: 2001902 },
  { label: '处置报警', value: 2001903 }
];

// 报警处置列表报警状态映射
export const HEATING_ALARM_PROCESS_STATUS_MAP = {
  2001901: '发生报警',
  2001902: '确认报警',
  2001903: '处置报警'
};

// 监测指标编码选项
export const HEATING_MONITOR_INDEX_CODE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '热量', value: 5003001 },
  { label: '温度', value: 5003002 },
  { label: '湿度', value: 5003003 },
  { label: '角度', value: 5003004 },
  { label: '水浸状态', value: 5003005 },
  { label: '井盖状态', value: 5003006 },
  { label: '流量', value: 5003007 },
  { label: '压力', value: 5003008 }
];

// 监测指标编码映射
export const HEATING_MONITOR_INDEX_CODE_MAP = {
  5003001: '热量',
  5003002: '温度',
  5003003: '湿度',
  5003004: '角度',
  5003005: '水浸状态',
  5003006: '井盖状态',
  5003007: '流量',
  5003008: '压力'
};

// ============ 建筑管理相关常量 ============

/**
 * 建筑类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const BUILDING_TYPE_OPTIONS = [
  { label: '住宅建筑', value: 2000901 },
  { label: '办公建筑', value: 2000902 },
  { label: '商业建筑', value: 2000903 },
  { label: '医疗建筑', value: 2000904 },
  { label: '工业建筑', value: 2000905 },
  { label: '学校建筑', value: 2000906 },
  { label: '其他', value: 2000907 }
];

/**
 * 建筑类型映射
 */
export const BUILDING_TYPE_MAP = {
  2000901: '住宅建筑',
  2000902: '办公建筑',
  2000903: '商业建筑',
  2000904: '医疗建筑',
  2000905: '工业建筑',
  2000906: '学校建筑',
  2000907: '其他'
};

/**
 * 建造年代选项
 * @type {Array<{label: string, value: string}>}
 */
export const CONSTRUCTION_YEAR_OPTIONS = [
  { label: '2020年', value: '2020年' },
  { label: '2019年', value: '2019年' },
  { label: '2018年', value: '2018年' },
  { label: '2017年', value: '2017年' },
  { label: '2016年', value: '2016年' },
  { label: '2015年', value: '2015年' },
  { label: '2010-2014年', value: '2010-2014年' },
  { label: '2005-2009年', value: '2005-2009年' },
  { label: '2000-2004年', value: '2000-2004年' },
  { label: '1995-1999年', value: '1995-1999年' },
  { label: '1990-1994年', value: '1990-1994年' },
  { label: '1980-1989年', value: '1980-1989年' },
  { label: '1970-1979年', value: '1970-1979年' },
  { label: '1970年以前', value: '1970年以前' }
];

// ============ 供热用户信息管理相关常量 ============

/**
 * 用户类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const USER_TYPE_OPTIONS = [
  { label: '居民', value: 2001001 },
  { label: '非居民', value: 2001002 }
];

/**
 * 用户类型映射
 */
export const USER_TYPE_MAP = {
  2001001: '居民',
  2001002: '非居民'
};

/**
 * 供热方式选项
 * @type {Array<{label: string, value: string}>}
 */
export const HEATING_TYPE_OPTIONS = [
  { label: '集中供热', value: '集中供热' },
  { label: '分散供热', value: '分散供热' },
  { label: '自采暖', value: '自采暖' },
  { label: '其他', value: '其他' }
];

// ============ 供热窨井信息管理相关常量 ============

/**
 * 井盖材质选项
 * @type {Array<{label: string, value: number}>}
 */
export const WELL_MATERIAL_OPTIONS = [
  { label: '铸铁', value: 2001201 },
  { label: '复合材料', value: 2001202 },
  { label: '钢', value: 2001203 },
  { label: '不锈钢', value: 2001204 },
  { label: '聚乙烯', value: 2001205 },
  { label: '铝合金', value: 2001206 },
  { label: '混凝土', value: 2001207 },
  { label: '其他', value: 2001208 }
];

/**
 * 井盖材质映射
 */
export const WELL_MATERIAL_MAP = {
  2001201: '铸铁',
  2001202: '复合材料',
  2001203: '钢',
  2001204: '不锈钢',
  2001205: '聚乙烯',
  2001206: '铝合金',
  2001207: '混凝土',
  2001208: '其他'
};

/**
 * 井盖形状选项
 * @type {Array<{label: string, value: number}>}
 */
export const WELL_SHAPE_OPTIONS = [
  { label: '圆形', value: 2001101 },
  { label: '方形', value: 2001102 },
  { label: '其他', value: 2001103 }
];

/**
 * 井盖形状映射
 */
export const WELL_SHAPE_MAP = {
  2001101: '圆形',
  2001102: '方形',
  2001103: '其他'
};

// ============ 预案管理相关常量 ============

/**
 * 事件分类选项
 * @type {Array<{label: string, value: number}>} 2003201：供热管网爆管停水，2003202：自然灾害事件，2003203：社会安全事件，2003204：其他
 */
export const EVENT_TYPE_OPTIONS = [
  { label: '管网破裂', value: 2003201 },   
  { label: '自然灾害事件', value: 2003202 },
  { label: '社会安全事件', value: 2003203 },
  { label: '其他', value: 2003204 }
];

/**
 * 事件分类映射 
 */
export const EVENT_TYPE_MAP = {
  2003201: '供热管网爆管停水',
  2003202: '自然灾害事件',
  2003203: '社会安全事件',
  2003204: '其他'
};

/**
 * 响应等级选项
 * @type {Array<{label: string, value: number}>} 2003301：|级(特别重大)，2003302：Ⅱ级(重大)，2003303：Ⅲ级(较大)，2003304：Ⅳ级（一般）
 */
export const RESPONSE_LEVEL_OPTIONS = [
  { label: 'Ⅰ级（特别重大）', value: 2003301 },
  { label: 'Ⅱ级（重大）', value: 2003302 },
  { label: 'Ⅲ级（较大）', value: 2003303 },
  { label: 'Ⅳ级（一般）', value: 2003304 }
];

/**
 * 响应等级映射
 */
export const RESPONSE_LEVEL_MAP = {
  2003301: 'Ⅰ级（特别重大）',
  2003302: 'Ⅱ级（重大）',
  2003303: 'Ⅲ级（较大）',
  2003304: 'Ⅳ级（一般）'
};

/**
 * 预案级别选项
 * @type {Array<{label: string, value: number}>} 2003401：国家级预案，2003402：省级预案，2003403：地方预案，2003404：专项预案
 */
export const SCHEME_LEVEL_OPTIONS = [
  { label: '国家级', value: 2003401 },  
  { label: '省级', value: 2003402 },
  { label: '地方预案', value: 2003403 },
  { label: '专项预案', value: 2003404 },
];

/**
 * 预案级别映射
 */
export const SCHEME_LEVEL_MAP = {
  2003401: '国家级',
  2003402: '省级',
  2003403: '地方预案',  
  2003404: '专项预案',
};

/**
 * 预案分类选项
 * @type {Array<{label: string, value: string}>}
 */
export const SCHEME_CLASSIFICATION_OPTIONS = [
  { label: '综合应急预案', value: '综合应急预案' },
  { label: '专项应急预案', value: '专项应急预案' },
  { label: '现场处置方案', value: '现场处置方案' },
  { label: '部门应急预案', value: '部门应急预案' }
];

/**
 * 预案审核状态选项
 * @type {Array<{label: string, value: number}>}
 */
export const PLAN_REVIEW_STATUS_OPTIONS = [
  { label: '通过', value: "1" },
  { label: '驳回', value: "2" }
];

/**
 * 预案审核状态映射
 */
export const PLAN_REVIEW_STATUS_MAP = {
  "0": '未审核',
  "1": '通过',
  "2": '驳回'
};

// ============ 供热安全事故信息管理相关常量 ============

/**
 * 是否人员伤亡选项
 * @type {Array<{label: string, value: number}>}
 */
export const CASUALTY_OPTIONS = [
  { label: '否', value: 0 },
  { label: '是', value: 1 }
];

/**
 * 是否人员伤亡映射
 */
export const CASUALTY_MAP = {
  0: '否',
  1: '是'
};

/**
 * 事件分级选项
 * @type {Array<{label: string, value: number}>} 2003001:特别重大, 2003002:重大, 2003003:较大, 2003004:一般
 */
export const ACCIDENT_EVENT_LEVEL_OPTIONS = [
  { label: '一级（特别重大）', value: 2003001 },
  { label: '二级（重大）', value: 2003002 },
  { label: '三级（较大）', value: 2003003 },
  { label: '四级（一般）', value: 2003004 }
];

/**
 * 事件分级映射
 */
export const ACCIDENT_EVENT_LEVEL_MAP = {
  2003001: '一级（特别重大）',
  2003002: '二级（重大）',
  2003003: '三级（较大）',
  2003004: '四级（一般）'
};

/**
 * 事件处置状态选项
 * @type {Array<{label: string, value: number}>} 2003101：未处置，2003102：处置中，2003103：已处置
 */
export const ACCIDENT_EVENT_STATUS_OPTIONS = [
  { label: '未处置', value: 2003101 },
  { label: '处置中', value: 2003102 },
  { label: '已处置', value: 2003103 }
];

/**
 * 事件处置状态映射
 */
export const ACCIDENT_EVENT_STATUS_MAP = {
  2003101: '未处置',
  2003102: '处置中',
  2003103: '已处置'
};

// ============ 危险源信息管理相关常量 ============

/**
 * 危险源建筑类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const DANGER_BUILDING_TYPE_OPTIONS = [
  { label: '危险化学品工厂', value: 6003401 },
  { label: '饭店', value: 6003402 },
  { label: '锅炉站', value: 6003403 },
  { label: '放射源', value: 6003404 },
  { label: '加气站', value: 6003405 },
  { label: '加油站', value: 6003406 },
  { label: '其他', value: 6003407 }
];

/**
 * 危险源建筑类型映射
 */
export const DANGER_BUILDING_TYPE_MAP = {
  6003401: '危险化学品工厂',
  6003402: '饭店',
  6003403: '锅炉站',
  6003404: '放射源',
  6003405: '加气站',
  6003406: '加油站',
  6003407: '其他'
};

/**
 * 是否重大危险源选项
 * @type {Array<{label: string, value: string}>}
 */
export const IS_MAJOR_DANGER_OPTIONS = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
];

/**
 * 是否重大危险源映射
 */
export const IS_MAJOR_DANGER_MAP = {
  '1': '是',
  '0': '否'
};

// ============ 防护目标信息管理相关常量 ============

/**
 * 防护目标建筑类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const PROTECTION_BUILDING_TYPE_OPTIONS = [
  { label: '学校', value: 6003501 },
  { label: '医院', value: 6003502 },
  { label: '重大基础设施/通讯、科技、体育、文化等社会事业', value: 6003503 },
  { label: '人员密集场所/如商场等', value: 6003504 },
  { label: '交通枢纽/如车站数据', value: 6003505 },
  { label: '其他', value: 6003506 }
];

/**
 * 防护目标建筑类型映射
 */
export const PROTECTION_BUILDING_TYPE_MAP = {
  6003501: '学校',
  6003502: '医院',
  6003503: '重大基础设施/通讯、科技、体育、文化等社会事业',
  6003504: '人员密集场所/如商场等',
  6003505: '交通枢纽/如车站数据',
  6003506: '其他'
};

/**
 * 是否重点防护目标选项
 * @type {Array<{label: string, value: string}>}
 */
export const IS_MAJOR_PROTECTION_OPTIONS = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
];

/**
 * 是否重点防护目标映射
 */
export const IS_MAJOR_PROTECTION_MAP = {
  '1': '是',
  '0': '否'
};

// ============ 供热管网风险评估管理相关常量 ============

/**
 * 管网风险等级选项
 * @type {Array<{label: string, value: number}>}
 */
export const PIPELINE_RISK_LEVEL_OPTIONS = [
  { label: '重大风险', value: 2002801 },
  { label: '较大风险', value: 2002802 },
  { label: '一般风险', value: 2002803 },
  { label: '低风险', value: 2002804 }
];

/**
 * 管网风险等级映射
 */
export const PIPELINE_RISK_LEVEL_MAP = {
  2002801: '重大风险',
  2002802: '较大风险',
  2002803: '一般风险',
  2002804: '低风险'
};

/**
 * 管网管控状态选项
 * @type {Array<{label: string, value: number}>}
 */
export const PIPELINE_CONTROL_STATUS_OPTIONS = [
  { label: '无需管控', value: 2002901 },
  { label: '未管控', value: 2002902 },
  { label: '已管控', value: 2002903 }
];

/**
 * 管网管控状态映射
 */
export const PIPELINE_CONTROL_STATUS_MAP = {
  2002901: '无需管控',
  2002902: '未管控',
  2002903: '已管控'
};

/**
 * 评估类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const ASSESSMENT_TYPE_OPTIONS = [
  { label: '系统评估', value: 0 },
  { label: '风险修改', value: 1 }
];

/**
 * 评估类型映射
 */
export const ASSESSMENT_TYPE_MAP = {
  0: '系统评估',
  1: '风险修改'
};

// ============ 供热场站风险评估管理相关常量 ============

/**
 * 场站风险等级选项
 * @type {Array<{label: string, value: number}>}
 */
export const STATION_RISK_LEVEL_OPTIONS = [
  { label: '重大风险', value: 3002401 },
  { label: '较大风险', value: 3002402 },
  { label: '一般风险', value: 3002403 },
  { label: '低风险', value: 3002404 }
];

/**
 * 场站风险等级映射
 */
export const STATION_RISK_LEVEL_MAP = {
  3002401: '重大风险',
  3002402: '较大风险',
  3002403: '一般风险',
  3002404: '低风险'
};

/**
 * 场站管控状态选项
 * @type {Array<{label: string, value: number}>}
 */
export const STATION_CONTROL_STATUS_OPTIONS = [
  { label: '无需管控', value: 3002501 },
  { label: '已管控', value: 3002502 },
  { label: '未管控', value: 3002503 }
];

/**
 * 场站管控状态映射
 */
export const STATION_CONTROL_STATUS_MAP = {
  3002501: '无需管控',
  3002502: '已管控',
  3002503: '未管控'
};

// ============ 供热隐患信息管理相关常量 ============

/**
 * 隐患等级选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_HIDDEN_DANGER_LEVEL_OPTIONS = [
  { label: '重大隐患', value: 2002401 },
  { label: '较大隐患', value: 2002402 },
  { label: '一般隐患', value: 2002403 }
];

/**
 * 隐患等级映射
 */
export const HEATING_HIDDEN_DANGER_LEVEL_MAP = {
  2002401: '重大隐患',
  2002402: '较大隐患',
  2002403: '一般隐患'
};

/**
 * 隐患来源选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_HIDDEN_DANGER_SOURCE_OPTIONS = [
  { label: '人工上报', value: 2002301 },
  { label: '系统同步', value: 2002302 }
];

/**
 * 隐患来源映射
 */
export const HEATING_HIDDEN_DANGER_SOURCE_MAP = {
  2002301: '人工上报',
  2002302: '系统同步'
};

/**
 * 隐患状态选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_HIDDEN_DANGER_STATUS_OPTIONS = [
  { label: '待整改', value: 2002701 },
  { label: '整改中', value: 2002702 },
  { label: '待复查', value: 2002703 },
  { label: '已整改', value: 2002704 }
];

/**
 * 隐患状态映射
 */
export const HEATING_HIDDEN_DANGER_STATUS_MAP = {
  2002701: '待整改',
  2002702: '整改中',
  2002703: '待复查',
  2002704: '已整改'
};

/**
 * 隐患对象选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_HIDDEN_DANGER_OBJECT_OPTIONS = [
  { label: '管线', value: 2002601 },
  { label: '热源', value: 2002602 },
  { label: '换热站', value: 2002603 },
  { label: '窨井', value: 2002604 },
  { label: '设备', value: 2002605 }
];

/**
 * 隐患对象映射
 */
export const HEATING_HIDDEN_DANGER_OBJECT_MAP = {
  2002601: '管线',
  2002602: '热源',
  2002603: '换热站',
  2002604: '窨井',
  2002605: '设备'
};

/**
 * 隐患类型选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_HIDDEN_DANGER_TYPE_OPTIONS = [
  { label: '管道泄漏隐患', value: 2002501 },
  { label: '管道堵塞隐患', value: 2002502 },
  { label: '管道破裂隐患', value: 2002503 },
  { label: '管道老化隐患', value: 2002504 },
  { label: '设备设施隐患', value: 2002505 },
  { label: '其他', value: 2002506 }
];

/**
 * 隐患类型映射
 */
export const HEATING_HIDDEN_DANGER_TYPE_MAP = {
  2002501: '管道泄漏隐患',
  2002502: '管道堵塞隐患',
  2002503: '管道破裂隐患',
  2002504: '管道老化隐患',
  2002505: '设备设施隐患',
  2002506: '其他'
};

/**
 * 整改状态选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_RECTIFY_STATUS_OPTIONS = [
  { label: '整改中', value: 7003501 },
  { label: '整改完成', value: 7003502 }
];

/**
 * 整改状态映射
 */
export const HEATING_RECTIFY_STATUS_MAP = {
  7003501: '整改中',
  7003502: '整改完成'
};

/**
 * 隐患流程步骤选项
 * @type {Array<{label: string, value: number}>}
 */
export const HEATING_PROCESS_STEP_OPTIONS = [
  { label: '上报', value: 7003401 },
  { label: '整改', value: 7003402 },
  { label: '复核', value: 7003403 }
];

/**
 * 隐患流程步骤映射
 */
export const HEATING_PROCESS_STEP_MAP = {
  7003401: '上报',
  7003402: '整改',
  7003403: '复核'
};

// ============ 管线维修记录相关常量 ============

/**
 * 维修结果选项
 * @type {Array<{label: string, value: number}>}
 */
export const REPAIR_RESULT_OPTIONS = [
  { label: '已完成', value: 2000701 },
  { label: '未完成', value: 2000702 }
];

/**
 * 维修结果映射
 */
export const REPAIR_RESULT_MAP = {
  2000701: '已完成',
  2000702: '未完成'
};
