<template>
  <div class="pipeline-maintenance-container">
    <!-- 搜索区域 -->
    <div class="pipeline-maintenance-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">维修单号:</span>
          <el-input v-model="formData.repairCode" class="form-input" placeholder="输入维修单号" />
        </div>
        <div class="form-item">
          <span class="label">关联管线:</span>
          <el-select v-model="formData.connectedPipelineId" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in pipelineOptions" :key="item.id" :label="item.pipelineCode" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">维修结果:</span>
          <el-select v-model="formData.repairResult" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in repairResultOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">权属单位:</span>
          <el-select v-model="formData.managementUnit" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">批量导入</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="repairCode" label="维修单号" min-width="120" />
      <el-table-column prop="connectedPipeline" label="关联管线" min-width="120" />
      <el-table-column prop="repairTypeName" label="维修类型" min-width="100" />
      <el-table-column prop="repairDesc" label="维修内容" min-width="150" />
      <el-table-column label="维修结果" min-width="100">
        <template #default="{ row }">
          <span :class="getResultClass(row.repairResult)">
            {{ getRepairResultText(row.repairResult) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="repairTime" label="维修时间" min-width="150" />
      <el-table-column prop="repairUser" label="维修人" min-width="100" />
      <el-table-column prop="managementUnitName" label="权属单位" min-width="120" />
      <el-table-column prop="address" label="维修位置" min-width="150" />
      <el-table-column label="操作" fixed="right" min-width="200">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
            <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <PipelineMaintenanceDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getPipelineMaintenancePage, 
  deletePipelineMaintenance, 
  getPipelineMaintenanceDetail,
  getAllEnterpriseList, 
  getPipelineList
} from '@/api/heating';
import { REPAIR_RESULT_OPTIONS, REPAIR_RESULT_MAP } from '@/constants/heating';
import { misPosition } from '@/hooks/gishooks';
import PipelineMaintenanceDialog from './PipelineMaintenanceDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 下拉选项数据
const repairResultOptions = ref(REPAIR_RESULT_OPTIONS);
const enterpriseOptions = ref([]);
const pipelineOptions = ref([]);

// 表单数据
const formData = ref({
  repairCode: '',
  connectedPipelineId: '',
  repairResult: '',
  managementUnit: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchMaintenanceData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    repairCode: '',
    connectedPipelineId: '',
    repairResult: '',
    managementUnit: ''
  };
  currentPage.value = 1;
  fetchMaintenanceData();
};

// 获取维修记录分页数据
const fetchMaintenanceData = async () => {
  try {
    loading.value = true;
    const params = {
      repairCode: formData.value.repairCode,
      connectedPipelineId: formData.value.connectedPipelineId,
      repairResult: formData.value.repairResult,
      managementUnit: formData.value.managementUnit
    };
    
    const res = await getPipelineMaintenancePage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取维修记录数据失败:', error);
    ElMessage.error('获取维修记录数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 获取管线列表
const fetchPipelines = async () => {
  try {
    const res = await getPipelineList();
    if (res && res.data) {
      pipelineOptions.value = res.data.map(item => ({
        id: item.id,
        pipelineCode: item.pipelineCode
      }));
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 获取维修结果文本
const getRepairResultText = (value) => {
  return REPAIR_RESULT_MAP[value] || '';
};

// 获取维修结果样式类
const getResultClass = (value) => {
  if (value === 2000701) { // 已完成
    return 'status-completed';
  } else if (value === 2000702) { // 未完成
    return 'status-pending';
  }
  return '';
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchMaintenanceData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchMaintenanceData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getPipelineMaintenanceDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取维修记录详情失败');
    }
  } catch (error) {
    console.error('获取维修记录详情失败:', error);
    ElMessage.error('获取维修记录详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getPipelineMaintenanceDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取维修记录详情失败');
    }
  } catch (error) {
    console.error('获取维修记录详情失败:', error);
    ElMessage.error('获取维修记录详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该维修记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deletePipelineMaintenance(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchMaintenanceData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除维修记录失败:', error);
      ElMessage.error('删除维修记录失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理导入
const handleImport = () => {
  console.log('导入');
  ElMessage.info('导入功能待实现');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchMaintenanceData();
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.pipeline-maintenance-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.pipeline-maintenance-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 80;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchEnterprises(),
      fetchPipelines(),
      fetchMaintenanceData()
    ]);
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.pipeline-maintenance-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.pipeline-maintenance-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 状态样式 */
.status-completed {
  color: #67C23A;
  font-weight: 500;
}

.status-pending {
  color: #E6A23C;
  font-weight: 500;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 