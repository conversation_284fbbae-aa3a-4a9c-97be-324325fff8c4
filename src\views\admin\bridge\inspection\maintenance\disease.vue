<template>
  <div class="bridge-disease-container">
    <!-- 搜索区域 -->
    <div class="bridge-disease-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属桥梁:</span>
          <el-select v-model="formData.bridgeId" class="form-input" placeholder="全部" clearable filterable>
            <el-option label="全部" value="" />
            <el-option v-for="item in bridgeOptions" :key="item.id" :label="item.bridgeName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">病害类型:</span>
          <el-select v-model="formData.defectType" class="form-input" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option v-for="item in defectTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">病害状态:</span>
          <el-select v-model="formData.isProcessed" class="form-input" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option v-for="item in processStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.diseaseName" class="form-input" placeholder="请输入病害名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="diseaseName" label="病害名称" min-width="120" />
      <el-table-column label="所属桥梁" min-width="120">
        <template #default="{ row }">
          {{ getBridgeName(row.bridgeId) }}
        </template>
      </el-table-column>
      <el-table-column prop="componentName" label="构件名称" min-width="120" />
      <el-table-column prop="partTypeName" label="部件名称" min-width="100" />
      <el-table-column prop="defectTypeName" label="病害类型" min-width="100" />
      <el-table-column prop="diseaseLevelName" label="病害等级" min-width="80" />
      <el-table-column prop="diseaseSourceName" label="病害来源" min-width="100" />
      <el-table-column prop="diseaseLocation" label="上报时间" min-width="120">
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="处理状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="row.isProcessed ? 'success' : 'warning'">
            {{ row.isProcessed ? '已处理' : '未处理' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="180">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
            <el-button 
              type="primary" 
              link 
              @click.stop="handleEdit(row)"
              :disabled="row.isProcessed"
            >
              编辑
            </el-button>
            <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <DiseaseDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElTag } from 'element-plus'
import { 
  getDiseaseRecordPage, 
  deleteDiseaseRecord, 
  getDiseaseRecordDetail,
  getBridgeBasicInfoList,
  DEFECT_TYPE_OPTIONS,
  DISEASE_LEVEL_OPTIONS,
  PROCESS_STATUS_OPTIONS
} from '@/api/bridge'
import DiseaseDialog from './components/DiseaseDialog.vue'
import moment from 'moment'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 下拉选项数据
const bridgeOptions = ref([])
const defectTypeOptions = ref(DEFECT_TYPE_OPTIONS)
const diseaseLevelOptions = ref(DISEASE_LEVEL_OPTIONS)
const processStatusOptions = ref(PROCESS_STATUS_OPTIONS)

// 表单数据
const formData = ref({
  bridgeId: '',
  defectType: '',
  diseaseLevel: '',
  isProcessed: '',
  diseaseName: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeOptions.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : '未知桥梁'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return moment(date).format('YYYY-MM-DD HH:mm:ss')
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchDiseaseData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    bridgeId: '',
    defectType: '',
    diseaseLevel: '',
    isProcessed: '',
    diseaseName: ''
  }
  currentPage.value = 1
  fetchDiseaseData()
}

// 获取病害分页数据
const fetchDiseaseData = async () => {
  try {
    loading.value = true
    const params = {
      bridgeId: formData.value.bridgeId,
      defectType: formData.value.defectType,
      diseaseLevel: formData.value.diseaseLevel,
      isProcessed: formData.value.isProcessed,
      diseaseName: formData.value.diseaseName
    }
    
    const res = await getDiseaseRecordPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取病害数据失败:', error)
    ElMessage.error('获取病害数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取桥梁列表
const fetchBridges = async () => {
  try {
    const res = await getBridgeBasicInfoList()
    if (res && res.data) {
      bridgeOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchDiseaseData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchDiseaseData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  if (row.isProcessed) {
    ElMessage.warning('已处理的病害数据不允许编辑')
    return
  }
  
  try {
    const res = await getDiseaseRecordDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取病害详情失败')
    }
  } catch (error) {
    console.error('获取病害详情失败:', error)
    ElMessage.error('获取病害详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getDiseaseRecordDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取病害详情失败')
    }
  } catch (error) {
    console.error('获取病害详情失败:', error)
    ElMessage.error('获取病害详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该病害记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDiseaseRecord(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchDiseaseData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除病害记录失败:', error)
      ElMessage.error('删除病害记录失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchDiseaseData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const container = document.querySelector('.bridge-disease-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const searchSection = container.querySelector('.bridge-disease-search');
    const headerSection = container.querySelector('.table-header');
    const paginationContainer = container.querySelector('.pagination-container');

    const searchHeight = searchSection ? searchSection.offsetHeight : 0;
    const headerHeight = headerSection ? headerSection.offsetHeight : 0;
    const paginationHeight = paginationContainer ? paginationContainer.offsetHeight : 0;
    
    const containerPadding = 16 * 2;
    const margins = 16 + 16;

    const otherElementsHeight = searchHeight + headerHeight + paginationHeight + containerPadding + margins;
    
    const containerRect = container.getBoundingClientRect();
    
    tableMaxHeight.value = window.innerHeight - containerRect.top - otherElementsHeight;
  });
};

const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchBridges(),
      fetchDiseaseData()
    ])
    calculateTableMaxHeight();
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.bridge-disease-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.bridge-disease-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
