<template>
  <div class="drainage-risk-plant-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="stats-card red">
        <div class="stats-number">{{ statisticsData.majorCount || 0 }}</div>
        <div class="stats-label">重大风险</div>
      </div>
      <div class="stats-card yellow">
        <div class="stats-number">{{ statisticsData.largeCount || 0 }}</div>
        <div class="stats-label">较大风险</div>
      </div>
      <div class="stats-card orange">
        <div class="stats-number">{{ statisticsData.normalCount || 0 }}</div>
        <div class="stats-label">一般风险</div>
      </div>
      <div class="stats-card blue">
        <div class="stats-number">{{ statisticsData.lowCount || 0 }}</div>
        <div class="stats-label">低风险</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">场站类型:</span>
          <el-select v-model="formData.factoryType" class="form-input" placeholder="全部">
            <el-option v-for="item in factoryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">风险等级:</span>
          <el-select v-model="formData.riskLevel" class="form-input" placeholder="全部">
            <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管控状态:</span>
          <el-select v-model="formData.factoryStatus" class="form-input" placeholder="全部">
            <el-option v-for="item in pipelineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.factoryName" class="form-input" placeholder="输入场站名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="warning" class="operation-btn" @click="handleExport">导出</el-button>
        <!-- <el-button type="primary" class="operation-btn" @click="handleAssessmentConfig">评估指标配置</el-button> -->
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :max-height="tableMaxHeight"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :scrollbar-always-on="true"
      :fit="true"
      v-loading="loading"
      empty-text="暂无数据"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="factoryName" label="污水厂名称" min-width="120" />
      <el-table-column prop="factoryId" label="污水厂编码" min-width="120" />
      <el-table-column label="厂站类型" min-width="100">
        <template #default="{ row }">
          {{ getFactoryTypeName(row.factoryType) }}
        </template>
      </el-table-column>
      <el-table-column label="污水处理设计规模（m³/天）" min-width="150">
        <template #default="{ row }">
          {{ row.processingCapacity }}
        </template>
      </el-table-column>
      <el-table-column prop="assessmentDate" label="投入运行时间" min-width="120">
        <template #default="{ row }">
          {{ formatDate(row.assessmentDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="assessor" label="权属单位" min-width="120" />
      <el-table-column label="位置" min-width="120">
        <template #default="{ row }">
          {{ row.address }}
        </template>
      </el-table-column>
      <el-table-column label="风险等级" min-width="100">
        <template #default="{ row }">
          <el-tag
            :type="getRiskLevelType(row.riskLevel)"
            class="risk-tag"
          >
            {{ getRiskLevelName(row.riskLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="evaluationDate" label="评估日期" min-width="120">
        <template #default="{ row }">
          {{ formatDate(row.evaluationDate) }}
        </template>
      </el-table-column>
      <el-table-column label="管控状态" min-width="80">
        <template #default="{ row }">
          {{ getPipelineStatusName(row.factoryStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="220">
        <template #default="{ row }">
          <div class="operation-btns">
            <!-- <el-button type="primary" link @click.stop="handleAssessmentRecord(row)">评估记录</el-button> -->
            <el-button type="primary" link @click.stop="handleEdit(row)">修改</el-button>
            <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 修改对话框 -->
    <PlantAssessmentDialog
      v-model:visible="dialogVisible"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElMessage, ElTag } from 'element-plus';
import { 
  getSewagePlantRiskAssessmentStatistics,
  getSewagePlantRiskAssessmentPage,
  getSewagePlantRiskAssessmentDetail
} from '@/api/drainage';
import { 
  SEWAGE_PLANT_TYPE_OPTIONS,
  RISK_LEVEL_OPTIONS,
  PIPELINE_STATUS_OPTIONS,
  RISK_LEVEL_MAP,
  PIPELINE_STATUS_MAP
} from '@/constants/drainage';
import { misPosition } from '@/hooks/gishooks';
import PlantAssessmentDialog from './components/PlantAssessmentDialog.vue';

// 加载状态
const loading = ref(false);

// 统计数据
const statisticsData = ref({
  majorCount: 0,
  largeCount: 0,
  normalCount: 0,
  lowCount: 0
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const factoryTypeOptions = SEWAGE_PLANT_TYPE_OPTIONS;
const riskLevelOptions = RISK_LEVEL_OPTIONS;
const pipelineStatusOptions = PIPELINE_STATUS_OPTIONS;

// 表单数据
const formData = ref({
  factoryType: '',
  riskLevel: '',
  factoryStatus: '',
  factoryName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogData = ref({});

// 表格最大高度
const tableMaxHeight = ref(500);

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 获取厂站类型名称
const getFactoryTypeName = (type) => {
  const option = factoryTypeOptions.find(item => item.value === type);
  return option ? option.label : '未知';
};

// 获取风险等级名称
const getRiskLevelName = (level) => {
  return RISK_LEVEL_MAP[level] || '未知';
};

// 获取风险等级标签类型
const getRiskLevelType = (level) => {
  const typeMap = {
    3002401: 'danger',  // 重大风险
    3002402: 'warning', // 较大风险
    3002403: '',        // 一般风险
    3002404: 'success'  // 低风险
  };
  return typeMap[level] || '';
};

// 获取管控状态名称
const getPipelineStatusName = (status) => {
  return PIPELINE_STATUS_MAP[status] || '未知';
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  return dateStr.split(' ')[0]; // 只显示日期部分
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchRiskAssessmentData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    factoryType: '',
    riskLevel: '',
    factoryStatus: '',
    factoryName: ''
  };
  currentPage.value = 1;
  fetchRiskAssessmentData();
};

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getSewagePlantRiskAssessmentStatistics();
    if (res && res.code === 200) {
      statisticsData.value = res.data || {
        majorCount: 0,
        largeCount: 0,
        normalCount: 0,
        lowCount: 0
      };
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
  }
};

// 获取风险评估分页数据
const fetchRiskAssessmentData = async () => {
  loading.value = true;
  try {
    const params = {
      factoryType: formData.value.factoryType,
      riskLevel: formData.value.riskLevel,
      factoryStatus: formData.value.factoryStatus,
      factoryName: formData.value.factoryName
    };
    
    const res = await getSewagePlantRiskAssessmentPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
      nextTick(() => {
        calculateTableMaxHeight();
      });
    }
  } catch (error) {
    console.error('获取风险评估数据失败:', error);
    ElMessage.error('获取风险评估数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchRiskAssessmentData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchRiskAssessmentData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理修改
const handleEdit = async (row) => {
  try {
    const res = await getSewagePlantRiskAssessmentDetail(row.id);
    if (res && res.code === 200) {
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
  }
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理评估指标配置
const handleAssessmentConfig = () => {
  console.log('评估指标配置');
  ElMessage.info('评估指标配置功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchRiskAssessmentData();
  fetchStatistics(); // 重新获取统计数据
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.drainage-risk-plant-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const statisticsSection = container.querySelector('.statistics-section');
    const statisticsHeight = statisticsSection ? statisticsSection.offsetHeight : 0;
    const paginationReservedHeight = 120;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - statisticsHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatistics(),
      fetchRiskAssessmentData()
    ]);
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.drainage-risk-plant-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #FFFFFF;
}

/* 数据统计区域样式 */
.statistics-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.stats-card {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stats-card.red {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
}

.stats-card.orange {
  background: linear-gradient(135deg, #ffa726, #ff9800);
}

.stats-card.yellow {
  background: linear-gradient(135deg, #ffeb3b, #ffc107);
  color: #333;
}

.stats-card.blue {
  background: linear-gradient(135deg, #42a5f5, #2196f3);
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
}

/* 表格样式 - 使用最大高度限制 */

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

.risk-tag {
  font-weight: bold;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style> 