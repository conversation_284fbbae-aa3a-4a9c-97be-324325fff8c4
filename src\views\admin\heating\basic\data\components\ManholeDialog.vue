<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="manhole-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管井编号" prop="wellCode">
            <el-input v-model="formData.wellCode" placeholder="请输入管井编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="探测点号" prop="detectCode">
            <el-input v-model="formData.detectCode" placeholder="请输入探测点号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="地面高程" prop="elevation">
            <el-input-number v-model="formData.elevation" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="特征" prop="feature">
            <el-input v-model="formData.feature" placeholder="请输入特征" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="附属物" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属物" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="旋转角" prop="rotationAngle">
            <el-input v-model="formData.rotationAngle" placeholder="请输入旋转角" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井盖形状" prop="wellShape">
            <el-select v-model="formData.wellShape" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellShapeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖材质" prop="wellMaterial">
            <el-select v-model="formData.wellMaterial" placeholder="请选择" class="w-full">
              <el-option v-for="item in wellMaterialOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井深 (m)" prop="wellDepth">
            <el-input-number v-model="formData.wellDepth" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖直径 (m)" prop="wellDiameter">
            <el-input-number v-model="formData.wellDiameter" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井盖长 (m)" prop="wellLength">
            <el-input-number v-model="formData.wellLength" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖宽 (m)" prop="wellWidth">
            <el-input-number v-model="formData.wellWidth" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井脖深 (m)" prop="wellNeckDepth">
            <el-input-number v-model="formData.wellNeckDepth" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井室直径 (m)" prop="wellRoomDiameter">
            <el-input-number v-model="formData.wellRoomDiameter" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="井室代码" prop="wellRoomCode">
            <el-input v-model="formData.wellRoomCode" placeholder="请输入井室代码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="井盖尺寸" prop="wellSize">
            <el-input v-model="formData.wellSize" placeholder="请输入井盖尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建造年代" prop="constructionYear">
            <el-select v-model="formData.constructionYear" placeholder="请选择" class="w-full">
              <el-option v-for="item in constructionYearOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full" @change="handleManagementUnitChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="探测单位" prop="detectUnit">
            <el-select v-model="formData.detectUnit" placeholder="请选择" class="w-full" @change="handleDetectUnitChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="探测日期" prop="detectTime">
            <el-date-picker
              v-model="formData.detectTime"
              type="date"
              placeholder="请选择探测日期"
              class="w-full"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维护井材质" prop="maintenanceWellMaterial">
            <el-input v-model="formData.maintenanceWellMaterial" placeholder="请输入维护井材质" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveManholeWell,
  updateManholeWell,
  getAllEnterpriseList
} from '@/api/heating';
import { 
  WELL_MATERIAL_OPTIONS, 
  WELL_SHAPE_OPTIONS, 
  CONSTRUCTION_YEAR_OPTIONS 
} from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import moment from 'moment';

// 使用从常量文件导入的选项
const wellMaterialOptions = WELL_MATERIAL_OPTIONS;
const wellShapeOptions = WELL_SHAPE_OPTIONS;
const constructionYearOptions = CONSTRUCTION_YEAR_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增/编辑',
    edit: '新增/编辑',
    view: '窨井信息详情'
  };
  return titles[props.mode] || '窨井信息';
});

// 下拉选项数据
const enterpriseOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  wellCode: '',
  detectCode: '',
  elevation: 0,
  feature: '',
  attachedFacilities: '',
  roadName: '',
  rotationAngle: '',
  wellShape: '',
  wellShapeName: '',
  wellMaterial: '',
  wellMaterialName: '',
  wellDepth: 0,
  wellDiameter: 0,
  wellLength: 0,
  wellWidth: 0,
  wellNeckDepth: 0,
  wellRoomDiameter: 0,
  wellRoomCode: '',
  wellSize: '',
  constructionYear: '',
  managementUnit: '',
  managementUnitName: '',
  detectUnit: '',
  detectUnitName: '',
  detectTime: '',
  maintenanceWellMaterial: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  geomText: ''
});

// 表单验证规则
const formRules = {
  wellCode: [{ required: true, message: '请输入管井编号', trigger: 'blur' }],
  wellShape: [{ required: true, message: '请选择井盖形状', trigger: 'change' }],
  wellMaterial: [{ required: true, message: '请选择井盖材质', trigger: 'change' }],
  constructionYear: [{ required: true, message: '请选择建造年代', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (['elevation', 'wellDepth', 'wellDiameter', 'wellLength', 'wellWidth', 'wellNeckDepth', 'wellRoomDiameter'].includes(key)) {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 井盖形状
  const selectedWellShape = wellShapeOptions.find(item => item.value === formData.wellShape);
  if (selectedWellShape) {
    formData.wellShapeName = selectedWellShape.label;
  }

  // 井盖材质
  const selectedWellMaterial = wellMaterialOptions.find(item => item.value === formData.wellMaterial);
  if (selectedWellMaterial) {
    formData.wellMaterialName = selectedWellMaterial.label;
  }

  // 权属单位
  const selectedManagementUnit = enterpriseOptions.value.find(item => item.value === formData.managementUnit);
  if (selectedManagementUnit) {
    formData.managementUnitName = selectedManagementUnit.label;
  }

  // 探测单位
  const selectedDetectUnit = enterpriseOptions.value.find(item => item.value === formData.detectUnit);
  if (selectedDetectUnit) {
    formData.detectUnitName = selectedDetectUnit.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 处理日期格式
    if (newVal.detectTime && typeof newVal.detectTime === 'object' && newVal.detectTime.time) {
      formData.detectTime = new Date(newVal.detectTime.time).toISOString().split('T')[0];
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.wellShape, (val) => {
  if (val) {
    const selected = wellShapeOptions.find(item => item.value === val);
    if (selected) {
      formData.wellShapeName = selected.label;
    }
  }
});

watch(() => formData.wellMaterial, (val) => {
  if (val) {
    const selected = wellMaterialOptions.find(item => item.value === val);
    if (selected) {
      formData.wellMaterialName = selected.label;
    }
  }
});

// 处理权属单位变化
const handleManagementUnitChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.managementUnitName = selected.label;
  }
};

// 处理探测单位变化
const handleDetectUnitChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.detectUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };
    submitData.detectTime = moment(submitData.detectTime).format('YYYY-MM-DD HH:mm:ss');
    let res;
    if (props.mode === 'add') {
      res = await saveManholeWell(submitData);
    } else if (props.mode === 'edit') {
      res = await updateManholeWell(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
});
</script>

<style scoped>
.manhole-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 