<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="notice-dialog"
    :destroy-on-close="true"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标题" prop="title">
            <el-input v-model="formData.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主体分类" prop="mainType">
            <el-select v-model="formData.mainType" placeholder="请选择" class="w-full" @change="handleMainTypeChange">
              <el-option v-for="item in mainTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="公文种类" prop="documentType">
            <el-select v-model="formData.documentType" placeholder="请选择" class="w-full" @change="handleDocumentTypeChange">
              <el-option v-for="item in documentTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重要程度" prop="importantLevel">
            <el-select v-model="formData.importantLevel" placeholder="请选择" class="w-full" @change="handleImportantLevelChange">
              <el-option v-for="item in importantLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布范围" prop="publishDeptId">
            <el-tree-select
              v-model="selectedDeptIds"
              :data="deptTreeOptions"
              multiple
              :render-after-expand="false"
              check-strictly
              :check-on-click-node="true"
              placeholder="请选择发布范围"
              class="w-full"
              @change="handleDeptChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="索引号" prop="indexNumber">
            <el-input v-model="formData.indexNumber" placeholder="请输入索引号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <div class="editor-container" v-if="dialogVisible">
              <div 
                ref="editorRef" 
                class="editor-content" 
                :id="`quill-editor-${editorId}`"
              ></div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="文件附件" prop="fileUrls">
            <div class="file-upload-container">
              <!-- 文件上传按钮 -->
              <div class="upload-header">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :file-list="[]"
                  :disabled="mode === 'view' || fileList.length >= 5"
                  :show-file-list="false"
                  multiple
                  accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx"
                >
                  <el-button 
                    type="primary" 
                    :disabled="mode === 'view' || fileList.length >= 5"
                  >
                    选择文件
                  </el-button>
                </el-upload>
                <span class="upload-tip">支持上传jpg、jpeg、png、pdf、doc、docx、xls、xlsx，大小50M以内</span>
              </div>
              
              <!-- 文件列表表格 -->
              <div class="file-list-table" v-if="fileList.length > 0">
                <div class="table-header">
                  <div class="column file-name">文件名</div>
                  <div class="column file-size">大小</div>
                  <div class="column file-actions">操作</div>
                </div>
                <div 
                  class="table-row" 
                  v-for="(file, index) in fileList" 
                  :key="file.uid || index"
                >
                  <div class="column file-name">
                    <div class="file-info">
                      <el-icon class="file-icon">
                        <Document v-if="isDocumentFile(file.name)" />
                        <Picture v-else-if="isImageFile(file.name)" />
                        <Files v-else />
                      </el-icon>
                      <span class="file-name-text">{{ file.name }}</span>
                    </div>
                  </div>
                  <div class="column file-size">
                    {{ formatFileSize(file.size) }}
                  </div>
                  <div class="column file-actions">
                    <div class="action-buttons">
                      <el-button 
                        link 
                        style="color: #409eff"
                        size="small"
                        @click="handleFileRemove(file, index)"
                        v-if="mode !== 'view'"
                      >
                        删除
                      </el-button>
                      <el-button 
                        link 
                        style="color: #409eff"
                        size="small"
                        @click="handleFileDownload(file)"
                        v-if="file.url && file.status === 'success'"
                      >
                        下载
                      </el-button>
                      <div class="upload-status" v-if="file.status === 'uploading'">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>上传中</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files, Loading } from '@element-plus/icons-vue'
import { saveNotice, updateNotice } from '@/api/comprehensive'
import { getDeptTree } from '@/api/system'
import { IMPORTANT_LEVEL_OPTIONS, MAIN_TYPE_OPTIONS, DOCUMENT_TYPE_OPTIONS } from '@/constants/comprehensive'
import { uploadFile } from '@/api/upload'

// 引入Quill富文本编辑器
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const uploadRef = ref(null)
const editorRef = ref(null)

// 富文本编辑器实例和唯一ID
let quillEditor = null
const editorId = ref(Date.now())
const editorInitialized = ref(false)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增通知公告',
    edit: '编辑通知公告',
    view: '通知公告详情'
  }
  return titles[props.mode] || '通知公告'
})

// 下拉选项数据
const importantLevelOptions = ref(IMPORTANT_LEVEL_OPTIONS)
const mainTypeOptions = ref(MAIN_TYPE_OPTIONS)
const documentTypeOptions = ref(DOCUMENT_TYPE_OPTIONS)
const deptTreeOptions = ref([])

// 文件列表
const fileList = ref([])

// 部门选择状态
const selectedDeptIds = ref([])

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  content: '',
  mainType: '',
  mainTypeName: '',
  documentType: '',
  importantLevel: '',
  importantLevelName: '',
  publishDeptId: '',
  publishDeptName: '',
  circulationDeptId: '',
  circulationDeptName: '',
  indexNumber: '',
  source: '系统创建', // 默认值设置为"系统创建"
  fileUrls: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  mainType: [{ required: true, message: '请选择主体分类', trigger: 'change' }],
  documentType: [{ required: true, message: '请选择公文种类', trigger: 'change' }],
  importantLevel: [{ required: true, message: '请选择重要程度', trigger: 'change' }],
  publishDeptId: [{ required: true, message: '请选择发布范围', trigger: 'change' }],
  content: [{ required: true, message: '', trigger: 'blur' }]
}

// 彻底销毁编辑器
const destroyEditor = () => {
  if (quillEditor) {
    try {
      quillEditor.off('text-change')
      quillEditor = null
    } catch (e) {
      console.log('编辑器销毁异常:', e)
    }
  }
  editorInitialized.value = false
  
  // 彻底清理DOM
  if (editorRef.value) {
    editorRef.value.innerHTML = ''
    // 移除所有可能残留的工具栏
    const toolbars = document.querySelectorAll('.ql-toolbar')
    toolbars.forEach(toolbar => {
      if (toolbar && toolbar.parentNode) {
        toolbar.parentNode.removeChild(toolbar)
      }
    })
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'source') {
      formData[key] = '系统创建'
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
  selectedDeptIds.value = []
  
  // 重置编辑器
  destroyEditor()
}

// 初始化富文本编辑器
const initQuillEditor = async () => {
  if (!editorRef.value || editorInitialized.value) return
  
  // 确保先销毁之前的编辑器
  destroyEditor()
  
  // 等待DOM完全清理
  await nextTick()
  
  try {
    const toolbarOptions = [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image']
    ]

    quillEditor = new Quill(editorRef.value, {
      theme: 'snow',
      placeholder: '',
      modules: {
        toolbar: toolbarOptions
      },
      readOnly: props.mode === 'view'
    })

    editorInitialized.value = true

    // 监听内容变化
    quillEditor.on('text-change', () => {
      if (quillEditor) {
        formData.content = quillEditor.root.innerHTML
      }
    })
    
    // 如果有初始内容，设置到编辑器中
    if (formData.content && formData.content.trim()) {
      quillEditor.root.innerHTML = formData.content
    }
    
  } catch (error) {
    console.error('编辑器初始化失败:', error)
  }
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 获取文件扩展名
const getFileExtension = (url) => {
  const match = url.match(/\.[^.]*$/)
  return match ? match[0] : ''
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理部门选择回显
    if (newVal.publishDeptId) {
      selectedDeptIds.value = newVal.publishDeptId.split(',').filter(id => id.trim())
    }
    
    // 处理文件显示
    if (newVal.fileUrls) {
      const urls = newVal.fileUrls.split(',').filter(url => url.trim())
      const existingFiles = urls.map((url, index) => ({
        name: `attachment_${index + 1}${getFileExtension(url)}`,
        url: url.trim(),
        uid: Date.now() + index,
        status: 'success',
        size: 0
      }))
      fileList.value = existingFiles
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 监听对话框显示状态
watch(() => props.visible, async (val) => {
  if (val) {
    // 生成新的编辑器ID
    editorId.value = Date.now()
    
    // 等待DOM渲染完成后初始化编辑器
    await nextTick()
    setTimeout(async () => {
      await initQuillEditor()
      
      // 如果有内容需要设置
      if (props.data && props.data.content && quillEditor) {
        setTimeout(() => {
          if (quillEditor && quillEditor.root) {
            quillEditor.root.innerHTML = props.data.content
          }
        }, 200)
      }
    }, 100)
  } else {
    // 对话框关闭时彻底清理
    destroyEditor()
  }
})

// 处理主体分类变化
const handleMainTypeChange = (value) => {
  const selected = mainTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.mainTypeName = selected.label
  }
}

// 处理公文种类变化
const handleDocumentTypeChange = (value) => {
  // documentType直接存储文本值，不需要额外的Name字段
  // 因为value和label已经是相同的文本
}

// 处理重要程度变化
const handleImportantLevelChange = (value) => {
  const selected = importantLevelOptions.value.find(item => item.value === value)
  if (selected) {
    formData.importantLevelName = selected.label
  }
}

// 处理部门选择变化
const handleDeptChange = (values) => {
  if (values && values.length > 0) {
    formData.publishDeptId = values.join(',')
    // 获取选中部门的名称
    const selectedNames = []
    const findDeptNames = (nodes, targetIds) => {
      for (const node of nodes) {
        if (targetIds.includes(node.value)) {
          selectedNames.push(node.label)
        }
        if (node.children) {
          findDeptNames(node.children, targetIds)
        }
      }
    }
    findDeptNames(deptTreeOptions.value, values)
    formData.publishDeptName = selectedNames.join(',')
  } else {
    formData.publishDeptId = ''
    formData.publishDeptName = ''
  }
}

// 处理文件变化
const handleFileChange = async (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 5) {
    ElMessage.warning('最多只能上传5个文件')
    return false
  }

  // 检查文件大小
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过 50MB!')
    return false
  }

  // 添加到文件列表，状态为上传中
  const fileItem = {
    name: file.name,
    size: file.size,
    uid: file.uid || Date.now(),
    status: 'uploading',
    raw: file,
    url: ''
  }
  
  fileList.value.push(fileItem)

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response && response.status === 200) {
      // 更新文件状态为成功
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value[index].url = response.data.url
        fileList.value[index].status = 'success'
      }
      
      // 更新formData中的fileUrls
      updateFileUrls()
      
      ElMessage.success('文件上传成功')
    } else {
      // 上传失败，移除文件
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value.splice(index, 1)
      }
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 上传失败，移除文件
    const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
const handleFileRemove = (file, index) => {
  fileList.value.splice(index, 1)
  updateFileUrls()
  ElMessage.success('文件已移除')
}

// 处理文件下载
const handleFileDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 更新文件URL字符串
const updateFileUrls = () => {
  nextTick(() => {
    const urls = fileList.value
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    formData.fileUrls = urls.join(',')
  })
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200) {
      // 转换数据格式以适配el-tree-select
      const convertTree = (nodes) => {
        return nodes.map(node => ({
          value: node.id,
          label: node.name,
          children: node.children ? convertTree(node.children) : undefined
        }))
      }
      deptTreeOptions.value = convertTree(res.data || [])
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 获取富文本编辑器内容
    if (quillEditor) {
      formData.content = quillEditor.root.innerHTML
    }

    // 验证内容不能为空
    if (!formData.content || formData.content.trim() === '' || formData.content === '<p><br></p>') {
      ElMessage.error('请输入内容')
      return
    }

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveNotice(submitData)
    } else if (props.mode === 'edit') {
      res = await updateNotice(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件卸载时清理编辑器
onUnmounted(() => {
  destroyEditor()
})

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree()
})
</script>

<style scoped>
.notice-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

/* 富文本编辑器样式 */
.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.editor-content {
  min-height: 200px;
}

:deep(.ql-toolbar) {
  border-bottom: 1px solid #dcdfe6 !important;
  background: #f5f7fa !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

:deep(.ql-container) {
  border: none !important;
  font-size: 14px;
  font-family: inherit;
}

:deep(.ql-editor) {
  min-height: 200px;
  line-height: 1.6;
  font-size: 14px;
  font-family: PingFangSC, PingFang SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #303133;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
  font-size: 14px;
}

:deep(.ql-editor p) {
  margin: 0 0 8px 0;
  line-height: 1.6;
}

:deep(.ql-editor h1, .ql-editor h2, .ql-editor h3, .ql-editor h4, .ql-editor h5, .ql-editor h6) {
  margin: 12px 0 8px 0;
  line-height: 1.4;
}

:deep(.ql-editor ul, .ql-editor ol) {
  margin: 8px 0;
  padding-left: 20px;
}

:deep(.ql-editor li) {
  margin: 4px 0;
  line-height: 1.6;
}

/* 文件上传样式 */
.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

/* 文件列表表格样式 */
.file-list-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f5f7fa;
}

.column {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.file-name {
  flex: 1;
  min-width: 0;
}

.file-size {
  width: 100px;
  justify-content: center;
}

.file-actions {
  width: 140px;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
}

.upload-status .el-icon {
  font-size: 14px;
}

.upload-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 