<template>
  <div class="bridge-safety-score-container">
    <!-- 搜索区域 -->
    <div class="bridge-safety-score-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">桥梁名称:</span>
          <el-select 
            v-model="formData.bridgeId" 
            class="form-input" 
            placeholder="请选择桥梁"
            filterable
            clearable
          >
            <el-option 
              v-for="item in bridgeOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">评分时间:</span>
          <el-date-picker
            v-model="formData.scoreTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="form-input"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table 
      :data="tableData" 
      style="width: 100%" 
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" 
      @row-click="handleRowClick" 
      :max-height="tableMaxHeight"
      empty-text="暂无数据"
      v-loading="tableLoading"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="bridgeName" label="桥梁名称" min-width="150" />
      <el-table-column label="评分时间" min-width="150">
        <template #default="{ row }">
          {{ formatScoreTime(row.scoreTime) }}
        </template>
      </el-table-column>
      <el-table-column label="评分结果" min-width="100" align="center">
        <template #default="{ row }">
          <span class="score-value">{{ row.totalScore || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="风险等级" min-width="100" align="center">
        <template #default="{ row }">
          <span 
            class="risk-level-tag" 
            :style="{ color: getRiskLevelColor(row.riskLevel), background: getRiskLevelBgColor(row.riskLevel) }"
          >
            {{ getRiskLevelName(row.riskLevel) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="120">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleDetail(row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <SafetyScoreDialog
      v-model:visible="dialogVisible"
      :score-id="currentScoreId"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { 
  getBridgeSafetyScorePage,
  getBridgeBasicInfoList,
} from '@/api/bridge'
import { BRIDGE_RISK_LEVEL_MAP, BRIDGE_RISK_LEVEL_COLOR_MAP } from '@/constants/bridge'
import SafetyScoreDialog from './components/SafetyScoreDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const tableLoading = ref(false)
const tableMaxHeight = ref(500)

// 桥梁下拉选项
const bridgeOptions = ref([])

// 表单数据
const formData = ref({
  bridgeId: '',
  scoreTime: null
})

// 对话框相关
const dialogVisible = ref(false)
const currentScoreId = ref('')

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取风险等级名称
const getRiskLevelName = (riskLevel) => {
  return BRIDGE_RISK_LEVEL_MAP[riskLevel] || '--'
}

// 获取风险等级颜色
const getRiskLevelColor = (riskLevel) => {
  return BRIDGE_RISK_LEVEL_COLOR_MAP[riskLevel] || '#333'
}

// 获取风险等级背景色
const getRiskLevelBgColor = (riskLevel) => {
  const colorMap = {
    4004301: 'rgba(255, 77, 79, 0.1)',   // 重大风险
    4004302: 'rgba(255, 122, 69, 0.1)',  // 较大风险
    4004303: 'rgba(250, 173, 20, 0.1)',  // 一般风险
    4004304: 'rgba(82, 196, 26, 0.1)'    // 低风险
  }
  return colorMap[riskLevel] || 'rgba(0, 0, 0, 0.1)'
}

// 格式化评分时间
const formatScoreTime = (scoreTime) => {
  if (!scoreTime) return '--'
  
  if (typeof scoreTime === 'string') {
    return moment(scoreTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  if (scoreTime.year !== undefined) {
    // 处理Java Date对象格式
    const year = scoreTime.year + 1900
    const month = scoreTime.month
    const date = scoreTime.date
    const hours = scoreTime.hours || 0
    const minutes = scoreTime.minutes || 0
    const seconds = scoreTime.seconds || 0
    
    return moment({
      year, month, date, hours, minutes, seconds
    }).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return '--'
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchScoreData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    bridgeId: '',
    scoreTime: null
  }
  currentPage.value = 1
  fetchScoreData()
}

// 获取安全评分分页数据
const fetchScoreData = async () => {
  tableLoading.value = true
  try {
    const params = {
      bridgeId: formData.value.bridgeId
    }
    
    // 处理时间范围参数
    if (formData.value.scoreTime && formData.value.scoreTime.length === 2) {
      params.startTime = formData.value.scoreTime[0] + ' 00:00:00'
      params.endTime = formData.value.scoreTime[1] + ' 23:59:59'
    }
    
    const res = await getBridgeSafetyScorePage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error('获取安全评分数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取安全评分数据失败:', error)
    ElMessage.error('获取安全评分数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList()
    if (res && res.code === 200 && res.data) {
      bridgeOptions.value = res.data.map(item => ({
        label: item.bridgeName,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取桥梁列表失败:', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchScoreData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchScoreData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理查看详情
const handleDetail = (row) => {
  currentScoreId.value = row.id
  dialogVisible.value = true
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const container = document.querySelector('.bridge-safety-score-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const searchSection = container.querySelector('.bridge-safety-score-search');
    const paginationContainer = container.querySelector('.pagination-container');

    const searchHeight = searchSection ? searchSection.offsetHeight : 0;
    const paginationHeight = paginationContainer ? paginationContainer.offsetHeight : 0;
    
    const containerPadding = 16 * 2;
    const margins = 16;

    const otherElementsHeight = searchHeight + paginationHeight + containerPadding + margins;
    
    const containerRect = container.getBoundingClientRect();
    
    tableMaxHeight.value = window.innerHeight - containerRect.top - otherElementsHeight;
  });
};

const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchBridgeList(),
      fetchScoreData()
    ])
    calculateTableMaxHeight();
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.bridge-safety-score-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.bridge-safety-score-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 200px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

:deep(.el-select) {
  width: 200px;
}

:deep(.el-date-editor) {
  width: 280px;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.score-value {
  font-weight: 500;
  font-size: 16px;
  color: #1890ff;
}

.risk-level-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 标题样式 */
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.font-bold {
  font-weight: 700;
}

.mb-4 {
  margin-bottom: 1rem;
}
</style>