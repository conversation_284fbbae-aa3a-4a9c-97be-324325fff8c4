<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="pipeline-maintenance-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修单号" prop="repairCode">
            <el-input v-model="formData.repairCode" placeholder="请输入维修单号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联管线" prop="connectedPipelineId">
            <el-select v-model="formData.connectedPipelineId" placeholder="请选择" class="w-full" @change="handlePipelineChange">
              <el-option v-for="item in pipelineOptions" :key="item.id" :label="item.pipelineCode" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修类型" prop="repairTypeName">
            <el-input v-model="formData.repairTypeName" placeholder="请输入维修类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附属设施" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" placeholder="请输入附属设施" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修内容" prop="repairDesc">
            <el-input v-model="formData.repairDesc" type="textarea" :rows="3" placeholder="请输入维修内容" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修结果" prop="repairResult">
            <el-select v-model="formData.repairResult" placeholder="请选择" class="w-full" >
              <el-option v-for="item in repairResultOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修时间" prop="repairTime">
            <el-date-picker
              v-model="formData.repairTime"
              type="datetime"
              placeholder="请选择维修时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修人" prop="repairUser">
            <el-input v-model="formData.repairUser" placeholder="请输入维修人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full" @change="handleManagementUnitChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修方案" prop="repairScheme">
            <el-input v-model="formData.repairScheme" type="textarea" :rows="3" placeholder="请输入维修方案" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修前照片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleBeforePicChange"
              :file-list="beforePicList"
              list-type="picture-card"
              :limit="3"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，最多3张
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修后照片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleAfterPicChange"
              :file-list="afterPicList"
              list-type="picture-card"
              :limit="3"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，最多3张
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              :limit="5"
              :disabled="mode === 'view'"
              multiple
            >
              <el-button type="primary">上传附件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，最多5个文件
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { Plus, Location } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import moment from 'moment';
import {
  savePipelineMaintenance,
  updatePipelineMaintenance,
  getAllEnterpriseList,
  getPipelineList
} from '@/api/heating';
import { REPAIR_RESULT_OPTIONS } from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import { uploadFile } from '@/api/upload';
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增维修记录',
    edit: '编辑维修记录',
    view: '维修记录详情'
  };
  return titles[props.mode] || '维修记录';
});

// 下拉选项数据
const repairResultOptions = ref(REPAIR_RESULT_OPTIONS);
const enterpriseOptions = ref([]);
const pipelineOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 文件列表
const beforePicList = ref([]);
const afterPicList = ref([]);
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  repairCode: '',
  connectedPipeline: '',
  connectedPipelineId: '',
  repairType: '',
  repairTypeName: '',
  attachedFacilities: '',
  repairDesc: '',
  repairScheme: '',
  repairResult: '',
  repairTime: '',
  repairUser: '',
  contactInfo: '',
  managementUnit: '',
  managementUnitName: '',
  address: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  longitude: '',
  latitude: '',
  beforePicUrl: '',
  afterPicUrl: '',
  fileUrl: ''
});

// 表单验证规则
const formRules = {
  repairCode: [{ required: true, message: '请输入维修单号', trigger: 'blur' }],
  connectedPipelineId: [{ required: true, message: '请选择关联管线', trigger: 'change' }],
  repairType: [{ required: true, message: '请输入维修类型', trigger: 'blur' }],
  repairDesc: [{ required: true, message: '请输入维修内容', trigger: 'blur' }],
  repairResult: [{ required: true, message: '请选择维修结果', trigger: 'change' }],
  repairTime: [{ required: true, message: '请选择维修时间', trigger: 'change' }],
  repairUser: [{ required: true, message: '请输入维修人', trigger: 'blur' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  contactInfo: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'latitude' || key === 'longitude') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  beforePicList.value = [];
  afterPicList.value = [];
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理图片显示
    if (newVal.beforePicUrl) {
      beforePicList.value = newVal.beforePicUrl.split(',').map((url, index) => ({
        name: `before_image_${index}`,
        url: url,
        uid: Date.now() + index
      }));
    }
    
    if (newVal.afterPicUrl) {
      afterPicList.value = newVal.afterPicUrl.split(',').map((url, index) => ({
        name: `after_image_${index}`,
        url: url,
        uid: Date.now() + index + 1000
      }));
    }
    
    if (newVal.fileUrl) {
      fileList.value = newVal.fileUrl.split(',').map((url, index) => ({
        name: `file_${index}`,
        url: url,
        uid: Date.now() + index + 2000
      }));
    }
    
    // 设置区域级联选择器的值
    if (newVal.town) {
      formData.town = [newVal.city, newVal.county, newVal.town];
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理管线选择变化
const handlePipelineChange = (value) => {
  const selected = pipelineOptions.value.find(item => item.id === value);
  if (selected) {
    formData.connectedPipeline = selected.pipelineCode;
  }
};

// 处理维修结果选择变化
// const handleRepairResultChange = (value) => {
//   const selected = repairResultOptions.value.find(item => item.value === value);
//   if (selected) {
//     formData.repairTypeName = selected.label;
//   }
// };

// 处理权属单位选择变化
const handleManagementUnitChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.managementUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  console.log('value',value);
  if (value && value.length > 0) {
    formData.city = value[0] || '';
    formData.county = value[0] || '';
    formData.town = value[1] || '';
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 获取管线列表
const fetchPipelines = async () => {
  try {
    const res = await getPipelineList();
    if (res && res.data) {
      pipelineOptions.value = res.data.map(item => ({
        id: item.id,
        pipelineCode: item.pipelineCode
      }));
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 维修前照片上传处理
const handleBeforePicChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!');
    return;
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.beforePicUrl ? formData.beforePicUrl.split(',') : [];
      urls.push(response.data.url);
      formData.beforePicUrl = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 维修后照片上传处理
const handleAfterPicChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!');
    return;
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.afterPicUrl ? formData.afterPicUrl.split(',') : [];
      urls.push(response.data.url);
      formData.afterPicUrl = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 附件上传处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    ElMessage.error('上传文件大小不能超过 20MB!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.fileUrl ? formData.fileUrl.split(',') : [];
      urls.push(response.data.url);
      formData.fileUrl = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    
    // 处理时间格式
    if (submitData.repairTime) {
      submitData.repairTime = moment(submitData.repairTime).format('YYYY-MM-DD HH:mm:ss');
    }

    let res;
    if (props.mode === 'add') {
      res = await savePipelineMaintenance(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePipelineMaintenance(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
  fetchPipelines();
});
</script>

<style scoped>
.pipeline-maintenance-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 