<template>
  <PanelBox title="风险清单" class="drainage-risk-left-bottom">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
      <PipelineDetailModal v-model="pipelineDetailVisible" :pipeline-data="selectedPipeline" />
    </template>
    <div class="panel-content">
      <!-- 数据为空时显示无数据组件 -->
      <div v-if="!loading && pipelineList.length === 0" class="no-data-wrapper">
        <NoData />
      </div>
      <!-- 有数据时显示滚动表格组件 -->
      <div v-else>
        <ScrollTable :columns="tableColumns" :data="pipelineList" :autoScroll="true" :scrollSpeed="3000"
          :tableHeight="tableHeight" :visibleRows="4" @row-click="openPipelineDetail">
          <!-- 自定义风险等级列 -->
          <template #riskLevel="{ row }">
            <span :style="{ color: getRiskColor(row.riskLevel) }">{{ row.riskLevel }}</span>
          </template>
        </ScrollTable>
        <!-- 更多按钮放置在右下角 -->
        <div class="more-btn-container">
          <div class="more-btn" @click="handleMoreClick">更多</div>
        </div>
      </div>
    </div>
  </PanelBox>
  <PipelineDetailModal v-model="pipelineDetailVisible" :pipeline-data="selectedPipeline" />
  <RiskListModal v-model="riskListVisible" :pipe-type="riskListPipeType" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import NoData from '@/components/common/NoData.vue'
import PipelineDetailModal from './PipelineDetailModal.vue'
import RiskListModal from './RiskListModal.vue'
import {
  getRainPipelineRiskList,
  getSewagePipelineRiskList,
  getFactoryRiskList,
  getStationRiskList
} from '@/api/drainage'

// 管线类型选项 - 与 LeftTopPanel.vue 保持一致
const pipeTypeOptions = [
  { value: 'rain', label: '雨水管线' },
  { value: 'sewage', label: '污水管线' },
  { value: 'plant', label: '污水厂' },
  { value: 'station', label: '泵站' }
]

// 默认选择雨水管线
const selectedPipeType = ref('rain')

// 加载状态
const loading = ref(false)

// 实际数据存储
const riskData = ref([])

// 动态表格列配置
const tableColumns = computed(() => {
  const baseColumns = [
    { title: '风险等级', dataIndex: 'riskLevel', width: '25%', fontSize: '13px', slot: 'riskLevel' }
  ]
  
  if (selectedPipeType.value === 'rain' || selectedPipeType.value === 'sewage') {
    return [
      { title: '管线编码', dataIndex: 'code', width: '25%', fontSize: '13px' },
      { title: '管龄', dataIndex: 'age', width: '15%', fontSize: '13px' },
      { title: '材质', dataIndex: 'material', width: '15%', fontSize: '13px' },
      { title: '管径', dataIndex: 'diameter', width: '20%', fontSize: '13px' },
      ...baseColumns
    ]
  } else if (selectedPipeType.value === 'plant') {
    return [
      { title: '污水厂编码', dataIndex: 'code', width: '25%', fontSize: '13px' },
      { title: '污水厂名称', dataIndex: 'name', width: '25%', fontSize: '13px' },
      { title: '类型', dataIndex: 'type', width: '25%', fontSize: '13px' },
      ...baseColumns
    ]
  } else if (selectedPipeType.value === 'station') {
    return [
      { title: '泵站编码', dataIndex: 'code', width: '25%', fontSize: '13px' },
      { title: '泵站名称', dataIndex: 'name', width: '25%', fontSize: '13px' },
      { title: '类型', dataIndex: 'type', width: '25%', fontSize: '13px' },
      ...baseColumns
    ]
  }
  return baseColumns
})

// 动态计算表格高度
const tableHeight = computed(() => {
  // 根据不同分辨率动态调整表格高度
  if (window.innerHeight < 910) {
    return '160px' // 910px高度的屏幕使用更小的表格高度
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '180px' // 940px-1055px高度的屏幕
  } else if (window.innerWidth >= 2561) {
    return '220px' // 超宽屏幕
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '220px' // 标准宽屏
  } else {
    return '200px' // 默认高度
  }
})



// 弹窗控制
const pipelineDetailVisible = ref(false)
const selectedPipeline = ref({
  riskCode: 'DFX-001',
  code: '18768_590845',
  type: '雨水管网',
  material: 'PE100',
  diameter: '400mm',
  length: '1.151km',
  location: '*******',
  buildTime: '2020年1月5日',
  riskLevel: '低风险',
  riskValue: '98',
  controlStatus: '未管控'
})
const riskListVisible = ref(false)
const riskListPipeType = computed(() => selectedPipeType.value)

// 计算当前展示数据
const pipelineList = computed(() => {
  return riskData.value || []
})

// 处理管线类型变化
const handlePipeTypeChange = () => {
  console.log('管线类型变更为:', selectedPipeType.value)
  // 这里可以调用接口获取对应类型的风险数据
  fetchRiskData(selectedPipeType.value)
}

// 打开管线详情弹窗
const openPipelineDetail = (row) => {
  // 在实际项目中，这里应该调用接口获取详细信息
  // const response = await api.getPipelineDetail(row.code)
  // selectedPipeline.value = response.data

  // 目前使用模拟数据
  selectedPipeline.value = {
    ...selectedPipeline.value,
    code: row.code,
    material: row.material,
    riskLevel: row.riskLevel
  }
  pipelineDetailVisible.value = true
}

// 点击更多按钮处理
const handleMoreClick = () => {
  riskListVisible.value = true
}

// 根据风险等级获取对应的颜色
const getRiskColor = (riskLevel) => {
  switch (riskLevel) {
    case '重大风险':
      return '#FF2330'
    case '较大风险':
      return '#FF9000'
    case '一般风险':
      return '#FFD11B'
    case '低风险':
      return '#00B0FF'
    default:
      return '#FFFFFF'
  }
}

// 从后端获取数据的方法
const fetchRiskData = async (pipeType) => {
  try {
    loading.value = true
    let response
    
    // 根据管线类型调用不同的接口
    switch (pipeType) {
      case 'rain':
        response = await getRainPipelineRiskList(1, 10)
        break
      case 'sewage':
        response = await getSewagePipelineRiskList(1, 10)
        break
      case 'plant':
        response = await getFactoryRiskList(1, 10)
        break
      case 'station':
        response = await getStationRiskList(1, 10)
        break
      default:
        console.warn('未知的管线类型:', pipeType)
        return
    }
    
    if (response.code === 200 && response.data && response.data.records) {
      // 数据格式转换
      const records = response.data.records
      if (records.length === 0) {
        riskData.value = []
        return
      }
      
      riskData.value = records.map(item => {
        if (pipeType === 'rain' || pipeType === 'sewage') {
          // 管线数据格式
          return {
            id: item.id,
            code: item.pipelineCode || '',
            age: item.pipelineAge ? `${item.pipelineAge}年` : '',
            material: item.pipelineMaterialName || '',
            diameter: item.pipelineDiameter ? `${item.pipelineDiameter}M` : '',
            riskLevel: item.riskLevelName || ''
          }
        } else if (pipeType === 'plant') {
          // 污水厂数据格式
          return {
            id: item.id,
            code: item.factoryCode || '',
            name: item.factoryName || '',
            type: item.factoryTypeName || '',
            riskLevel: item.riskLevelName || ''
          }
        } else if (pipeType === 'station') {
          // 泵站数据格式
          return {
            id: item.id,
            code: item.stationCode || '',
            name: item.stationName || '',
            type: item.stationTypeName || '',
            riskLevel: item.riskLevelName || ''
          }
        }
        return item
      })
    } else {
      riskData.value = []
    }
  } catch (error) {
    console.error('获取风险数据失败:', error)
    riskData.value = []
  } finally {
    loading.value = false
  }
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchRiskData(newValue)
})

onMounted(() => {
  // 初始化时获取数据
  fetchRiskData(selectedPipeType.value)
})
</script>

<style scoped>
.drainage-risk-left-bottom {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 无数据组件样式 */
.no-data-wrapper {
  height: 100%;
  display: flex;
  min-height: 200px;
  align-items: center;
  justify-content: center;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table td) {
  cursor: pointer;
  transition: background-color 0.2s;
  line-height: 2.3rem;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 更多按钮容器样式 */
.more-btn-container {
  position: relative;
  width: 100%;
  height: 0;
}

/* 更多按钮样式 */
.more-btn {
  position: absolute;
  right: 5px;
  bottom: -5px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  z-index: 10;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
}

@media screen and (min-width: 2561px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
    gap: 15px;
  }
}

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  .drainage-risk-left-bottom {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .more-btn-container {
    height: 0;
  }

  .more-btn {
    font-size: 11px;
    padding: 4px;
    bottom: 5px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .drainage-risk-left-bottom {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
    gap: 0px;
  }

  .more-btn-container {
    height: 0;
  }

  .more-btn {
    bottom: 15px;
    z-index: 10;
  }
}
</style>