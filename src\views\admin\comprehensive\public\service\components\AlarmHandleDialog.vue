<template>
  <el-dialog
    v-model="dialogVisible"
    title="报警处置"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="alarm-handle-dialog"
  >
    <div class="handle-container">
      <!-- 报警基本信息 -->
      <div class="basic-info-section">
        <h3 class="section-title">报警信息</h3>
        <div class="basic-info">
          <div class="info-item">
            <span class="label">报警编号：</span>
            <span class="value">{{ data.alarmCode || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">报警标题：</span>
            <span class="value">{{ data.alarmTitle || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">紧急程度：</span>
            <span class="value">
              <el-tag :type="getUrgentLevelType(data.urgentLevel)" size="small">
                {{ data.urgentLevelName || '-' }}
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">报警时间：</span>
            <span class="value">{{ formatDateTime(data.alarmTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 处置记录列表 -->
      <div class="handle-list-section">
        <div class="section-header">
          <h3 class="section-title">处置记录</h3>
          <el-button type="primary" size="small" @click="handleAddHandle">+ 新增处置</el-button>
        </div>
        <div class="handle-table-container">
          <el-table :data="handleList" style="width: 100%" empty-text="暂无处置记录">
            <el-table-column label="序号" width="60">
              <template #default="{ $index }">{{ $index + 1 }}</template>
            </el-table-column>
            <el-table-column label="处置状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getHandleStatusType(row.handleStatus)" size="small">
                  {{ row.handleStatusName || '-' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="handleUserName" label="处置人员" width="100" />
            <el-table-column prop="handleUnitName" label="处置单位" width="120" />
            <el-table-column prop="dealTime" label="处置时间" width="150">
              <template #default="{ row }">
                {{ formatDateTime(row.dealTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="处置描述" min-width="200" show-overflow-tooltip />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row, $index }">
                <el-button type="primary" link size="small" @click="handleEditHandle(row, $index)">编辑</el-button>
                <el-button type="danger" link size="small" @click="handleDeleteHandle(row, $index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 新增/编辑处置表单 -->
      <div v-show="showHandleForm" class="handle-form-section">
        <div class="section-header">
          <h3 class="section-title">{{ editingHandleIndex !== -1 ? '编辑' : '新增' }}处置</h3>
          <el-button size="small" @click="cancelHandleForm">取消</el-button>
        </div>
        <el-form
          ref="handleFormRef"
          :model="handleFormData"
          :rules="handleFormRules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="处置状态" prop="handleStatus">
                <el-select v-model="handleFormData.handleStatus" placeholder="请选择" class="w-full" @change="handleStatusChange">
                  <el-option v-for="item in handleStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处置时间" prop="dealTime">
                <el-date-picker
                  v-model="handleFormData.dealTime"
                  type="datetime"
                  placeholder="请选择处置时间"
                  class="w-full"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="处置人员" prop="handleUserName">
                <el-input v-model="handleFormData.handleUserName" placeholder="请输入处置人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处置单位" prop="handleUnit">
                <el-tree-select
                  v-model="handleFormData.handleUnit"
                  :data="deptTreeOptions"
                  :props="{
                    value: 'id',
                    label: 'name',
                    children: 'children'
                  }"
                  placeholder="请选择处置单位"
                  class="w-full"
                  @change="handleDeptChange"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="处置描述" prop="description">
                <el-input
                  v-model="handleFormData.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入处置描述"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 文件上传区域 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="处置前照片">
                <FileUpload
                  v-model:file-list="beforePicList"
                  accept="image/*"
                  :limit="3"
                  @update:urls="updateBeforePicUrls"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处置后照片">
                <FileUpload
                  v-model:file-list="afterPicList"
                  accept="image/*"
                  :limit="3"
                  @update:urls="updateAfterPicUrls"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处置附件">
                <FileUpload
                  v-model:file-list="fileList"
                  :limit="3"
                  @update:urls="updateFileUrls"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input
                  v-model="handleFormData.remark"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-button type="primary" @click="submitHandleForm">保存</el-button>
                <el-button @click="cancelHandleForm">取消</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAlarmHandleList,
  addAlarmHandle,
  deleteAlarmHandle,
} from '@/api/comprehensive'
import { getDeptListTree } from '@/api/system'
import FileUpload from './FileUpload.vue'
import moment from 'moment'
import { HANDLE_STATUS_OPTIONS_ALARM } from '@/constants/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const handleFormRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 数据
const handleList = ref([])
const showHandleForm = ref(false)
const editingHandleIndex = ref(-1)
const deptTreeOptions = ref([])
const handleStatusOptions = ref(HANDLE_STATUS_OPTIONS_ALARM)

// 文件列表
const beforePicList = ref([])
const afterPicList = ref([])
const fileList = ref([])

// 处置表单数据
const handleFormData = reactive({
  id: '',
  alarmId: '',
  handleStatus: '',
  handleStatusName: '',
  handleUnit: '',
  handleUnitName: '',
  handleUserName: '',
  dealTime: '',
  description: '',
  beforePicUrls: '',
  afterPicUrls: '',
  fileUrls: '',
  remark: ''
})

// 表单验证规则
const handleFormRules = {
  handleStatus: [{ required: true, message: '请选择处置状态', trigger: 'change' }],
  handleUserName: [{ required: true, message: '请输入处置人员', trigger: 'blur' }],
  handleUnit: [{ required: true, message: '请选择处置单位', trigger: 'change' }],
  dealTime: [{ required: true, message: '请选择处置时间', trigger: 'change' }],
  description: [{ required: true, message: '请输入处置描述', trigger: 'blur' }]
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 获取紧急程度标签类型
const getUrgentLevelType = (level) => {
  switch (level) {
    case 7001401: // 特别重大（Ⅰ级）
      return 'danger'
    case 7001402: // 重大（Ⅱ级）
      return 'warning'
    case 7001403: // 较大（Ⅲ级）
      return 'info'
    case 7001404: // 一般（Ⅳ级）
      return 'success'
    default:
      return ''
  }
}

// 获取处理状态标签类型
const getHandleStatusType = (status) => {
  switch (status) {
    case 7001701: // 处置中
      return 'warning'
    case 7001702: // 已处置
      return 'success'
    default:
      return ''
  }
}

// 重置处置表单
const resetHandleForm = () => {
  Object.keys(handleFormData).forEach(key => {
    handleFormData[key] = ''
  })
  handleFormData.alarmId = props.data.id || ''
  beforePicList.value = []
  afterPicList.value = []
  fileList.value = []
}

// 处理状态变化
const handleStatusChange = (value) => {
  const selected = handleStatusOptions.value.find(item => item.value === value)
  if (selected) {
    handleFormData.handleStatusName = selected.label
  }
}

// 处理部门选择变化
const handleDeptChange = (value, option) => {
  if (option) {
    handleFormData.handleUnitName = option.deptName
  }
}

// 更新处置前照片URLs
const updateBeforePicUrls = (urls) => {
  handleFormData.beforePicUrls = urls.join(',')
}

// 更新处置后照片URLs
const updateAfterPicUrls = (urls) => {
  handleFormData.afterPicUrls = urls.join(',')
}

// 更新文件URLs
const updateFileUrls = (urls) => {
  handleFormData.fileUrls = urls.join(',')
}

// 获取处置列表
const fetchHandleList = async () => {
  if (!props.data.id) return
  
  try {
    const res = await getAlarmHandleList(props.data.id)
    if (res && res.code === 200) {
      handleList.value = res.data || []
    }
  } catch (error) {
    console.error('获取处置列表失败:', error)
    ElMessage.error('获取处置列表失败')
  }
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptListTree({})
    if (res && res.status === 200) {
      deptTreeOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取部门树失败', error)
  }
}

// 新增处置
const handleAddHandle = () => {
  showHandleForm.value = true
  editingHandleIndex.value = -1
  resetHandleForm()
}

// 编辑处置
const handleEditHandle = (row, index) => {
  showHandleForm.value = true
  editingHandleIndex.value = index
  
  // 填充表单数据
  Object.keys(handleFormData).forEach(key => {
    if (row[key] !== undefined) {
      handleFormData[key] = row[key]
    }
  })
  
  // 处理文件列表
  if (row.beforePicUrls) {
    beforePicList.value = row.beforePicUrls.split(',').map((url, i) => ({
      uid: Date.now() + i,
      name: `before_pic_${i + 1}`,
      url: url.trim(),
      status: 'success'
    }))
  }
  
  if (row.afterPicUrls) {
    afterPicList.value = row.afterPicUrls.split(',').map((url, i) => ({
      uid: Date.now() + i + 100,
      name: `after_pic_${i + 1}`,
      url: url.trim(),
      status: 'success'
    }))
  }
  
  if (row.fileUrls) {
    fileList.value = row.fileUrls.split(',').map((url, i) => ({
      uid: Date.now() + i + 200,
      name: `file_${i + 1}`,
      url: url.trim(),
      status: 'success'
    }))
  }
}

// 删除处置
const handleDeleteHandle = (row, index) => {
  ElMessageBox.confirm('确定要删除该处置记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteAlarmHandle(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchHandleList()
        emit('success')
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除处置失败:', error)
      ElMessage.error('删除处置失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 提交处置表单
const submitHandleForm = async () => {
  if (!handleFormRef.value) return

  try {
    await handleFormRef.value.validate()

    const submitData = { ...handleFormData }

    const res = await addAlarmHandle(submitData)
    if (res && res.code === 200) {
      ElMessage.success(editingHandleIndex.value !== -1 ? '更新成功' : '新增成功')
      cancelHandleForm()
      fetchHandleList()
      emit('success')
    } else {
      ElMessage.error(res?.msg || (editingHandleIndex.value !== -1 ? '更新失败' : '新增失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 取消处置表单
const cancelHandleForm = () => {
  showHandleForm.value = false
  editingHandleIndex.value = -1
  if (handleFormRef.value) {
    handleFormRef.value.resetFields()
  }
  resetHandleForm()
}

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (visible && props.data.id) {
    fetchHandleList()
  }
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  handleList.value = []
  cancelHandleForm()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree()
  if (props.visible && props.data.id) {
    fetchHandleList()
  }
})
</script>

<style scoped>
.alarm-handle-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.handle-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.basic-info-section,
.handle-list-section,
.handle-form-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-left: 4px solid #0277FD;
  padding-left: 12px;
}

.basic-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 8px;
}

.value {
  color: #333;
}

.handle-table-container {
  background: white;
  border-radius: 6px;
  padding: 16px;
}

.w-full {
  width: 100%;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .basic-info {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style> 