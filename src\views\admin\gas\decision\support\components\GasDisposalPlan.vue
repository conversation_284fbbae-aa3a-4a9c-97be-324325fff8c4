<template>
  <div class="gas-disposal-plan">
    <!-- 搜索区域 -->
    <GasDisposalSearch @search="handleSearch" @reset="handleReset" />

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :max-height="tableMaxHeight"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :scrollbar-always-on="true"
      :fit="true"
      v-loading="loading"
      empty-text="暂无数据"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="schemeName" label="方案名称" min-width="120" />
      <el-table-column prop="sourceUnitName" label="来源单位" min-width="100" />
      <el-table-column label="匹配报警类型" min-width="120">
        <template #default="scope">
          {{ getAlarmTypeName(scope.row.alarmType) }}
        </template>
      </el-table-column>
      <el-table-column label="附件" min-width="100">
        <template #default="scope">
          <span v-if="scope.row.fileUrl" class="operation-btn-text" @click.stop="handleDownload(scope.row)">查看附件</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="editDate" label="编辑日期" min-width="120" />
      <el-table-column label="操作" min-width="220" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDownload(scope.row)">下载</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 弹窗组件 -->
    <GasDisposalPlanDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentRow"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElMessageBox } from 'element-plus';
import GasDisposalSearch from './GasDisposalSearch.vue';
import GasDisposalPlanDialog from './GasDisposalPlanDialog.vue';
import { getGasDisposalPlanPage, deleteGasDisposalPlan, saveGasDisposalPlan, updateGasDisposalPlan, getGasDisposalPlanDetail } from '@/api/gas';
import { ALARM_TYPE_MAP } from '@/constants/gas';

// 加载状态
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({
  sourceUnit: '',
  alarmType: '',
  schemeName: ''
});

// 表格最大高度相关
const tableMaxHeight = ref(500);



// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1; // 重置到第一页
  fetchPlanData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {
    sourceUnit: '',
    alarmType: '',
    schemeName: ''
  };
  currentPage.value = 1; // 重置到第一页
  fetchPlanData();
};

// 获取方案数据
const fetchPlanData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    };

    const res = await getGasDisposalPlanPage(params);
    if (res && res.code === 200) { // 根据接口返回的code值调整
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
      nextTick(() => {
        calculateTableMaxHeight();
      });
    } else {
      ElMessage.error(res?.message || '获取处置方案列表失败');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取处置方案列表失败', error);
    ElMessage.error('获取处置方案列表失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchPlanData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchPlanData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const currentRow = ref(null);

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add';
  currentRow.value = {};
  dialogVisible.value = true;
};

const handleDetail = async (row) => {
  try {
    const res = await getGasDisposalPlanDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      currentRow.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败', error);
    ElMessage.error('获取详情失败');
  }
};

const handleEdit = async (row) => {
  try {
    const res = await getGasDisposalPlanDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      currentRow.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败', error);
    ElMessage.error('获取详情失败');
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该处置方案吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteGasDisposalPlan(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPlanData(); // 重新加载数据
      } else {
        ElMessage.error(res?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除处置方案失败', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理弹窗提交
const handleDialogSuccess = async (formData) => {
  try {
    let res;
    if (dialogMode.value === 'add') {
      res = await saveGasDisposalPlan(formData);
    } else if (dialogMode.value === 'edit') {
      res = await updateGasDisposalPlan(formData);
    }

    if (res && res.code === 200) {
      ElMessage.success(dialogMode.value === 'add' ? '新增成功' : '更新成功');
      fetchPlanData(); // 重新加载数据
    } else {
      ElMessage.error(res?.message || (dialogMode.value === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('操作失败', error);
    ElMessage.error('操作失败');
  }
};

const handleDownload = (row) => {
  if (row.fileUrl) {
    window.open(row.fileUrl, '_blank');
  } else {
    ElMessage.warning('没有可下载的附件');
  }
};

// 获取报警类型名称
const getAlarmTypeName = (type) => {
  return type ? ALARM_TYPE_MAP[type] || '-' : '-';
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.gas-disposal-plan');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchPlanData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.gas-disposal-plan {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden;
}

.search-section {
  margin-bottom: 8px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 - 使用最大高度限制 */

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>