<template>
  <div class="emergency-shelter-container">
    <!-- 搜索区域 -->
    <div class="emergency-shelter-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">场所名称:</span>
          <el-input v-model="formData.shelterName" class="form-input" placeholder="请输入场所名称" />
        </div>
        <div class="form-item">
          <span class="label">当前状态:</span>
          <el-select v-model="formData.shelterStatus" class="form-input" placeholder="请选择状态">
            <el-option label="全部" value="" />
            <el-option v-for="item in shelterStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
 
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="shelterName" label="应急避难场所名称" min-width="160" />
      <el-table-column prop="longitude" label="经度" min-width="100" />
      <el-table-column prop="latitude" label="纬度" min-width="100" />
      <el-table-column prop="shelterCapacity" label="所能容纳人数" min-width="120" />
      <el-table-column prop="shelterStatusName" label="当前状态" min-width="100" />
      <el-table-column prop="contactUser" label="联系人" min-width="100" />
      <el-table-column prop="contactInfo" label="联系电话" min-width="120" />
      <el-table-column prop="dutyPhone" label="值班电话" min-width="120" />
      <el-table-column prop="address" label="详细地址" min-width="200" />
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <EmergencyShelterDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  getEmergencyShelterPage, 
  deleteEmergencyShelter, 
  getEmergencyShelterDetail
} from '@/api/comprehensive';
import { EMERGENCY_SHELTER_STATUS_OPTIONS } from '@/constants/comprehensive';
import { misPosition } from '@/hooks/gishooks';
import EmergencyShelterDialog from './EmergencyShelterDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const tableMaxHeight = ref(500);

// 下拉选项数据
const shelterStatusOptions = ref(EMERGENCY_SHELTER_STATUS_OPTIONS);

// 表单数据
const formData = ref({
  shelterName: '',
  shelterStatus: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchShelterData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    shelterName: '',
    shelterStatus: ''
  };
  currentPage.value = 1;
  fetchShelterData();
};

// 获取避难场所分页数据
const fetchShelterData = async () => {
  loading.value = true;
  try {
    const params = {
      shelterName: formData.value.shelterName,
      shelterStatus: formData.value.shelterStatus
    };
    
    const res = await getEmergencyShelterPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取避难场所数据失败:', error);
    ElMessage.error('获取避难场所数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 其他处理函数（新增、编辑、删除、定位等）实现类似救援队伍组件
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchShelterData();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchShelterData();
};

const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

const handleEdit = async (row) => {
  try {
    const res = await getEmergencyShelterDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error('获取避难场所详情失败');
  }
};

const handleDetail = async (row) => {
  try {
    const res = await getEmergencyShelterDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error('获取避难场所详情失败');
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该避难场所信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEmergencyShelter(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchShelterData();
      }
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

const handleLocation = (row) => {
  if (row.latitude && row.longitude) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

const handleDialogSuccess = () => {
  fetchShelterData();
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.emergency-shelter-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.emergency-shelter-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 48;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchShelterData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
/* 样式复用救援队伍组件的样式 */
.emergency-shelter-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

.emergency-shelter-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

.search-btn, .reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>