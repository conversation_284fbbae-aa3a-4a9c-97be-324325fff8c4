<template>
  <div class="gas-adjacent-manhole-info">
    <!-- 搜索区域 -->
    <div class="search-section">
      <GasAdjacentManholeSearch @search="handleSearch" @reset="handleReset" />
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <!-- <el-button type="primary" class="operation-btn" @click="handleSync">同步窨井数据</el-button> -->
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :max-height="tableMaxHeight"
      :scrollbar-always-on="true"
      :fit="true"
      v-loading="loading"
      empty-text="暂无数据"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="wellCode" label="窨井编码" min-width="100" />
      <el-table-column prop="wellTypeName" label="窨井类型" min-width="100" />
      <el-table-column prop="roadName" label="所在道路" min-width="120" />
      <el-table-column prop="wellDepth" label="井深(m)" min-width="100" />
      <el-table-column prop="wellShapeName" label="井盖形状" min-width="80" />
      <el-table-column prop="wellMaterialName" label="井盖材质" min-width="80" />
      <el-table-column prop="constructionTime" label="建设时间" min-width="170" />
      <el-table-column label="位置" show-overflow-tooltip min-width="270">
        <template #default="{ row }">
          <span :title="formatPosition(row)" class="text-ellipsis">
            {{ formatPosition(row) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="managementUnitName" label="权属单位" min-width="220" show-overflow-tooltip>
        <template #default="scope">
          <span :title="scope.row.managementUnitName" class="text-ellipsis">
            {{ scope.row.managementUnitName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="300" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 窨井弹窗 -->
    <GasAdjacentManholeDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentManholeData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElMessageBox } from 'element-plus';
import GasAdjacentManholeSearch from './GasAdjacentManholeSearch.vue';
import GasAdjacentManholeDialog from './GasAdjacentManholeDialog.vue';
import { 
  getSurroundingWellPage, 
  deleteSurroundingWell, 
  exportSurroundingWell, 
  importSurroundingWell,
  getSurroundingWellDetail
} from '@/api/gas';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add', 'edit', 'view'
const currentManholeData = ref({});

// 表格最大高度相关
const tableMaxHeight = ref(500);

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchManholeData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchManholeData();
};

// 获取窨井数据
const fetchManholeData = async () => {
  try {
    loading.value = true;
    const response = await getSurroundingWellPage(currentPage.value, pageSize.value, queryParams.value);
    if (response && response.code === 200) {
      tableData.value = response.data.records || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error('获取窨井数据失败');
    }
  } catch (error) {
    console.error('获取窨井数据异常:', error);
    ElMessage.error('获取窨井数据异常');
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchManholeData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchManholeData();
};

// 格式化位置信息
const formatPosition = (row) => {
  if (row.longitude && row.latitude) {
    return `经度: ${row.longitude}, 纬度: ${row.latitude}`;
  }
  return '暂无位置信息';
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 处理弹窗成功提交
const handleDialogSuccess = () => {
  fetchManholeData();
};

// 获取窨井详情
const fetchManholeDetail = async (id) => {
  try {
    loading.value = true;
    const response = await getSurroundingWellDetail(id);
    if (response && response.code === 200) {
      return response.data;
    } else {
      ElMessage.error('获取窨井详情失败');
      return null;
    }
  } catch (error) {
    console.error('获取窨井详情异常:', error);
    ElMessage.error('获取窨井详情异常');
    return null;
  } finally {
    loading.value = false;
  }
};

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add';
  currentManholeData.value = {};
  dialogVisible.value = true;
};

const handleImport = () => {
  console.log('导入');
  // 这里需要实现文件上传的逻辑
};

const handleExport = () => {
  console.log('导出');
  exportSurroundingWell(queryParams.value).then(response => {
    // 处理文件下载
    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '周边窨井数据.xlsx';
    link.click();
    URL.revokeObjectURL(link.href);
  }).catch(error => {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  });
};

const handleSync = () => {
  ElMessage.info('同步窨井数据功能待实现');
};

const handleEdit = async (row) => {
  dialogMode.value = 'edit';
  // 获取最新详情数据
  const detailData = await fetchManholeDetail(row.id);
  if (detailData) {
    currentManholeData.value = detailData;
    dialogVisible.value = true;
  }
};

const handleDetail = async (row) => {
  dialogMode.value = 'view';
  // 获取最新详情数据
  const detailData = await fetchManholeDetail(row.id);
  if (detailData) {
    currentManholeData.value = detailData;
    dialogVisible.value = true;
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除此窨井信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true;
    deleteSurroundingWell(row.id).then(() => {
      ElMessage.success('删除成功');
      fetchManholeData();
    }).catch(error => {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }).finally(() => {
      loading.value = false;
    });
  }).catch(() => {
    // 取消删除
  });
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.gas-adjacent-manhole-info');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchManholeData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.gas-adjacent-manhole-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 8px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  min-width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0px 4px;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
