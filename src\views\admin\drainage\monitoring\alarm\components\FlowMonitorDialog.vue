<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="flow-monitor-dialog"
  >
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 监测曲线 -->
      <el-tab-pane label="监测曲线" name="curve">
        <div class="curve-container">
          <!-- 时间范围选择 -->
          <div class="time-range-selector">
            <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
              <el-radio-button label="24h">最近24小时</el-radio-button>
              <el-radio-button label="7d">最近7天</el-radio-button>
              <el-radio-button label="30d">最近30天</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 监测指标选择 -->
          <div class="indicator-selector" v-if="monitorIndicators.length > 0">
            <span class="label">监测指标:</span>
            <el-select v-model="selectedIndicator" @change="handleIndicatorChange" placeholder="请选择监测指标">
              <el-option
                v-for="item in monitorIndicators"
                :key="item.monitorIndex"
                :label="`${item.monitorIndexName}(${item.measureUnit})`"
                :value="item.monitorIndex"
              />
            </el-select>
          </div>

          <!-- 图表区域 -->
          <div class="chart-container">
            <div ref="chartRef" class="chart" style="width: 100%; height: 450px;"></div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 运行记录 -->
      <el-tab-pane label="运行记录" name="records">
        <div class="records-container">
          <!-- 时间筛选 -->
          <div class="time-filter">
            <el-date-picker
              v-model="recordTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleRecordTimeChange"
            />
            <el-button type="primary" @click="fetchRecords">查询</el-button>
          </div>

          <!-- 记录类型选择 -->
          <el-tabs v-model="recordType" @tab-change="handleRecordTypeChange">
            <!-- 在线记录 -->
            <el-tab-pane label="历史数据" name="online">
              <el-table :data="onlineRecords" style="width: 100%" height="300" empty-text="暂无数据">
                <el-table-column prop="monitorTime" label="监测时间" width="180" />
                <el-table-column label="监测指标" min-width="200">
                  <template #default="{ row }">
                    {{ formatMonitorValues(row) }}
                  </template>
                </el-table-column>
                <el-table-column label="监测值" min-width="200">
                  <template #default="{ row }">
                    {{ formatMonitorData(row) }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="onlineCurrentPage"
                  v-model:page-size="onlinePageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="onlineTotal"
                  @size-change="handleOnlineSizeChange"
                  @current-change="handleOnlineCurrentChange"
                />
              </div>
            </el-tab-pane>

            <!-- 离线记录 -->
            <el-tab-pane label="离线记录" name="offline">
              <el-table :data="offlineRecords" style="width: 100%" height="300" empty-text="暂无数据">
                <el-table-column prop="deviceName" label="设备名称" min-width="120" />
                <el-table-column label="离线时间" min-width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.offlineTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="恢复时间" min-width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.recoveryTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="offlineDuration" label="离线时长" min-width="100" />
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="offlineCurrentPage"
                  v-model:page-size="offlinePageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="offlineTotal"
                  @size-change="handleOfflineSizeChange"
                  @current-change="handleOfflineCurrentChange"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import moment from 'moment';
import {
  getMonitorIndicators,
  getMonitorCurve,
  getMonitorRecordPage,
  getOfflineRecordsPage
} from '@/api/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return `${props.deviceInfo.deviceName || '设备'} - 监测详情`;
});

// 活动标签页
const activeTab = ref('curve');
const recordType = ref('online');

// 监测指标数据
const monitorIndicators = ref([]);
const selectedIndicator = ref('');

// 时间范围
const timeRange = ref('24h');
const recordTimeRange = ref([]);

// 图表相关
const chartRef = ref(null);
let chartInstance = null;

// 在线记录数据
const onlineRecords = ref([]);
const onlineCurrentPage = ref(1);
const onlinePageSize = ref(10);
const onlineTotal = ref(0);

// 离线记录数据
const offlineRecords = ref([]);
const offlineCurrentPage = ref(1);
const offlinePageSize = ref(10);
const offlineTotal = ref(0);

// 监测指标字段映射
const monitorFieldMap = {
  'monLl': '流量',
  'monLs': '流速',
  'monSw': '水位',
  'monCod': '化学需氧量（COD）',
  'monNd': '浓度',
  'monZd': '浊度',
  'monWd': '温度',
  'monJd': '角度',
  'monYl': '液位',
  'monJgzt': '井盖状态',
  'monSjzt': '水浸状态'
};

// 获取监测指标
const fetchMonitorIndicators = async () => {
  if (!props.deviceInfo.id) return;
  
  try {
    const res = await getMonitorIndicators(props.deviceInfo.id);
    if (res && res.code === 200) {
      monitorIndicators.value = res.data || [];
      if (monitorIndicators.value.length > 0) {
        selectedIndicator.value = monitorIndicators.value[0].monitorIndex;
        fetchCurveData();
      }
    }
  } catch (error) {
    console.error('获取监测指标失败:', error);
    ElMessage.error('获取监测指标失败');
  }
};

// 获取时间范围
const getTimeRange = () => {
  const now = moment();
  let startTime, endTime = now.format('YYYY-MM-DD HH:mm:ss');
  
  switch (timeRange.value) {
    case '24h':
      startTime = now.subtract(24, 'hours').format('YYYY-MM-DD HH:mm:ss');
      break;
    case '7d':
      startTime = now.subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss');
      break;
    case '30d':
      startTime = now.subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss');
      break;
    default:
      startTime = now.subtract(24, 'hours').format('YYYY-MM-DD HH:mm:ss');
  }
  
  return { startTime, endTime };
};

// 获取曲线数据
const fetchCurveData = async () => {
  if (!props.deviceInfo.id || !selectedIndicator.value) return;
  
  try {
    const { startTime, endTime } = getTimeRange();
    const res = await getMonitorCurve({
      deviceId: props.deviceInfo.id,
      startTime,
      endTime
    });
    
    if (res && res.code === 200) {
      renderChart(res.data || []);
    }
  } catch (error) {
    console.error('获取监测曲线数据失败:', error);
    ElMessage.error('获取监测曲线数据失败');
  }
};

// 渲染图表
const renderChart = (data) => {
  if (!chartRef.value) return;
  
  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value);
  }
  
  const currentIndicator = monitorIndicators.value.find(item => item.monitorIndex === selectedIndicator.value);
  if (!currentIndicator) return;
  
  const fieldName = currentIndicator.monitorField;
  const unit = currentIndicator.measureUnit;
  const indicatorName = currentIndicator.monitorIndexName;
  
  // 处理数据
  const xData = [];
  const yData = [];
  
  data.forEach(item => {
    if (item[fieldName] !== null && item[fieldName] !== undefined && item[fieldName] !== -999) {
      xData.push(item.monitorTime);
      
      // 特殊处理井盖状态等固定值
      if (fieldName === 'monJgzt' || fieldName === 'monSjzt') {
        yData.push(Number(item[fieldName]) + 1); // 0变成1，1变成2，便于显示
      } else {
        yData.push(Number(item[fieldName]));
      }
    }
  });
  
  const option = {
    title: {
      text: `${indicatorName}监测曲线`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const param = params[0];
        let value = param.value;
        
        // 井盖状态特殊处理显示
        if (fieldName === 'monJgzt') {
          value = value - 1; // 还原真实值
          return `${param.axisValue}<br/>${indicatorName}: ${value === 0 ? '正常' : '异常'}`;
        } else if (fieldName === 'monSjzt') {
          value = value - 1;
          return `${param.axisValue}<br/>${indicatorName}: ${value === 0 ? '干燥' : '水浸'}`;
        }
        
        return `${param.axisValue}<br/>${indicatorName}: ${value}${unit}`;
      }
    },
    grid: {
      top: 50,
      left: '5%',
      right: '8%',
      bottom: 90,
      containLabel: true
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 20,
        end: 80,
        height: 30,
        bottom: 10,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '60%',
        handleStyle: {
          color: '#0277FD',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        textStyle: {
          color: '#333',
          fontSize: 10
        },
        borderColor: '#ddd',
        fillerColor: 'rgba(2, 119, 253, 0.2)',
        backgroundColor: '#f8f9fa',
        selectedDataBackground: {
          lineStyle: {
            color: '#0277FD'
          },
          areaStyle: {
            color: 'rgba(2, 119, 253, 0.3)'
          }
        },
        labelFormatter: function(value, valueStr) {
          try {
            // value 是数据索引，需要从 xData 中获取对应的时间字符串
            const timeStr = xData[Math.round(value)] || valueStr;
            if (timeRange.value === '24h') {
              return moment(timeStr).format('HH:mm');
            } else {
              return moment(timeStr).format('MM/DD HH:mm');
            }
          } catch (e) {
            return valueStr;
          }
        }
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 0,
        end: 100
      }
    ],
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        formatter: function(value) {
          return moment(value).format('MM-DD HH:mm');
        }
      }
    },
    yAxis: {
      type: 'value',
      name: `${indicatorName}(${unit})`,
      axisLabel: {
        formatter: function(value) {
          if (fieldName === 'monJgzt') {
            const realValue = value - 1;
            return realValue === 0 ? '正常' : '异常';
          } else if (fieldName === 'monSjzt') {
            const realValue = value - 1;
            return realValue === 0 ? '干燥' : '水浸';
          }
          return value;
        }
      }
    },
    series: [{
      name: indicatorName,
      type: 'line',
      data: yData,
      smooth: false,
      lineStyle: {
        color: fieldName === 'monJgzt' && yData.some(v => v > 1) ? '#ff4d4f' : '#1890ff'
      },
      itemStyle: {
        color: fieldName === 'monJgzt' && yData.some(v => v > 1) ? '#ff4d4f' : '#1890ff'
      }
    }]
  };
  
  chartInstance.setOption(option);
};

// 获取记录数据
const fetchRecords = () => {
  if (recordType.value === 'online') {
    fetchOnlineRecords();
  } else {
    fetchOfflineRecords();
  }
};

// 获取在线记录
const fetchOnlineRecords = async () => {
  if (!props.deviceInfo.id) return;
  
  try {
    const params = {
      deviceId: props.deviceInfo.id
    };
    
    if (recordTimeRange.value && recordTimeRange.value.length === 2) {
      params.startTime = recordTimeRange.value[0];
      params.endTime = recordTimeRange.value[1];
    }
    
    const res = await getMonitorRecordPage(onlineCurrentPage.value, onlinePageSize.value, params);
    if (res && res.code === 200) {
      onlineRecords.value = res.data.records || [];
      onlineTotal.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取在线记录失败:', error);
    ElMessage.error('获取在线记录失败');
  }
};

// 获取离线记录
const fetchOfflineRecords = async () => {
  if (!props.deviceInfo.id) return;
  
  try {
    const params = {
      deviceId: props.deviceInfo.id
    };
    
    if (recordTimeRange.value && recordTimeRange.value.length === 2) {
      params.startTime = recordTimeRange.value[0];
      params.endTime = recordTimeRange.value[1];
    }
    
    const res = await getOfflineRecordsPage(offlineCurrentPage.value, offlinePageSize.value, params);
    if (res && res.code === 200) {
      offlineRecords.value = res.data.records || [];
      offlineTotal.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取离线记录失败:', error);
    ElMessage.error('获取离线记录失败');
  }
};

// 格式化监测指标名称
const formatMonitorValues = (row) => {
  const indicators = [];
  Object.keys(monitorFieldMap).forEach(field => {
    if (row[field] !== null && row[field] !== undefined) {
      indicators.push(monitorFieldMap[field]);
    }
  });
  return indicators.join('，');
};

// 格式化监测数据
const formatMonitorData = (row) => {
  const values = [];
  Object.keys(monitorFieldMap).forEach(field => {
    if (row[field] !== null && row[field] !== undefined) {
      let value = row[field];
      if (field === 'monJgzt') {
        value = value === 0 ? '正常' : '异常';
      } else if (field === 'monSjzt') {
        value = value === 0 ? '干燥' : '水浸';
      }
      if(value === null || value === -999){
        value = '无';
      }
      values.push(value);
    }
  });
  return values.join('，');
};

// 格式化时间
const formatTime = (timeObj) => {
  if (!timeObj || !timeObj.time) return '-';
  return moment(timeObj.time).format('YYYY-MM-DD HH:mm:ss');
};

// 事件处理
const handleTabChange = (tab) => {
  if (tab === 'curve' && monitorIndicators.value.length > 0) {
    nextTick(() => {
      if (chartInstance) {
        chartInstance.resize();
      }
    });
  } else if (tab === 'records') {
    fetchRecords();
  }
};

const handleTimeRangeChange = () => {
  fetchCurveData();
};

const handleIndicatorChange = () => {
  fetchCurveData();
};

const handleRecordTimeChange = () => {
  fetchRecords();
};

const handleRecordTypeChange = (type) => {
  if (type === 'online') {
    onlineCurrentPage.value = 1;
    fetchOnlineRecords();
  } else {
    offlineCurrentPage.value = 1;
    fetchOfflineRecords();
  }
};

// 分页事件
const handleOnlineSizeChange = (size) => {
  onlinePageSize.value = size;
  onlineCurrentPage.value = 1;
  fetchOnlineRecords();
};

const handleOnlineCurrentChange = (page) => {
  onlineCurrentPage.value = page;
  fetchOnlineRecords();
};

const handleOfflineSizeChange = (size) => {
  offlinePageSize.value = size;
  offlineCurrentPage.value = 1;
  fetchOfflineRecords();
};

const handleOfflineCurrentChange = (page) => {
  offlineCurrentPage.value = page;
  fetchOfflineRecords();
};

const handleClose = () => {
  dialogVisible.value = false;
  
  // 清理图表
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  
  // 重置数据
  activeTab.value = 'curve';
  recordType.value = 'online';
  selectedIndicator.value = '';
  timeRange.value = '24h';
  recordTimeRange.value = [];
  onlineRecords.value = [];
  offlineRecords.value = [];
  onlineCurrentPage.value = 1;
  offlineCurrentPage.value = 1;
};

// 监听设备变化
watch(() => props.deviceInfo, (newVal) => {
  if (newVal && newVal.id && props.visible) {
    fetchMonitorIndicators();
  }
}, { deep: true });

// 监听对话框显示
watch(() => props.visible, (newVal) => {
  if (newVal && props.deviceInfo.id) {
    fetchMonitorIndicators();
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped>
.flow-monitor-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  height: 600px;
  overflow: hidden;
}

.curve-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.time-range-selector {
  margin-bottom: 16px;
  text-align: center;
}

.indicator-selector {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.indicator-selector .label {
  margin-right: 8px;
  font-weight: 500;
  min-width: 60px;
}

.chart-container {
  flex: 1;
  overflow: hidden;
}

.records-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.time-filter {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

:deep(.el-radio-button__inner) {
  border-radius: 2px;
}

:deep(.el-tabs__header) {
  margin-bottom: 16px;
}

:deep(.el-tabs__content) {
  height: calc(100% - 56px);
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
}
</style>