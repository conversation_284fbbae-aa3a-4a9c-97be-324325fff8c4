<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-sewage-plant-dialog"
    :teleported="true"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="污水厂名称" prop="factoryName" required>
            <el-input v-model="formData.factoryName" placeholder="请输入污水厂名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="污水厂类型" prop="factoryType" required>
            <el-select v-model="formData.factoryType" placeholder="请选择" class="w-full">
              <el-option v-for="item in sewagePlantTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="污水处理级别" prop="handleLevel" required>
            <el-input v-model="formData.handleLevel" placeholder="请输入污水处理级别" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="污水处理设计规模" prop="handleModel" required>
            <div class="input-with-unit">
              <el-input-number 
                v-model="formData.handleModel" 
                :min="0" 
                :precision="2"
                :controls-position="'right'"
                class="w-90" 
                placeholder="请输入污水处理设计规模" 
              />
              <span class="unit no-wrap">m³/天</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName" required>
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="占地面积" prop="floorArea">
            <el-input v-model="formData.floorArea" placeholder="请输入占地面积" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排水去向" prop="drainDirection">
            <el-input v-model="formData.drainDirection" placeholder="请输入排水去向" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="污泥去向" prop="sludgeDirection">
            <el-input v-model="formData.sludgeDirection" placeholder="请输入污泥去向" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="污水处理设施类型" prop="facilityType">
            <el-input v-model="formData.facilityType" placeholder="请输入污水处理设施类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理工艺" prop="handleTechnology">
            <el-input v-model="formData.handleTechnology" placeholder="请输入处理工艺" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设单位" prop="constructionUnitName">
            <el-input v-model="formData.constructionUnitName" placeholder="请输入建设单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运行时间" prop="constructionTime">
            <el-date-picker
              v-model="formData.constructionTime"
              type="date"
              placeholder="请选择运行时间"
              class="w-full"
              :teleported="false"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactUser">
            <el-input v-model="formData.contactUser" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置" prop="address" required>
            <div class="flex items-center">
              <el-cascader
                v-model="areaValues"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-50"
                @change="handleAreaChange"
                :teleported="false"
              />
              <el-input v-model="formData.address" placeholder="请输入详细地址" class="ml-2" style="width: calc(100% - 250px);" />
            </div>
            <div class="error-tip" v-if="addressError">请输入详细地址</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标" required>
            <div class="flex items-center">
              <el-form-item prop="longitude" class="mb-0 coordinate-item">
                <el-input v-model="formData.longitude" placeholder="经度" style="width: 180px;" />
              </el-form-item>
              <el-form-item prop="latitude" class="mb-0 coordinate-item ml-2">
                <el-input v-model="formData.latitude" placeholder="纬度" style="width: 180px;" />
              </el-form-item>
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus" required>
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in usageStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveSewagePlant, updateSewagePlant } from '@/api/drainage';
import { SEWAGE_PLANT_TYPE_OPTIONS, USE_TYPE } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import moment from 'moment';

// 过滤掉选项中的"全部"选项
const sewagePlantTypeOptions = SEWAGE_PLANT_TYPE_OPTIONS.filter(item => item.value !== '');
const usageStatusOptions = USE_TYPE;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增污水厂',
    edit: '编辑污水厂',
    view: '污水厂详情'
  };
  return titles[props.mode] || '污水厂信息';
});

// 行政区划选项和选中值
const areaOptions = ref(AREA_OPTIONS);
const areaValues = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  factoryName: '',
  factoryType: '',
  factoryTypeName: '',
  handleLevel: '',
  handleModel: 0,
  roadName: '',
  floorArea: '',
  drainDirection: '',
  sludgeDirection: '',
  facilityType: '',
  handleTechnology: '',
  constructionUnit: '',
  constructionUnitName: '',
  constructionTime: null,
  contactUser: '',
  contactInfo: '',
  address: '',
  longitude: '',
  latitude: '',
  usageStatus: '',
  usageStatusName: '',
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  factoryName: [{ required: true, message: '请输入污水厂名称', trigger: 'blur' }],
  factoryType: [{ required: true, message: '请选择污水厂类型', trigger: 'change' }],
  handleLevel: [{ required: true, message: '请输入污水处理级别', trigger: 'blur' }],
  handleModel: [{ required: true, message: '请输入污水处理设计规模', trigger: 'blur' }],
  roadName: [{ required: true, message: '请输入所在道路', trigger: 'blur' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
  usageStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }],
  contactInfo: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

// 用于额外的地址验证
const addressError = ref(false);

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  
  // 保留默认值
  formData.county = '371728';
  formData.countyName = '东明县';
  areaValues.value = [];
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 污水厂类型
  const selectedFactoryType = sewagePlantTypeOptions.find(item => item.value === formData.factoryType);
  if (selectedFactoryType) {
    formData.factoryTypeName = selectedFactoryType.label;
  }
  
  // 使用状态
  const selectedUsageStatus = usageStatusOptions.find(item => item.value === formData.usageStatus);
  if (selectedUsageStatus) {
    formData.usageStatusName = selectedUsageStatus.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        // 特殊处理handleModel字段，确保是数字类型
        if (key === 'handleModel') {
          formData[key] = Number(newVal[key]) || 0;
        } else {
          formData[key] = newVal[key];
        }
      }
    });
    
    // 处理日期格式
    if (newVal.constructionTime) {
      if (typeof newVal.constructionTime === 'object' && newVal.constructionTime.time) {
        formData.constructionTime = new Date(newVal.constructionTime.time);
      } else if (typeof newVal.constructionTime === 'string') {
        formData.constructionTime = new Date(newVal.constructionTime);
      }
    }
    
    // 处理area级联选择器
    if (newVal.town) {
      // 确保town是字符串
      const townValue = String(newVal.town);
      // 初始化区域选择
      initAreaValues(townValue);
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.factoryType, (val) => {
  if (val) {
    const selected = sewagePlantTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.factoryTypeName = selected.label;
    }
  }
});

watch(() => formData.usageStatus, (val) => {
  if (val) {
    const selected = usageStatusOptions.find(item => item.value === val);
    if (selected) {
      formData.usageStatusName = selected.label;
    }
  }
});

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  // 额外的地址验证
  addressError.value = !formData.address;
  
  try {
    await formRef.value.validate();
    
    // 如果地址为空，不提交
    if (!formData.address) {
      return;
    }
    
    // 处理污水处理设计规模，确保是数字类型
    if (formData.handleModel && typeof formData.handleModel === 'string') {
      formData.handleModel = Number(formData.handleModel) || 0;
    }
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理日期格式
    if (submitData.constructionTime) {
      submitData.constructionTime = moment(submitData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 设置枚举值对应的名称
    updateNamesByValues();
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveSewagePlant(submitData);
    } else if (props.mode === 'edit') {
      res = await updateSewagePlant(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里添加其他初始化操作
});

// 初始化区域选择器的值
const initAreaValues = (townCode) => {
  if (!townCode) return;
  
  // 递归查找完整的区域路径
  const findPath = (areas, targetCode, currentPath = []) => {
    for (const area of areas) {
      const newPath = [...currentPath, area.code];
      if (area.code === targetCode) {
        return newPath;
      }
      if (area.children && area.children.length > 0) {
        const found = findPath(area.children, targetCode, newPath);
        if (found) return found;
      }
    }
    return null;
  };
  
  const path = findPath(areaOptions.value, townCode);
  if (path) {
    areaValues.value = path;
  } else {
    // 如果无法找到完整路径，退而求其次只设置town值
    areaValues.value = [townCode];
  }
};
</script>

<style scoped>
.drainage-sewage-plant-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-form-item[required] .el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.w-90 {
  width: 90%;
}

.w-50 {
  width: 50%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.input-with-unit {
  display: flex;
  align-items: center;
}

.unit {
  margin-left: 8px;
  color: #606266;
}

.no-wrap {
  white-space: nowrap;
}

.coordinate-item {
  margin-bottom: 0;
}

.mb-0 {
  margin-bottom: 0;
}

.error-tip {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style> 