<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="gas-network-line-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线编码" prop="pipelineCode">
            <el-input v-model="formData.pipelineCode" placeholder="请输入管线编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="压力级别" prop="pressureLevel">
            <el-select v-model="formData.pressureLevel" placeholder="请选择" class="w-full">
              <el-option v-for="item in PRESSURE_LEVELS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋设类型" prop="buriedType">
            <el-select v-model="formData.buriedType" placeholder="请选择" class="w-full">
              <el-option v-for="item in BURIED_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设计压力" prop="designPressure">
            <div class="flex items-center">
              <el-input-number v-model="formData.designPressure" :min="0" :precision="2" />
              <span class="ml-2">Mpa</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="材质" prop="material">
            <el-select v-model="formData.material" placeholder="请选择" class="w-full">
              <el-option v-for="item in PIPE_MATERIALS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管径" prop="pipeDiameter">
            <div class="flex items-center">
              <el-input v-model="formData.pipeDiameter" placeholder="请输入管径" />
              <span class="ml-2">DN</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="线型" prop="pipeType">
            <el-select v-model="formData.pipeType" placeholder="请选择" class="w-full">
              <el-option v-for="item in LINE_TYPES" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="长度" prop="pipeLength">
            <div class="flex items-center">
              <el-input-number v-model="formData.pipeLength" :min="0" :precision="2" />
              <span class="ml-2">KM</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起点埋深" prop="startPointDepth">
            <div class="flex items-center">
              <el-input-number v-model="formData.startPointDepth" :min="0" :precision="2" />
              <span class="ml-2">m</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点埋深" prop="endPointDepth">
            <div class="flex items-center">
              <el-input-number v-model="formData.endPointDepth" :min="0" :precision="2" />
              <span class="ml-2">m</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设时间" prop="constructionTime">
            <el-date-picker
              v-model="formData.constructionTime"
              type="date"
              placeholder="请选择建设时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus">
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in USAGE_STATUS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="流向" prop="flowDirection">
            <el-input v-model="formData.flowDirection" placeholder="请输入流向" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地理位置">
            <div class="flex items-center">
              <div class="flex-1 mr-4">
                <div class="flex items-center">
                  <span class="mr-2 whitespace-nowrap">起点：</span>
                  <el-input v-model="formData.startPointLongitude" placeholder="经度" class="mr-2 w-32" />
                  <el-input v-model="formData.startPointLatitude" placeholder="纬度" class="w-32" />
                  <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker('start')"></el-button>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex items-center">
                  <span class="mr-2 whitespace-nowrap">终点：</span>
                  <el-input v-model="formData.endPointLongitude" placeholder="经度" class="mr-2 w-32" />
                  <el-input v-model="formData.endPointLatitude" placeholder="纬度" class="w-32" />
                  <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker('end')"></el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起点高程" prop="startPointElevation">
            <div class="flex items-center">
              <el-input-number v-model="formData.startPointElevation" :min="0" :precision="2" />
              <span class="ml-2">m</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点高程" prop="endPointElevation">
            <div class="flex items-center">
              <el-input-number v-model="formData.endPointElevation" :min="0" :precision="2" />
              <span class="ml-2">m</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveGasPipeline, updateGasPipeline, getManagementUnits } from '@/api/gas';
import { PRESSURE_LEVELS, BURIED_TYPES, PIPE_MATERIALS, LINE_TYPES, USAGE_STATUS, PRESSURE_LEVEL_MAP, BURIED_TYPE_MAP, MATERIAL_MAP, USAGE_STATUS_MAP, AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增管线',
    edit: '编辑管线',
    view: '管线详情'
  };
  return titles[props.mode] || '管线信息';
});

// 表单数据
const formData = reactive({
  id: '',
  pipelineCode: '',
  pressureLevel: PRESSURE_LEVELS[0]?.value || '',
  pressureLevelName: '',
  buriedType: BURIED_TYPES[0]?.value || '',
  buriedTypeName: '',
  designPressure: 0,
  material: PIPE_MATERIALS[0]?.value || '',
  materialName: '',
  pipeDiameter: '',
  pipeLength: 0,
  roadName: '',
  startPointDepth: 0,
  endPointDepth: 0,
  startPointElevation: 0,
  endPointElevation: 0,
  pipeType: LINE_TYPES[0]?.value || '',
  pipeTypeName: '',
  constructionTime: null,
  managementUnit: '',
  managementUnitName: '',
  usageStatus: 5001,
  usageStatusName: '',
  flowDirection: '',
  startPointLongitude: '',
  startPointLatitude: '',
  endPointLongitude: '',
  endPointLatitude: '',
  startPointDistance: 0,
  endPointDistance: 0,
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  address: '',
  geom: {}
});

// 表单验证规则
const formRules = {
  pipelineCode: [{ required: true, message: '请输入管线编码', trigger: 'blur' }],
  pressureLevel: [{ required: true, message: '请选择压力级别', trigger: 'change' }],
  buriedType: [{ required: true, message: '请选择埋设类型', trigger: 'change' }],
  designPressure: [{ required: true, message: '请输入设计压力', trigger: 'blur' }],
  material: [{ required: true, message: '请选择材质', trigger: 'change' }],
  pipeDiameter: [{ required: true, message: '请输入管径', trigger: 'blur' }],
  pipeLength: [{ required: true, message: '请输入长度', trigger: 'blur' }],
  roadName: [{ required: true, message: '请输入所在道路', trigger: 'blur' }],
  startPointDepth: [{ required: true, message: '请输入起点埋深', trigger: 'blur' }],
  endPointDepth: [{ required: true, message: '请输入终点埋深', trigger: 'blur' }],
  startPointElevation: [{ required: true, message: '请输入起点高程', trigger: 'blur' }],
  endPointElevation: [{ required: true, message: '请输入终点高程', trigger: 'blur' }],
  pipeType: [{ required: true, message: '请选择线型', trigger: 'change' }],
  constructionTime: [{ required: true, message: '请选择建设时间', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  usageStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }]
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 确保pipeType和pipeTypeName正确回显
    if (newVal.pipeType) {
      formData.pipeType = newVal.pipeType;
      formData.pipeTypeName = LINE_TYPES.find(item => item.value === newVal.pipeType)?.label || '';
    }
}}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'usageStatus') {
      formData[key] = 5001;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else if (key === 'constructionTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
};

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 当前选点类型
const currentPointType = ref('');

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    if (currentPointType.value === 'start') {
      formData.startPointLongitude = params.longitude;
      formData.startPointLatitude = params.latitude;
    } else if (currentPointType.value === 'end') {
      formData.endPointLongitude = params.longitude;
      formData.endPointLatitude = params.latitude;
    }
  });
};

// 打开地图选点
const openMapPicker = (type) => {
  currentPointType.value = type;
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 设置枚举值对应的名称
    formData.pressureLevelName = PRESSURE_LEVEL_MAP[formData.pressureLevel] || '';
    formData.buriedTypeName = BURIED_TYPE_MAP[formData.buriedType] || '';
    formData.materialName = MATERIAL_MAP[formData.material] || '';
    formData.usageStatusName = USAGE_STATUS_MAP[formData.usageStatus] || '';
    formData.pipeTypeName = LINE_TYPES.find(item => item.value === formData.pipeType)?.label || '';
    formData.managementUnitName = formData.managementUnit; // 假设权属单位的值就是名称
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveGasPipeline(formData);
    } else if (props.mode === 'edit') {
      res = await updateGasPipeline(formData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.gas-network-line-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mr-4 {
  margin-right: 16px;
}
</style>