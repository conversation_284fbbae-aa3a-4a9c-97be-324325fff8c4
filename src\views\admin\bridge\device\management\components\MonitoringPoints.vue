<template>
  <div class="points-container">
    <!-- 查询条件区域 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="设备名称">
          <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" style="width: 200px" clearable />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="searchForm.deviceType" placeholder="请选择设备类型" style="width: 200px" clearable>
            <el-option v-for="item in deviceTypeOptions" :key="item.deviceType" :label="item.deviceTypeName"
              :value="item.deviceType" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select v-model="searchForm.onlineStatus" placeholder="请选择使用状态" style="width: 200px" clearable>
            <el-option v-for="item in deviceStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchForm.indexCode" placeholder="请输入设备编码搜索" style="width: 250px" clearable>
            <template #append>
              <el-button icon="Search" @click="handleSearch" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="warning" icon="Upload" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table ref="dataTable" :data="tableData" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" v-loading="loading" style="width: 100%"
      @selection-change="handleSelectionChange" @row-click="handleRowClick" :max-height="tableMaxHeight">
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" width="60" align="center">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
      <el-table-column prop="indexCode" label="设备编码" min-width="150" show-overflow-tooltip />
      <el-table-column prop="monitorIndexName" label="监测指标" min-width="140" align="center">
      </el-table-column>
      <el-table-column prop="collectFrequency" label="采集频率" width="100" align="center">
        <template #default="{ row }">
          {{ row.collectFrequency }}分钟/次
        </template>
      </el-table-column>
      <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="monitorObjectName" label="监测对象" width="100" align="center">
      </el-table-column>
      <el-table-column prop="address" label="位置" min-width="150" show-overflow-tooltip />
      <el-table-column prop="onlineStatus" label="使用状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.onlineStatus === 1 ? 'success' : 'danger'" size="small">
            {{ getDeviceStatusName(row.onlineStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleView(row)">详情</el-button>
            <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]" layout="total, prev, pager, next, jumper, sizes" :total="total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <!-- 弹窗组件 -->
    <PipelineDialog v-model="dialogVisible" :mode="dialogMode" :id="currentId" @success="handleDialogSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElTable, ElTableColumn, ElPagination, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElTag } from 'element-plus'
import {
  DEVICE_STATUS_OPTIONS,
  MONITOR_INDEX_MAP,
  DEVICE_STATUS_MAP,
  MONITOR_OBJECT_MAP
} from '@/constants/bridge'
import {
  getPipelineInfoPage,
  deletePipelineInfo,
  getDeviceType
} from '@/api/bridge'
import PipelineDialog from './PipelineDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const dialogVisible = ref(false)
const dialogMode = ref('add')
const currentId = ref(null)

// 搜索表单
const searchForm = reactive({
  deviceName: '',
  deviceType: '',
  onlineStatus: '',
  indexCode: ''
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 下拉选项
const deviceTypeOptions = ref([])
const getDeviceTypeList = async () => {
  const response = await getDeviceType(0) // 0 表示查所有类型 
  if (response.code === 200) {
    deviceTypeOptions.value = response.data
  }
}
getDeviceTypeList()

const deviceStatusOptions = DEVICE_STATUS_OPTIONS

// 获取监测指标名称
const getMonitorIndexName = (value) => {
  return MONITOR_INDEX_MAP[value] || value
}

// 获取设备状态名称
const getDeviceStatusName = (value) => {
  return DEVICE_STATUS_MAP[value] || value
}

// 获取监测对象名称
const getMonitorObjectName = (value) => {
  return MONITOR_OBJECT_MAP[value] || value
}

// 表头样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row)
}

// 加载列表数据
const loadData = async () => {
  try {
    loading.value = true

    const params = {
      deviceName: searchForm.deviceName,
      deviceType: searchForm.deviceType,
      onlineStatus: searchForm.onlineStatus ? parseInt(searchForm.onlineStatus) : undefined,
      // 其他搜索参数
      indexCode: searchForm.indexCode,
    }

    const response = await getPipelineInfoPage(currentPage.value, pageSize.value, params)

    if (response.code === 200) {
      const data = response.data
      tableData.value = data.records || []
      total.value = data.total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  loadData()
}

// 新增
const handleAdd = () => {
  dialogMode.value = 'add'
  currentId.value = null
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogMode.value = 'edit'
  currentId.value = row.id
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  dialogMode.value = 'view'
  currentId.value = row.id
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备"${row.deviceName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deletePipelineInfo(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 定位
const handleLocation = (row) => {
  ElMessage.info('定位功能开发中...')
}

// 导入
const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (current) => {
  currentPage.value = current
  loadData()
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  loadData()
}

const tableMaxHeight = ref(500)

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight
    const container = document.querySelector('.points-container')
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100)
      return
    }
    const containerRect = container.getBoundingClientRect()
    const containerTop = containerRect.top
    const searchSection = container.querySelector('.search-section')
    const searchHeight = searchSection ? searchSection.offsetHeight : 60
    const headerSection = container.querySelector('.table-header')
    const headerHeight = headerSection ? headerSection.offsetHeight : 60
    const paginationReservedHeight = 80
    const bottomReserved = 30
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved
    const minHeight = 300
    const absoluteMaxHeight = 600
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight)
    tableMaxHeight.value = maxHeight
  })
}

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer)
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight()
  }, 100)
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  setTimeout(() => {
    calculateTableMaxHeight()
  }, 100)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (handleResize.timer) {
    clearTimeout(handleResize.timer)
  }
})
</script>

<style scoped>
.points-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 16px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>