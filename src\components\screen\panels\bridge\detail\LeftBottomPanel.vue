<template>
  <PanelBox title="视频监控">
    <div class="panel-content">
      <!-- 单路视频播放 -->
      <div class="video-container">
        <div class="video-item">
          <div class="video-title">{{ videoInfo.title }}</div>
          <VideoPlayer 
            :src="videoInfo.src" 
            :muted="true" 
            :autoplay="true"
            :showControls="true"
            ref="videoPlayerRef"
            class="video-player"
            @error="handlePlayerError"
          />
          <!-- 错误提示 -->
          <div v-if="videoInfo.error" class="video-error">
            {{ videoInfo.error }}
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue'

// 定义props
const props = defineProps({
  bridgeId: {
    type: [String, Number],
    required: true
  },
  bridgeName: {
    type: String,
    default: '未知桥梁'
  }
})

// 播放器引用
const videoPlayerRef = ref(null)

// 视频信息数据
const videoInfo = ref({
  title: 'B_南华路南护城河桥北(西侧)第一立杆_V_1',
  src: 'http://**************/video/rtp/34020000002001300023_41010500001320000024/hls.m3u8?originTypeStr=rtp_push',
  error: null
})

// 处理播放器错误
const handlePlayerError = (error) => {
  console.error(`🔍 [视频监控] 播放器发生错误:`, error)
  videoInfo.value.error = error.message || '播放失败'
  
  // 10秒后自动清除错误，尝试重新连接
  setTimeout(() => {
    console.log('🔍 [视频监控] 播放器自动重试连接')
    videoInfo.value.error = null
    if (videoPlayerRef.value) {
      videoPlayerRef.value.play()
    }
  }, 10000)
}

// 组件挂载时初始化
onMounted(() => {
  console.log('🔍 [视频监控] 桥梁详情视频监控面板初始化完成')
  console.log('🔍 [视频监控] 桥梁ID:', props.bridgeId, '桥梁名称:', props.bridgeName)
})

// 组件卸载时处理
onUnmounted(() => {
  console.log('🔍 [视频监控] 销毁播放器')
  if (videoPlayerRef.value && videoPlayerRef.value.destroy) {
    videoPlayerRef.value.destroy()
  }
})
</script>

<style scoped>
.panel-content {
  padding: 15px 15px 10px 15px;
  display: flex;
  flex-direction: column;
}

.video-container {
  display: flex;
  justify-content: center;
  align-items: stretch;
  height: 90%;
}

.video-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 242, 241, 0.2);
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100% !important;
  height: 32px;
  background: #000000;
  opacity: 0.4;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: #FF6B6B;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 20;
  text-align: center;
  max-width: 80%;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px 15px 10px 15px;
  }
  
  .video-title {
    height: 32px;
    font-size: 12px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px 12px 10px 12px;
  }
  
  .video-title {
    height: 28px;
    font-size: 11px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px 18px 10px 18px;
  }
  
  .video-title {
    height: 36px;
    font-size: 14px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px 10px 10px 10px;
  }
  
  .video-title {
    height: 26px;
    font-size: 10px;
  }
}

/* 针对910px-940px高度范围，确保视频组件充满高度 */
@media screen and (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 8px 8px 10px 8px;
  }
  
  .video-container {
    height: 210px; /* 减去面板标题和其他元素的高度 */
  }
  
  .video-title {
    height: 24px;
    font-size: 10px;
  }
}

/* 针对小于910px的高度，进一步优化 */
@media screen and (max-height: 909px) {
  .panel-content {
    padding: 5px 5px 10px 5px;
  }
  
  .video-container {
    height: calc(100vh - 180px); /* 更小的高度偏移 */
  }
  
  .video-title {
    height: 20px;
    font-size: 9px;
  }
}
</style> 