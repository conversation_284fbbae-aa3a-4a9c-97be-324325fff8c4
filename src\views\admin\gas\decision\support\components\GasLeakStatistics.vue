<template>
  <div class="gas-leak-statistics">
    <h3 class="component-title">燃气泄漏报警统计分析</h3>
    
    <!-- 日期选择区域 -->
    <div class="date-filter">
      <div class="date-range">
        <span class="label">日期:</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :size="'default'"
          :disabled="false"
          format="YYYY/MM/DD"
          value-format="YYYY/MM/DD"
          @change="handleDateChange"
        />
      </div>
      <div class="quick-dates">
        <el-button 
          :type="activeDateBtn === 'recent7' ? 'primary' : ''" 
          @click="setDateRange('recent7')"
        >
          近7日
        </el-button>
        <el-button 
          :type="activeDateBtn === 'recent30' ? 'primary' : ''" 
          @click="setDateRange('recent30')"
        >
          最近30天
        </el-button>
      </div>
    </div>
    
    <!-- 内容区域包裹器 -->
    <div class="content-wrapper">
      <!-- 顶部统计卡片区域 -->
      <div class="statistics-cards">
        <div class="stat-card total-card">
          <div class="card-content">
            <div class="stat-title">全部报警</div>
            <div class="stat-value">{{ statData.totalAlarms }}</div>
            <div class="stat-trends">
              <span class="trend-item">
                <span class="trend-label">同比</span>
                <span class="trend-value" :class="statData.yoyAnalysisTrend === 'up' ? 'up' : 'down'">
                  <el-icon v-if="statData.yoyAnalysisTrend === 'up'"><CaretTop /></el-icon>
                  <el-icon v-else><CaretBottom /></el-icon>
                  {{ statData.yoyAnalysis }}
                </span>
              </span>
              <span class="trend-item">
                <span class="trend-label">环比</span>
                <span class="trend-value" :class="statData.momAnalysisTrend === 'up' ? 'up' : 'down'">
                  <el-icon v-if="statData.momAnalysisTrend === 'up'"><CaretTop /></el-icon>
                  <el-icon v-else><CaretBottom /></el-icon>
                  {{ statData.momAnalysis }}
                </span>
              </span>
            </div>
          </div>
        </div>
        
        <div class="stat-card blue-card">
          <div class="card-content">
            <div class="stat-title">待确认</div>
            <div class="stat-value">{{ statData.pendingConfirm }}</div>
            <div class="stat-trends">
              <span class="trend-value">{{ statData.pendingConfirmRate }}</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card red-card">
          <div class="card-content">
            <div class="stat-title">待处置</div>
            <div class="stat-value">{{ statData.pendingHandle }}</div>
            <div class="stat-trends">
              <span class="trend-value">{{ statData.pendingHandleRate }}</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card yellow-card">
          <div class="card-content">
            <div class="stat-title">处置中</div>
            <div class="stat-value">{{ statData.handling }}</div>
            <div class="stat-trends">
              <span class="trend-value">{{ statData.handlingRate }}</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card gray-card">
          <div class="card-content">
            <div class="stat-title">已处置</div>
            <div class="stat-value">{{ statData.handled }}</div>
            <div class="stat-trends">
              <span class="trend-value">{{ statData.handledRate }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 中部图表区域 -->
      <div class="chart-section">
        <!-- 左侧内容：统计图表区域 -->
        <div class="chart-area">
          <!-- 报警趋势图 -->
          <div class="trend-chart box-container">
            <h4 class="section-title">报警趋势</h4>
            <div class="chart-container" ref="trendChartRef"></div>
          </div>
          
          <!-- 报警等级图 -->
          <div class="level-chart box-container">
            <h4 class="section-title">报警等级</h4>
            <div class="chart-container" ref="levelChartRef"></div>
          </div>
        </div>
        
        <!-- 右侧内容：圆环图和统计 -->
        <div class="stats-area">
          <div class="circular-charts box-container">
            <!-- 处置完成率 -->
            <div class="chart-box">
              <div class="chart-container" ref="completeChartRef"></div>
              <div class="chart-info">
                <div class="chart-title">处置完成率</div>
                <div class="chart-percent complete-percent">{{ completionRate }}%</div>
                <div class="chart-compare">
                  环比 
                  {{ completionCompare > 0 ? '上升' : '下降' }}
                  {{ Math.abs(completionCompare) }}%
                </div>
              </div>
            </div>
            
            <!-- 误报率 -->
            <div class="chart-box">
              <div class="chart-container" ref="errorChartRef"></div>
              <div class="chart-info">
                <div class="chart-title">误报率</div>
                <div class="chart-percent error-percent">{{ errorRate }}%</div>
                <div class="chart-compare">
                  环比 
                  {{ errorCompare > 0 ? '上升' : '下降' }}
                  {{ Math.abs(errorCompare) }}%
                </div>
              </div>
            </div>
            
            <!-- 平均处置时长 -->
            <div class="time-stat">
              <div class="time-value">
                {{ avgHandlingTime.hours }}<span class="time-unit">小时</span>
                {{ avgHandlingTime.minutes }}<span class="time-unit">分钟</span>
              </div>
              <div class="time-label">平均处置时长</div>
              <div class="time-compare">
                环比 
                {{ avgHandlingCompare > 0 ? '上升' : '下降' }}
                {{ Math.abs(avgHandlingCompare) }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部表格区域 -->
      <div class="tables-section">
        <!-- 高发报警设备表格 -->
        <div class="table-container box-container">
          <h4 class="section-title">高发报警设备（top10）</h4>
          <el-table
            :data="deviceTableData"
            style="width: 100%"
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName"
            border
          >
            <el-table-column prop="rank" label="排序" width="80" align="center">
              <template #default="scope">
                <div :class="['rank-cell', scope.row.rank <= 3 ? 'top-rank' : '']">
                  {{ scope.row.rank }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="设备名称" min-width="160" />
            <el-table-column prop="total" label="报警总数" width="110" align="center" />
            <el-table-column prop="completedPercent" label="已处置" width="120" align="center">
              <template #default="scope">
                {{ scope.row.completed }} ({{ scope.row.completedPercent }}%)
              </template>
            </el-table-column>
            <el-table-column label="一级报警" align="center">
              <el-table-column prop="level1" label="报警" width="80" align="center" />
              <el-table-column prop="level1Completed" label="已处置" width="80" align="center">
                <template #default="scope">
                  {{ scope.row.level1Completed }} ({{ scope.row.level1CompletedPercent }}%)
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二级报警" align="center">
              <el-table-column prop="level2" label="报警" width="80" align="center" />
              <el-table-column prop="level2Completed" label="已处置" width="80" align="center">
                <template #default="scope">
                  {{ scope.row.level2Completed }} ({{ scope.row.level2CompletedPercent }}%)
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="三级报警" align="center">
              <el-table-column prop="level3" label="报警" width="80" align="center" />
              <el-table-column prop="level3Completed" label="已处置" width="80" align="center">
                <template #default="scope">
                  {{ scope.row.level3Completed }} ({{ scope.row.level3CompletedPercent }}%)
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 企业报警表格 -->
        <div class="table-container box-container">
          <h4 class="section-title">企业报警</h4>
          <el-table
            :data="companyTableData"
            style="width: 100%"
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName"
            border
          >
            <el-table-column prop="rank" label="排序" width="80" align="center">
              <template #default="scope">
                <div :class="['rank-cell', scope.row.rank <= 3 ? 'top-rank' : '']">
                  {{ scope.row.rank }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="企业名称" min-width="160" />
            <el-table-column prop="total" label="报警总数" width="110" align="center" />
            <el-table-column prop="completedPercent" label="已处置" width="120" align="center">
              <template #default="scope">
                {{ scope.row.completed }} ({{ scope.row.completedPercent }}%)
              </template>
            </el-table-column>
            <el-table-column label="一级报警" align="center">
              <el-table-column prop="level1" label="报警" width="80" align="center" />
              <el-table-column prop="level1Completed" label="已处置" width="80" align="center">
                <template #default="scope">
                  {{ scope.row.level1Completed }} ({{ scope.row.level1CompletedPercent }}%)
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二级报警" align="center">
              <el-table-column prop="level2" label="报警" width="80" align="center" />
              <el-table-column prop="level2Completed" label="已处置" width="80" align="center">
                <template #default="scope">
                  {{ scope.row.level2Completed }} ({{ scope.row.level2CompletedPercent }}%)
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="三级报警" align="center">
              <el-table-column prop="level3" label="报警" width="80" align="center" />
              <el-table-column prop="level3Completed" label="已处置" width="80" align="center">
                <template #default="scope">
                  {{ scope.row.level3Completed }} ({{ scope.row.level3CompletedPercent }}%)
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import { CaretTop, CaretBottom } from '@element-plus/icons-vue';
import moment from 'moment';
import { 
  getAlarmAnalysisStatistics, 
  getAlarmAnalysisDisposalSituation, 
  getAlarmAnalysisTrendStatistics, 
  getAlarmAnalysisLevelStatistics, 
  getAlarmAnalysisDeviceHighFrequency, 
  getAlarmAnalysisEnterpriseStatistics 
} from '@/api/gas';

// 日期范围 - 默认最近3天
const getDefaultDateRange = () => {
  const endDate = moment();
  const startDate = moment().subtract(3, 'days');
  return [
    startDate.format('YYYY/MM/DD'),
    endDate.format('YYYY/MM/DD')
  ];
};

const dateRange = ref(getDefaultDateRange());
const activeDateBtn = ref('');

// 图表引用
const trendChartRef = ref(null);
const levelChartRef = ref(null);
const completeChartRef = ref(null);
const errorChartRef = ref(null);

// 图表实例
let trendChart = null;
let levelChart = null;
let completeChart = null;
let errorChart = null;

// 统计数据
const statData = ref({});

// 环形图数据
const completionRate = ref(0);
const errorRate = ref(0);
const completionCompare = ref(0);
const errorCompare = ref(0);
const avgHandlingTime = ref({ hours: 0, minutes: 0 });
const avgHandlingCompare = ref(0);

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '48px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 设备表格数据
const deviceTableData = ref([
  {
    rank: 1,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 2,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 3,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 4,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 5,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 6,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 7,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 8,
    name: 'xxx设备',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  }
]);

// 企业表格数据
const companyTableData = ref([
  {
    rank: 1,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 2,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 3,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 4,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 5,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 6,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 7,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  },
  {
    rank: 8,
    name: 'xxx企业',
    total: 10,
    completed: 10,
    completedPercent: 90,
    level1: 10,
    level1Completed: 10,
    level1CompletedPercent: 90,
    level2: 10,
    level2Completed: 10,
    level2CompletedPercent: 90,
    level3: 10,
    level3Completed: 10,
    level3CompletedPercent: 90
  }
]);

// 日期选择事件处理
const handleDateChange = () => {
  // 手动选择日期时，清除快捷按钮的激活状态
  activeDateBtn.value = '';
  
  // 刷新数据
  refreshData();
};

// 设置日期范围
const setDateRange = (type) => {
  activeDateBtn.value = type;
  
  if (type === 'recent7') {
    const endDate = moment();
    const startDate = moment().subtract(7, 'days');
    dateRange.value = [
      startDate.format('YYYY/MM/DD'),
      endDate.format('YYYY/MM/DD')
    ];
  } else if (type === 'recent30') {
    const endDate = moment();
    const startDate = moment().subtract(30, 'days');
    dateRange.value = [
      startDate.format('YYYY/MM/DD'),
      endDate.format('YYYY/MM/DD')
    ];
  }
  
  // 刷新数据
  refreshData();
};

// 准备API请求参数
const prepareRequestParams = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return { startDate: '', endDate: '' };
  }
  
  return {
    startDate: moment(dateRange.value[0]).format('YYYY-MM-DD HH:mm:ss'),
    endDate: moment(dateRange.value[1]).format('YYYY-MM-DD HH:mm:ss')
  };
};

// 获取统计卡片数据
const fetchStatisticsData = async () => {
  try {
    const params = prepareRequestParams();
    const res = await getAlarmAnalysisStatistics(params);
    
    if (res.code === 200 && res.data) {
      statData.value = res.data;
    }
  } catch (error) {
    console.error('获取报警统计数据失败:', error);
  }
};

// 获取处置情况数据
const fetchDisposalSituationData = async () => {
  try {
    const params = prepareRequestParams();
    const res = await getAlarmAnalysisDisposalSituation(params);
    
    if (res.code === 200 && res.data) {
      const data = res.data;
      
      // 更新处置完成率
      completionRate.value = parseFloat(data.completionRate || 0);
      completionCompare.value = parseFloat(data.completionAnalysis || 0);
      
      // 更新误报率
      errorRate.value = parseFloat(data.falseAlarmRate || 0);
      errorCompare.value = parseFloat(data.falseAlarmAnalysis || 0);
      
      // 处理平均处置时长
      if (data.avgHandlingDuration) {
        const avgDuration = data.avgHandlingDuration.split('小时');
        if (avgDuration.length >= 2) {
          avgHandlingTime.value = {
            hours: avgDuration[0] || 0,
            minutes: avgDuration[1].split('分钟')[0] || 0
          };
        }
      }
      avgHandlingCompare.value = parseFloat(data.avgHandlingDurationAnalysis || 0);
    }
  } catch (error) {
    console.error('获取处置情况数据失败:', error);
  }
};

// 获取报警趋势数据
const fetchTrendData = async () => {
  try {
    const params = prepareRequestParams();
    const res = await getAlarmAnalysisTrendStatistics(params);
    
    if (res.code === 200 && res.data && res.data.statistics) {
      const trendData = res.data.statistics;
      const dates = [];
      const values = [];
      
      trendData.forEach(item => {
        // 格式化日期显示
        const dateStr = moment(item.date).format('MM/DD日');
        dates.push(dateStr);
        values.push(item.totalCount);
      });
      
      // 更新趋势图
      updateTrendChart(dates, values);
    }
  } catch (error) {
    console.error('获取报警趋势数据失败:', error);
  }
};

// 获取报警等级数据
const fetchLevelData = async () => {
  try {
    const params = prepareRequestParams();
    const res = await getAlarmAnalysisLevelStatistics(params);
    
    if (res.code === 200 && res.data && res.data.statistics) {
      const levelData = res.data.statistics;
      const levels = [];
      const totalCounts = [];
      const handledCounts = [];
      const percents = [];
      
      levelData.forEach(item => {
        levels.push(item.alarmLevelName + '报警');
        totalCounts.push(item.totalCount);
        handledCounts.push(item.handledCount);
        percents.push(parseFloat(item.percent));
      });
      
      // 更新等级图
      updateLevelChart(levels, totalCounts, handledCounts, percents);
    }
  } catch (error) {
    console.error('获取报警等级数据失败:', error);
  }
};

// 获取高发报警设备数据
const fetchHighFrequencyDeviceData = async () => {
  try {
    const params = {
      ...prepareRequestParams(),
      pageNum: 1,
      pageSize: 10
    };
    
    const res = await getAlarmAnalysisDeviceHighFrequency(params);
    
    if (res.code === 200 && res.data && res.data.records) {
      const records = res.data.records;
      const deviceData = records.map((item, index) => {
        return {
          rank: index + 1,
          name: item.deviceName || `设备${index + 1}`,
          total: item.alarmCount || 0,
          completed: item.handledCount || 0,
          completedPercent: item.handleRate || 0,
          level1: item.level1Count || 0,
          level1Completed: item.level1HandledCount || 0,
          level1CompletedPercent: item.level1HandleRate || 0,
          level2: item.level2Count || 0,
          level2Completed: item.level2HandledCount || 0,
          level2CompletedPercent: item.level2HandleRate || 0,
          level3: item.level3Count || 0,
          level3Completed: item.level3HandledCount || 0,
          level3CompletedPercent: item.level3HandleRate || 0
        };
      });
      
      deviceTableData.value = deviceData;
    }
  } catch (error) {
    console.error('获取高发报警设备数据失败:', error);
  }
};

// 获取企业报警数据
const fetchEnterpriseData = async () => {
  try {
    const params = {
      ...prepareRequestParams(),
      pageNum: 1,
      pageSize: 10
    };
    
    const res = await getAlarmAnalysisEnterpriseStatistics(params);
    
    if (res.code === 200 && res.data && res.data.records) {
      const records = res.data.records;
      const enterpriseData = records.map((item, index) => {
        return {
          rank: index + 1,
          name: item.enterpriseName || `企业${index + 1}`,
          total: item.alarmCount || 0,
          completed: item.handledCount || 0,
          completedPercent: item.handleRate || 0,
          level1: item.level1Count || 0,
          level1Completed: item.level1HandledCount || 0,
          level1CompletedPercent: item.level1HandleRate || 0,
          level2: item.level2Count || 0,
          level2Completed: item.level2HandledCount || 0,
          level2CompletedPercent: item.level2HandleRate || 0,
          level3: item.level3Count || 0,
          level3Completed: item.level3HandledCount || 0,
          level3CompletedPercent: item.level3HandleRate || 0
        };
      });
      
      companyTableData.value = enterpriseData;
    }
  } catch (error) {
    console.error('获取企业报警数据失败:', error);
  }
};

// 刷新图表和数据
const refreshData = () => {
  console.log('正在刷新数据，日期范围:', dateRange.value);
  
  // 显示加载状态
  if (trendChart) trendChart.showLoading();
  if (levelChart) levelChart.showLoading();
  if (completeChart) completeChart.showLoading();
  if (errorChart) errorChart.showLoading();
  
  // 并行获取所有数据
  Promise.all([
    fetchStatisticsData(),
    fetchDisposalSituationData(),
    fetchTrendData(),
    fetchLevelData(),
    fetchHighFrequencyDeviceData(),
    fetchEnterpriseData()
  ]).finally(() => {
    // 隐藏加载状态
    nextTick(() => {
      if (trendChart) trendChart.hideLoading();
      if (levelChart) levelChart.hideLoading();
      if (completeChart) completeChart.hideLoading();
      if (errorChart) errorChart.hideLoading();
    });
  });
};

// 监听完成率和误报率的变化，更新图表
watch([completionRate, errorRate], () => {
  nextTick(() => {
    updateCompleteChart();
    updateErrorChart();
  });
});

// 更新趋势图表数据
const updateTrendChart = (dates, values) => {
  if (!trendChart) return;
  
  trendChart.setOption({
    xAxis: {
      data: dates
    },
    series: [{
      data: values
    }]
  });
};

// 更新等级图表数据
const updateLevelChart = (levels, totalCounts, handledCounts, percents) => {
  if (!levelChart) return;
  
  levelChart.setOption({
    xAxis: {
      data: levels
    },
    series: [
      {
        name: '总数',
        data: totalCounts
      },
      {
        name: '已处置',
        data: handledCounts
      },
      {
        name: '占比',
        data: percents
      }
    ]
  });
};

// 更新处置完成率环形图
const updateCompleteChart = () => {
  if (!completeChart) return;
  
  // 更新图表数据
  completeChart.setOption({
    series: [{
      data: [
        { value: completionRate.value, name: '已完成', itemStyle: { color: '#0086FF' } },
        { value: 100 - completionRate.value, name: '未完成', itemStyle: { color: '#E1E1E1' } }
      ]
    }]
  });
};

// 更新误报率环形图
const updateErrorChart = () => {
  if (!errorChart) return;
  
  // 更新图表数据
  errorChart.setOption({
    series: [{
      data: [
        { value: errorRate.value, name: '误报', itemStyle: { color: '#0086FF' } },
        { value: 100 - errorRate.value, name: '非误报', itemStyle: { color: '#E1E1E1' } }
      ]
    }]
  });
};

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return;
  
  trendChart = echarts.init(trendChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
      axisLine: {
        lineStyle: {
          color: '#E0E0E0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E0E0E0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '报警数量',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: '#409EFF'
        },
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(64, 158, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(64, 158, 255, 0.1)'
            }]
          }
        }
      }
    ]
  };
  
  trendChart.setOption(option);
};

// 初始化等级图表
const initLevelChart = () => {
  if (!levelChartRef.value) return;
  
  levelChart = echarts.init(levelChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['总数', '已处置', '占比'],
      right: 10,
      top: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['一级报警', '二级报警', '三级报警'],
      axisLine: {
        lineStyle: {
          color: '#E0E0E0'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '单位/个',
        min: 0,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E0E0E0',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '占比',
        min: 0,
        max: 100,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '总数',
        type: 'bar',
        barWidth: 30,
        data: [],
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '已处置',
        type: 'bar',
        barWidth: 30,
        data: [],
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '占比',
        type: 'line',
        yAxisIndex: 1,
        data: [],
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 2,
          color: '#E6A23C'
        },
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  };
  
  levelChart.setOption(option);
};

// 初始化处置完成率环形图
const initCompleteChart = () => {
  if (!completeChartRef.value) return;
  
  completeChart = echarts.init(completeChartRef.value);
  
  const option = {
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          { value: completionRate.value, name: '已完成', itemStyle: { color: '#0086FF' } },
          { value: 100 - completionRate.value, name: '未完成', itemStyle: { color: '#E1E1E1' } }
        ]
      }
    ]
  };
  
  completeChart.setOption(option);
};

// 初始化误报率环形图
const initErrorChart = () => {
  if (!errorChartRef.value) return;
  
  errorChart = echarts.init(errorChartRef.value);
  
  const option = {
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          { value: errorRate.value, name: '误报', itemStyle: { color: '#0086FF' } },
          { value: 100 - errorRate.value, name: '非误报', itemStyle: { color: '#E1E1E1' } }
        ]
      }
    ]
  };
  
  errorChart.setOption(option);
};

// 初始化所有图表
const initCharts = () => {
  nextTick(() => {
    initTrendChart();
    initLevelChart();
    initCompleteChart();
    initErrorChart();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
    
    // 初始化后立即刷新一次数据
    refreshData();
  });
};

// 组件挂载后初始化
onMounted(() => {
  initCharts();
  
  // 添加滚动监听
  document.querySelector('.gas-leak-statistics')?.addEventListener('scroll', handleScroll);
});

// 组件销毁前清理事件
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  
  // 移除滚动监听
  const container = document.querySelector('.gas-leak-statistics');
  if (container) {
    container.removeEventListener('scroll', handleScroll);
  }
  
  // 销毁图表实例
  trendChart && trendChart.dispose();
  levelChart && levelChart.dispose();
  completeChart && completeChart.dispose();
  errorChart && errorChart.dispose();
});

// 窗口大小调整处理函数
const handleResize = () => {
  trendChart && trendChart.resize();
  levelChart && levelChart.resize();
  completeChart && completeChart.resize();
  errorChart && errorChart.resize();
};

// 处理页面滚动
const handleScroll = () => {
  // 当页面滚动时可以做一些操作，比如懒加载等
};
</script>

<style scoped>
.gas-leak-statistics {
  width: 100%;
  height: calc(100vh - 300px); /* 减去可能的顶部导航等高度 */
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  overflow-y: auto; /* 使整个组件可滚动 */
  position: relative; /* 为相对定位的内容提供参考 */
}

.component-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #282828;
  margin-bottom: 16px;
  position: sticky; /* 标题固定在顶部 */
  top: 0;
  background: white;
  z-index: 10;
  padding: 10px 0;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 保持日期筛选区域在页面顶部可见 */
.date-filter {
  position: sticky;
  top: 50px; /* 标题下方固定 */
  background: white;
  z-index: 9;
  padding: 10px 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.date-range {
  display: flex;
  align-items: center;
}

.label {
  margin-right: 8px;
  font-size: 14px;
  color: #282828;
}

.quick-dates {
  display: flex;
  gap: 10px;
}

:deep(.el-button) {
  height: 32px;
  padding: 8px 15px;
  font-size: 14px;
}

:deep(.el-date-editor) {
  width: 240px;
}

:deep(.el-date-editor .el-range__icon) {
  color: #0086FF;
}

:deep(.el-date-editor .el-range-separator) {
  color: #282828;
}

:deep(.el-date-editor .el-range-input) {
  color: #282828;
}

/* 统计卡片样式 */
.statistics-cards {
  display: flex;
  width: 100%;
  margin-bottom: 20px;
  gap: 16px;
}

.stat-card {
  flex: 1;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.total-card {
  background-color: #FFFFFF;
  border: 1px solid #EBEEF5;
}

.blue-card {
  background-color: #3498DB;
  color: white;
}

.red-card {
  background-color: #F56C6C;
  color: white;
}

.yellow-card {
  background-color: #F39C12;
  color: white;
}

.gray-card {
  background-color: #909399;
  color: white;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-title {
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-trends {
  display: flex;
  font-size: 12px;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-label {
  color: #909399;
}

.trend-value {
  display: flex;
  align-items: center;
}

.trend-value.up {
  color: #67C23A;
}

.trend-value.down {
  color: #F56C6C;
}

/* 图表区域样式 */
.chart-section {
  display: flex;
  margin-bottom: 20px;
  gap: 16px;
}

.chart-area {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stats-area {
  flex: 1;
}

.box-container {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.section-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #282828;
  margin: 0 0 16px 0;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.trend-chart, .level-chart {
  flex: 1;
}

.circular-charts {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.chart-box .chart-container {
  width: 120px;
  height: 120px;
}

.chart-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
}

.chart-title {
  font-size: 14px;
  color: #282828;
  margin-bottom: 5px;
}

.chart-percent {
  font-size: 20px;
  font-weight: bold;
  color: #282828;
  margin-bottom: 5px;
}

.chart-compare, .time-compare {
  font-size: 12px;
  color: #909399;
}

.time-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.time-value {
  font-size: 24px;
  font-weight: bold;
  color: #0086FF;
  margin-bottom: 5px;
}

.time-unit {
  font-size: 14px;
  margin: 0 2px;
}

.time-label {
  font-size: 14px;
  color: #282828;
  margin-bottom: 5px;
}

/* 表格区域样式 */
.tables-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-container {
  margin-bottom: 20px;
  overflow-x: auto; /* 表格可横向滚动 */
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important; /* 确保表格内容可滚动 */
  max-height: 500px; /* 限制表格高度 */
}

:deep(.el-table) {
  overflow-x: auto !important;
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-table th) {
  background-color: #F4F4F4 !important;
  height: 48px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-button--primary) {
  background-color: #0086FF;
  border-color: #0086FF;
}

:deep(.el-button--primary:hover) {
  background-color: #409EFF;
  border-color: #409EFF;
}

:deep(.el-button--primary:focus) {
  background-color: #0086FF;
  border-color: #0086FF;
}

.rank-cell {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #F2F6FC;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-weight: bold;
}

.rank-cell.top-rank {
  background-color: #0086FF;
  color: white;
}

/* Responsive styles */
@media (max-width: 1366px) {
  .chart-section {
    flex-direction: column;
  }
  
  .stats-area {
    display: flex;
    gap: 16px;
  }
  
  .circular-charts {
    flex: 1;
    flex-direction: row;
    justify-content: space-around;
  }
  
  .chart-box {
    margin-bottom: 0;
  }
}

@media (max-height: 900px) {
  .gas-leak-statistics {
    height: auto;
    min-height: 100vh;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .chart-box .chart-container {
    width: 100px;
    height: 100px;
  }
  
  :deep(.el-table__body-wrapper) {
    max-height: 300px;
  }
}

/* 统计卡片响应式样式 */
@media (max-width: 1280px) {
  .statistics-cards {
    flex-wrap: wrap;
  }
  
  .stat-card {
    flex: 0 0 calc(50% - 8px);
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .stat-card {
    flex: 0 0 100%;
  }
  
  .chart-section {
    flex-direction: column;
  }
  
  .chart-box .chart-container {
    width: 80px;
    height: 80px;
  }
  
  .chart-percent {
    font-size: 16px;
  }
}

/* 美化滚动条 */
.gas-leak-statistics::-webkit-scrollbar {
  width: 8px;
}

.gas-leak-statistics::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.gas-leak-statistics::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.gas-leak-statistics::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格美化 */
:deep(.el-table) {
  margin-bottom: 20px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}
</style>