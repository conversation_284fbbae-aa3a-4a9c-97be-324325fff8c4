<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患整改"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="rectify-dialog"
  >
    <div class="rectify-content">
      <!-- 新增按钮 -->
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </div>

      <!-- 整改记录列表 -->
      <el-table
        :data="rectifyList"
        style="width: 100%"
        :max-height="tableMaxHeight"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        :scrollbar-always-on="true"
        :fit="true"
        v-loading="loading"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="handleStatusName" label="整改状态" min-width="100" />
        <el-table-column prop="handleUserName" label="整改责任人" min-width="120" />
        <el-table-column prop="dealTime" label="整改时间" min-width="150" />
        <el-table-column prop="description" label="整改描述" min-width="200" />
        <el-table-column label="操作" fixed="right" min-width="150">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click="handleView(row)">查看</el-button>
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>

    <!-- 整改方案新增/编辑弹窗 -->
    <RectifyFormDialog
      v-model:visible="formDialogVisible"
      :mode="formMode"
      :data="formData"
      :danger-id="dangerId"
      @success="handleFormSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getHiddenDangerHandleList, deleteHiddenDangerHandle } from '@/api/drainage';
import RectifyFormDialog from './RectifyFormDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dangerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

// 加载状态
const loading = ref(false);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表格数据
const rectifyList = ref([]);

// 表单弹窗相关
const formDialogVisible = ref(false);
const formMode = ref('add'); // 'add', 'edit', 'view'
const formData = ref({});

// 表格最大高度
const tableMaxHeight = ref(300);

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 监听弹窗显示，获取数据
watch(() => props.visible, (newVal) => {
  if (newVal && props.dangerId) {
    fetchRectifyList();
  }
});

// 获取整改记录列表
const fetchRectifyList = async () => {
  loading.value = true;
  try {
    const res = await getHiddenDangerHandleList(props.dangerId);
    if (res && res.code === 200) {
      rectifyList.value = res.data || [];
      nextTick(() => {
        calculateTableMaxHeight();
      });
    }
  } catch (error) {
    console.error('获取整改记录失败:', error);
    ElMessage.error('获取整改记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理新增
const handleAdd = () => {
  formMode.value = 'add';
  formData.value = {};
  formDialogVisible.value = true;
};

// 处理查看
const handleView = (row) => {
  formMode.value = 'view';
  formData.value = { ...row };
  formDialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  formMode.value = 'edit';
  formData.value = { ...row };
  formDialogVisible.value = true;
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该整改记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteHiddenDangerHandle(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchRectifyList();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除整改记录失败:', error);
      ElMessage.error('删除整改记录失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理表单成功提交
const handleFormSuccess = () => {
  fetchRectifyList();
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const dialogBody = document.querySelector('.rectify-dialog .el-dialog__body');
    if (dialogBody) {
      const { top } = dialogBody.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const headerHeight = 60; // 弹窗标题栏高度
      const footerHeight = 70; // 弹窗底部按钮高度
      const headerActionsHeight = 60; // 新增按钮区域高度
      const availableHeight = viewportHeight - top - headerHeight - footerHeight - headerActionsHeight;
      tableMaxHeight.value = Math.max(200, availableHeight);
    }
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      calculateTableMaxHeight();
      window.addEventListener('resize', handleResize);
    });
  } else {
    window.removeEventListener('resize', handleResize);
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.rectify-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.rectify-content {
  min-height: 400px;
}

.header-actions {
  margin-bottom: 16px;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style> 