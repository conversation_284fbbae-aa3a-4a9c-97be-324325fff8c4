<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">报警列表</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-content">
            <!-- 查询条件 -->
            <div class="search-container">
              <el-form :model="searchParams" layout="inline">
                <el-form-item label="报警编号">
                  <el-input v-model="searchParams.alarmCode" placeholder="请输入报警编号" />
                </el-form-item>
                <el-form-item label="报警来源">
                  <el-select v-model="searchParams.alarmSource" placeholder="全部" popper-class="screen-dialog-popper">
                    <el-option label="全部" value="" />
                    <el-option v-for="source in ALARM_SOURCES" :key="source.value" :value="source.value" :label="source.label" />
                  </el-select>
                </el-form-item>
                <el-form-item label="报警等级">
                  <el-select v-model="searchParams.alarmLevel" placeholder="全部" popper-class="screen-dialog-popper">
                    <el-option label="全部" value="" />
                    <el-option v-for="level in ALARM_LEVELS" :key="level.value" :value="level.value" :label="level.label" />
                  </el-select>
                </el-form-item>
                <el-form-item label="报警时间">
                  <el-date-picker
                    v-model="searchParams.timeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :clearable="true"
                    popper-class="screen-dialog-popper"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchAlarms">查询</el-button>
                  <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 表格列表 -->
            <div class="table-container">
              <div v-if="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">数据加载中...</div>
              </div>
              <ScrollTable 
                v-else
                :columns="tableColumns" 
                :data="alarmList" 
                :autoScroll="false"
                :tableHeight="'600px'"
                @row-click="handleRowClick"
              >
                <!-- 自定义等级列 -->
                <template #alarmLevelName="{ row }">
                  <div class="level-icon-wrapper">
                    <!-- <SvgIcon 
                      :raw="getAlarmLevelIcon(row.alarmLevel)" 
                      :color="getAlarmLevelColor(row.alarmLevel)" 
                      size="20px"
                    /> -->
                    <span :style="{ color: getAlarmLevelColor(row.alarmLevel) }">{{ row.alarmLevelName }}</span>
                  </div>
                </template>
                
                <!-- 自定义编号列 -->
                <template #alarmCode="{ row }">
                  <span class="text-ellipsis" :title="row.alarmCode">{{ row.alarmCode }}</span>
                </template>
                
                <!-- 自定义设备名称列 -->
                <template #deviceName="{ row }">
                  <span class="text-ellipsis" :title="row.deviceName">{{ row.deviceName }}</span>
                </template>
                
                <!-- 自定义状态列 -->
                <template #handleStatusName="{ row }">
                  <span :class="getStatusClass(row.handleStatusName)">{{ row.handleStatusName }}</span>
                </template>
                
                <!-- 自定义位置列 -->
                <template #address="{ row }">
                  <div style="display: flex; align-items: center; gap: 5px;">
                    <!-- <SvgIcon 
                      :raw="locationIconSvg" 
                      color="#FF6D28" 
                      size="20px"
                    /> -->
                    <span class="text-ellipsis" :title="row.address">{{ row.address }}</span>
                  </div>
                </template>
                
                <!-- 自定义时间列 -->
                <template #alarmTime="{ row }">
                  {{ row.alarmTime }}
                </template>
              </ScrollTable>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
              <div class="page-info">共 {{ totalItems }} 条记录，每页 {{ pageSize }} 条</div>
              <div class="page-controls">
                <span class="page-btn" :class="{ disabled: currentPage === 1 }" @click="changePage(currentPage - 1)">上一页</span>
                <span class="page-number" v-for="page in pageNumbers" :key="page" :class="{ active: currentPage === page }" @click="changePage(page)">{{ page }}</span>
                <span class="page-btn" :class="{ disabled: currentPage === totalPages }" @click="changePage(currentPage + 1)">下一页</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { getMonitorAnalysisStatisticsCondition } from '@/api/gas'
import { ALARM_SOURCES, AREA_OPTIONS, ALARM_LEVEL } from '@/constants/gas'
import moment from 'moment'

// 报警等级选项
const ALARM_LEVELS = [
  { label: '一级', value: ALARM_LEVEL.LEVEL_ONE },
  { label: '二级', value: ALARM_LEVEL.LEVEL_TWO },
  { label: '三级', value: ALARM_LEVEL.LEVEL_THREE },
  { label: '四级', value: ALARM_LEVEL.LEVEL_FOUR }
]

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:model-value'])

// 关闭弹窗
const closeModal = () => {
  emit('update:model-value', false)
}

// 表格列配置
const tableColumns = [
  { title: '等级', dataIndex: 'alarmLevelName', width: '8%', fontSize: '13px' },
  { title: '编号', dataIndex: 'alarmCode', width: '15%', fontSize: '13px' },
  { title: '报警来源', dataIndex: 'alarmSource', width: '9%', fontSize: '13px' },
  { title: '设备名称', dataIndex: 'deviceName', width: '20%', fontSize: '13px' },
  { title: '处置状态', dataIndex: 'handleStatusName', width: '9%', fontSize: '13px' },
  { title: '位置', dataIndex: 'address', width: '12%', fontSize: '13px' },
  { title: '报警时间', dataIndex: 'alarmTime', width: '17%', fontSize: '13px' }
]

// 位置图标SVG
const locationIconSvg = `<svg viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M6.5 0C2.91 0 0 2.91 0 6.5C0 11.375 6.5 16 6.5 16C6.5 16 13 11.375 13 6.5C13 2.91 10.09 0 6.5 0ZM6.5 8.8C5.235 8.8 4.2 7.765 4.2 6.5C4.2 5.235 5.235 4.2 6.5 4.2C7.765 4.2 8.8 5.235 8.8 6.5C8.8 7.765 7.765 8.8 6.5 8.8Z" fill="currentColor"/>
</svg>`

// 报警等级图标SVG
const getAlarmLevelIcon = (level) => {
  // 使用通用的报警器图标，根据等级变换颜色
  return `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0ZM8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C11.5899 1.5 14.5 4.41015 14.5 8C14.5 11.5899 11.5899 14.5 8 14.5Z" fill="currentColor"/>
    <path d="M7.25 3.5V8.75H10.75V7.25H8.75V3.5H7.25Z" fill="currentColor"/>
  </svg>`
}

// 根据报警等级获取颜色
const getAlarmLevelColor = (level) => {
  switch (level) {
    case '9101': // 一级报警 - 红色
      return '#FC4949'
    case '9102': // 二级报警 - 橙色
      return '#FF6D28'
    case '9103': // 三级报警 - 黄色
      return '#FFC75A'
    case '9104': // 四级报警 - 蓝色
      return '#3B82F6'
    default:
      return '#FFFFFF'
  }
}

// 获取处置状态对应的样式类名
const getStatusClass = (status) => {
  switch (status) {
    case '待确认':
    case '9201':
      return 'status-pending'
    case '已处置':
    case '9206':
      return 'status-resolved'
    case '处置中':
    case '9202':
    case '9203':
    case '9204':
    case '9205':
      return 'status-processing'
    default:
      return ''
  }
}

// 搜索参数
const searchParams = ref({
  alarmCode: '',
  alarmLevel: 0,
  alarmSource: '',
  endDate: '',
  startDate: '',
  town: '',
  timeRange: []
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(100)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

// 计算页码显示
const pageNumbers = computed(() => {
  const pages = []
  let startPage = Math.max(1, currentPage.value - 2)
  let endPage = Math.min(totalPages.value, startPage + 4)
  
  // 调整startPage，确保始终有5个页码（如果总页数足够）
  if (endPage - startPage + 1 < 5 && totalPages.value >= 5) {
    startPage = Math.max(1, endPage - 4)
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i)
  }
  return pages
})

// 报警数据
const alarmList = ref([]);
const loading = ref(false);

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    loading.value = true;
    const params = {
      alarmCode: searchParams.value.alarmCode,
      alarmLevel: searchParams.value.alarmLevel,
      alarmSource: searchParams.value.alarmSource,
      town: searchParams.value.town,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    // 处理时间范围
    if (searchParams.value.timeRange && searchParams.value.timeRange.length === 2) {
      params.startDate = moment(searchParams.value.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
      params.endDate = moment(searchParams.value.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
    } else {
      params.startDate = searchParams.value.startDate ? moment(searchParams.value.startDate).format('YYYY-MM-DD') : '';
      params.endDate = searchParams.value.endDate ? moment(searchParams.value.endDate).format('YYYY-MM-DD') : '';
    }

    const response = await getMonitorAnalysisStatisticsCondition(params);
    if (response.code === 200) {
      const { records, total } = response.data;
      alarmList.value = records;
      totalItems.value = total;
    }
  } catch (error) {
    console.error('获取报警数据失败:', error);
  } finally {
    loading.value = false;
  }
};


// 处理行点击
const handleRowClick = (row) => {
  console.log('查看报警详情:', row)
  // 这里可以添加查看详情的逻辑
}

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) return
  currentPage.value = page
  fetchAlarmData()
}

// 搜索报警
const searchAlarms = () => {
  currentPage.value = 1
  fetchAlarmData()
}

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    alarmCode: '',
    alarmLevel: 0,
    alarmSource: '',
    endDate: '',
    startDate: '',
    town: '',
    timeRange: []
  }
  searchAlarms()
}



onMounted(() => {
  fetchAlarmData()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 1000px;
  height: 850px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 查询条件区域 */
.search-container {
  background: rgba(3, 24, 55, 0.5);
  border-radius: 4px;
  padding: 15px;
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow-y: auto;
  min-height: 390px;
  position: relative;
}

/* Loading样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 22, 72, 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 141, 242, 0.3);
  border-top: 3px solid #3B8DF2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-text {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #D3E5FF;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

:deep(.scroll-table td) {
  line-height: 40px;
}

/* 文本省略样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.page-info {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-btn,
.page-number {
  min-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 2px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-number:hover {
  background: rgba(59, 141, 242, 0.2);
}

.page-number.active {
  background: #1890FF;
  color: #FFFFFF;
}

.page-btn.disabled {
  cursor: not-allowed;
  color: rgba(255, 255, 255, 0.3);
}

/* 状态样式 */
.level-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-pending {
  color: #FF6D28;
}

.status-resolved {
  color: #3FD87C;
}

.status-processing {
  color: #FFC75A;
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
:deep(.el-select__wrapper) {
    align-items: center;
    background-color: transparent;
    border-radius: var(--el-border-radius-base);
    box-shadow: 0 0 0 1px rgba(59, 141, 242, 0.5) inset;
    box-sizing: border-box;
  }
/* Element Plus 样式覆盖 */
:deep(.el-form) {
  .el-form-item__label {
    color: #D3E5FF;
  }

  .el-button--primary {
    background: #1890FF;
    border-color: #1890FF;
    color: #FFFFFF;
  }
  .el-button {
    background: rgba(24, 144, 255, 0.1);
    border: 1px solid #1890FF;
    color: #1890FF;
  }
}

:deep(.el-input__wrapper) {
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: none;

  .el-input__inner {
    color: #fff;
    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: none;
}

:deep(.el-date-editor.el-range-editor) {
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: none;

  .el-range-input {
    color: #fff;
    background: transparent;
     &::placeholder {
        color: rgba(255, 255, 255, 0.3);
    }
  }

  .el-range-separator {
    color: #fff;
  }
}
</style>

<style lang="scss">
.screen-dialog-popper {
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.95) 0%, rgba(0, 35, 91, 0.95) 100%) !important;
  border: 1px solid rgba(59, 141, 242, 0.5) !important;
  z-index: 10000 !important;

  .el-select-dropdown__item {
    background-color: transparent;
    color: #fff;
  }
  .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
    background-color: rgba(0, 163, 255, 0.2);
  }
  .el-select-dropdown__item.selected {
    color: #409eff;
    font-weight: bold;
    background-color: rgba(0, 163, 255, 0.2);
  }

  .el-picker-panel {
    background: transparent !important;
    color: #fff;
    border: none !important;

    .el-picker-panel__header, .el-picker-panel__body {
      color: #fff;
    }

    .el-picker-panel__footer {
      background: transparent !important;
      border-top: 1px solid rgba(59, 141, 242, 0.3);
    }

    .el-date-table th {
      color: #fff;
      border-bottom: 1px solid rgba(59, 141, 242, 0.3);
    }

    .el-date-table td.available:hover {
      color: #409eff !important;
    }

    .el-date-table td.in-range .el-date-table-cell {
        background-color: rgba(0, 163, 255, 0.2);
    }

    .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
      background: #1890FF;
      color: #fff;
    }

    .el-picker-panel__icon-btn {
      color: #fff;
    }
    .el-picker-panel__icon-btn:hover {
      color: #409eff;
    }
  }

  .el-popper__arrow::before {
    background: rgba(0, 35, 91, 0.95) !important;
    border-color: rgba(59, 141, 242, 0.5) !important;
  }
}
</style>