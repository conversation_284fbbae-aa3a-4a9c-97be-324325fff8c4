<template>
  <div 
    v-if="unreadCount > 0 || alwaysShow"
    class="alarm-notification-btn"
    :class="notificationClass"
    @click="openAlarmModal"
    :title="`${unreadCount}条未读报警消息`"
  >
    <!-- 报警图标 -->
    <div class="alarm-icon" :class="{ 'animate-pulse': unreadCount > 0 }">
      <SvgIcon :raw="alarmIconSvg" size="20px" />
    </div>
    
    <!-- 未读数量徽章 -->
    <div v-if="unreadCount > 0" class="unread-badge">
      {{ unreadCount > 99 ? '99+' : unreadCount }}
    </div>
    
    <!-- 动画波纹效果 -->
    <div v-if="unreadCount > 0" class="ripple-animation"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAlarmStore } from '@/stores/alarm'
import SvgIcon from '@/components/SvgIcon.vue'

// Props
const props = defineProps({
  // 界面模式：screen(大屏) 或 admin(管理端)
  mode: {
    type: String,
    default: 'screen',
    validator: (value) => ['screen', 'admin'].includes(value)
  },
  // 是否总是显示（即使没有未读消息）
  alwaysShow: {
    type: Boolean,
    default: false
  },
  // 自定义位置样式类
  positionClass: {
    type: String,
    default: ''
  }
})

// Store
const alarmStore = useAlarmStore()

// 计算属性
const unreadCount = computed(() => alarmStore.unreadCount)
const isScreenMode = computed(() => props.mode === 'screen')
const notificationClass = computed(() => ({
  'screen-mode': isScreenMode.value,
  'admin-mode': !isScreenMode.value,
  'has-unread': unreadCount.value > 0,
  [props.positionClass]: props.positionClass
}))

// 方法
const openAlarmModal = () => {
  alarmStore.openGlobalModal()
}

// SVG 图标
const alarmIconSvg = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M8 2C5.79086 2 4 3.79086 4 6V7.5L2.5 9V11H13.5V9L12 7.5V6C12 3.79086 10.2091 2 8 2Z" fill="currentColor"/>
  <path d="M6.5 12.5C6.5 13.6046 7.39543 14.5 8.5 14.5C9.60457 14.5 10.5 13.6046 10.5 12.5" stroke="currentColor" stroke-width="1"/>
</svg>`
</script>

<style scoped>
.alarm-notification-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.alarm-notification-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* 大屏模式样式 */
.alarm-notification-btn.screen-mode {
  background: linear-gradient(135deg, #FF4949 0%, #FF6D28 100%);
  border: 2px solid rgba(255, 73, 73, 0.3);
  color: #FFFFFF;
}

.alarm-notification-btn.screen-mode:hover {
  background: linear-gradient(135deg, #FF6D28 0%, #FF8A5B 100%);
  border-color: rgba(255, 109, 40, 0.5);
}

.alarm-notification-btn.screen-mode.has-unread {
  animation: screen-glow 2s ease-in-out infinite alternate;
}

/* 管理端模式样式 */
.alarm-notification-btn.admin-mode {
  background: linear-gradient(135deg, #FF4757 0%, #FF6B7D 100%);
  border: 2px solid rgba(255, 71, 87, 0.3);
  color: #FFFFFF;
}

.alarm-notification-btn.admin-mode:hover {
  background: linear-gradient(135deg, #FF6B7D 0%, #FF8FA3 100%);
  border-color: rgba(255, 107, 125, 0.5);
}

.alarm-notification-btn.admin-mode.has-unread {
  animation: admin-glow 2s ease-in-out infinite alternate;
}

/* 报警图标 */
.alarm-icon {
  transition: all 0.3s ease;
}

.alarm-icon.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 未读数量徽章 */
.unread-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #FF1744;
  color: white;
  font-size: 10px;
  font-weight: bold;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: bounce-in 0.6s ease;
}

/* 波纹动画效果 */
.ripple-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 73, 73, 0.3);
  animation: ripple 2s infinite;
  pointer-events: none;
}

.admin-mode .ripple-animation {
  background: rgba(255, 71, 87, 0.3);
}

/* 动画定义 */
@keyframes screen-glow {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(255, 73, 73, 0.7);
  }
  100% {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 0 0 10px rgba(255, 73, 73, 0);
  }
}

@keyframes admin-glow {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(255, 71, 87, 0.7);
  }
  100% {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 0 0 10px rgba(255, 71, 87, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alarm-notification-btn {
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
  }
  
  .unread-badge {
    top: -6px;
    right: -6px;
    min-width: 18px;
    height: 18px;
    font-size: 9px;
  }
}

/* 自定义位置类 */
.top-right {
  top: 30px;
  right: 30px;
  bottom: auto;
}

.top-left {
  top: 30px;
  left: 30px;
  right: auto;
  bottom: auto;
}

.bottom-left {
  bottom: 30px;
  left: 30px;
  right: auto;
}

.center {
  top: 50%;
  left: 50%;
  right: auto;
  bottom: auto;
  transform: translate(-50%, -50%);
}

.center:hover {
  transform: translate(-50%, -50%) translateY(-2px);
}

/* 无障碍支持 */
.alarm-notification-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.7);
  outline-offset: 2px;
}

.alarm-notification-btn:focus:not(:focus-visible) {
  outline: none;
}

.alarm-notification-btn:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.7);
  outline-offset: 2px;
}
</style>