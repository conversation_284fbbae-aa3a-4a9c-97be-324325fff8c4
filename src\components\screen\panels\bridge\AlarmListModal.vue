<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">报警列表</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          <div class="modal-content">
            <!-- 查询条件 -->
            <div class="search-container">
              <div class="search-row">
                <div class="search-item">
                  <span class="search-label">报警编号:</span>
                  <input type="text" v-model="searchParams.alarmCode" placeholder="请输入报警编号" class="search-input" />
                </div>
                <div class="search-item">
                  <span class="search-label">报警类型:</span>
                  <select v-model="searchParams.alarmType" class="search-select">
                    <option value="">全部</option>
                    <option v-for="type in ALARM_TYPES" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="search-row">
                <div class="search-item">
                  <span class="search-label">报警等级:</span>
                  <select v-model="searchParams.alarmLevel" class="search-select">
                    <option value="">全部</option>
                    <option v-for="level in ALARM_LEVELS" :key="level.value" :value="level.value">
                      {{ level.label }}
                    </option>
                  </select>
                </div>
                <div class="search-item">
                  <span class="search-label">报警来源:</span>
                  <select v-model="searchParams.alarmSource" class="search-select">
                    <option value="">全部</option>
                    <option v-for="source in ALARM_SOURCES" :key="source.value" :value="source.value">
                      {{ source.label }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="search-row">
                <div class="search-item">
                  <span class="search-label">报警时间:</span>
                  <div class="date-range">
                    <input type="date" v-model="searchParams.startDate" class="date-input" />
                    <span class="date-separator">至</span>
                    <input type="date" v-model="searchParams.endDate" class="date-input" />
                  </div>
                </div>
                <div class="search-btn-group">
                  <button class="search-btn" @click="searchAlarms">查 询</button>
                  <button class="reset-btn" @click="resetSearch">重 置</button>
                </div>
              </div>
            </div>
            
            <!-- 表格列表 -->
            <div class="table-container">
              <div v-if="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">数据加载中...</div>
              </div>
              <ScrollTable 
                v-else
                :columns="tableColumns" 
                :data="alarmList" 
                :autoScroll="false"
                :tableHeight="'600px'"
                @row-click="handleRowClick"
              >
                <!-- 自定义等级列 -->
                <template #alarmLevelName="{ row }">
                  <div class="level-icon-wrapper">
                    <SvgIcon 
                      :raw="getAlarmLevelIcon(row.alarmLevel)" 
                      :color="getAlarmLevelColor(row.alarmLevel)" 
                      size="20px"
                    />
                  </div>
                </template>
                
                <!-- 自定义编号列 -->
                <template #alarmCode="{ row }">
                  <span class="text-ellipsis" :title="row.alarmCode">{{ row.alarmCode }}</span>
                </template>
                
                <!-- 自定义设备名称列 -->
                <template #deviceName="{ row }">
                  <span class="text-ellipsis" :title="row.deviceName">{{ row.deviceName }}</span>
                </template>
                
                <!-- 自定义状态列 -->
                <template #alarmStatusName="{ row }">
                  <span :class="getStatusClass(row.alarmStatusName)">{{ row.alarmStatusName }}</span>
                </template>
                
                <!-- 自定义位置列 -->
                <template #alarmLocation="{ row }">
                  <div style="display: flex; align-items: center; gap: 5px;">
                    <span class="text-ellipsis" :title="row.alarmLocation">{{ row.alarmLocation }}</span>
                  </div>
                </template>
                
                <!-- 自定义时间列 -->
                <template #alarmTime="{ row }">
                  {{ row.alarmTime }}
                </template>
              </ScrollTable>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
              <div class="page-info">共 {{ totalItems }} 条记录，每页 {{ pageSize }} 条</div>
              <div class="page-controls">
                <span class="page-btn" :class="{ disabled: currentPage === 1 }" @click="changePage(currentPage - 1)">上一页</span>
                <span class="page-number" v-for="page in pageNumbers" :key="page" :class="{ active: currentPage === page }" @click="changePage(page)">{{ page }}</span>
                <span class="page-btn" :class="{ disabled: currentPage === totalPages }" @click="changePage(currentPage + 1)">下一页</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { getBridgeAlarmList, getBridgeAlarmTypes } from '@/api/bridge'
import { BRIDGE_ALARM_SOURCE_OPTIONS } from '@/constants/bridge'
import moment from 'moment'

// 报警类型选项
const ALARM_TYPES = ref([])

// 报警来源选项
const ALARM_SOURCES = BRIDGE_ALARM_SOURCE_OPTIONS.filter(item => item.value !== '')

// 报警等级选项
const ALARM_LEVELS = [
  { label: '一级', value: 4003601 },
  { label: '二级', value: 4003602 },
  { label: '三级', value: 4003603 },
  { label: '四级', value: 4003604 }
]

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:model-value'])

// 关闭弹窗
const closeModal = () => {
  emit('update:model-value', false)
}

// 表格列配置
const tableColumns = [
  { title: '等级', dataIndex: 'alarmLevelName', width: '8%', fontSize: '13px' },
  { title: '编号', dataIndex: 'alarmCode', width: '15%', fontSize: '13px' },
  { title: '报警类型', dataIndex: 'alarmTypeName', width: '12%', fontSize: '13px' },
  { title: '设备名称', dataIndex: 'deviceName', width: '18%', fontSize: '13px' },
  { title: '处置状态', dataIndex: 'alarmStatusName', width: '9%', fontSize: '13px' },
  { title: '位置', dataIndex: 'alarmLocation', width: '20%', fontSize: '13px' },
  { title: '报警时间', dataIndex: 'alarmTime', width: '18%', fontSize: '13px' }
]

// 报警等级图标SVG
const getAlarmLevelIcon = (level) => {
  // 使用通用的报警器图标，根据等级变换颜色
  return `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0ZM8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C11.5899 1.5 14.5 4.41015 14.5 8C14.5 11.5899 11.5899 14.5 8 14.5Z" fill="currentColor"/>
    <path d="M7.25 3.5V8.75H10.75V7.25H8.75V3.5H7.25Z" fill="currentColor"/>
  </svg>`
}

// 根据报警等级获取颜色
const getAlarmLevelColor = (level) => {
  switch (level) {
    case 4003601: // 一级报警 - 红色
      return '#FC4949'
    case 4003602: // 二级报警 - 橙色
      return '#FF6D28'
    case 4003603: // 三级报警 - 黄色
      return '#FFC75A'
    case 4003604: // 四级报警 - 蓝色
      return '#3B82F6'
    default:
      return '#FFFFFF'
  }
}

// 获取处置状态对应的样式类名
const getStatusClass = (status) => {
  switch (status) {
    case '待确认':
    case '待处置':
      return 'status-pending'
    case '已处置':
    case '处置完成':
      return 'status-resolved'
    case '处置中':
      return 'status-processing'
    default:
      return ''
  }
}

// 搜索参数
const searchParams = ref({
  alarmCode: '',
  alarmLevel: '',
  alarmType: '',
  alarmSource: '',
  endDate: '',
  startDate: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

// 计算页码显示
const pageNumbers = computed(() => {
  const pages = []
  let startPage = Math.max(1, currentPage.value - 2)
  let endPage = Math.min(totalPages.value, startPage + 4)
  
  // 调整startPage，确保始终有5个页码（如果总页数足够）
  if (endPage - startPage + 1 < 5 && totalPages.value >= 5) {
    startPage = Math.max(1, endPage - 4)
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i)
  }
  return pages
})

// 报警数据
const alarmList = ref([])
const loading = ref(false)

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    loading.value = true
    const params = {
      alarmCode: searchParams.value.alarmCode,
      alarmLevel: searchParams.value.alarmLevel,
      alarmType: searchParams.value.alarmType,
      alarmSource: searchParams.value.alarmSource,
      startDate: searchParams.value.startDate ? moment(searchParams.value.startDate).format('YYYY-MM-DD') : '',
      endDate: searchParams.value.endDate ? moment(searchParams.value.endDate).format('YYYY-MM-DD') : ''
    }
    
    const response = await getBridgeAlarmList(currentPage.value, pageSize.value, params)
    if (response.code === 200) {
      const { rows, total } = response.data
      alarmList.value = rows || []
      totalItems.value = total || 0
    }
  } catch (error) {
    console.error('获取桥梁报警数据失败:', error)
    alarmList.value = []
    totalItems.value = 0
  } finally {
    loading.value = false
  }
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('查看桥梁报警详情:', row)
  // 这里可以添加查看详情的逻辑
}

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) return
  currentPage.value = page
  fetchAlarmData()
}

// 搜索报警
const searchAlarms = () => {
  currentPage.value = 1
  fetchAlarmData()
}

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    alarmCode: '',
    alarmLevel: '',
    alarmType: '',
    alarmSource: '',
    endDate: '',
    startDate: ''
  }
  searchAlarms()
}

// 获取报警类型选项
const fetchAlarmTypes = async () => {
  try {
    const response = await getBridgeAlarmTypes()
    if (response.code === 200 && response.data) {
      ALARM_TYPES.value = response.data.map(item => ({
        label: item.alarmTypeName,
        value: item.alarmType
      }))
    }
  } catch (error) {
    console.error('获取桥梁报警类型失败:', error)
    // 如果接口失败，使用默认的报警类型选项
    ALARM_TYPES.value = [
      { label: '桥梁温度监测报警', value: 4003101 },
      { label: '桥梁湿度监测报警', value: 4003102 },
      { label: '桥梁位移/变形监测报警', value: 4003103 },
      { label: '桥梁应变监测报警', value: 4003104 },
      { label: '桥梁裂缝监测报警', value: 4003105 },
      { label: '桥梁挠度监测报警', value: 4003106 }
    ]
  }
}

onMounted(() => {
  fetchAlarmTypes()
  fetchAlarmData()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 1000px;
  height: 700px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden;
}

/* 查询条件区域 */
.search-container {
  background: rgba(3, 24, 55, 0.5);
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-label {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #D3E5FF;
  margin-right: 10px;
  white-space: nowrap;
}

.search-input,
.search-select {
  flex: 1;
  height: 32px;
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 2px;
  padding: 0 10px;
  color: #FFFFFF;
  font-size: 14px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.search-input:focus,
.search-select:focus {
  outline: none;
  border-color: rgba(59, 141, 242, 0.8);
}

.date-range {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 5px;
}

.date-input {
  flex: 1;
  height: 32px;
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 2px;
  padding: 0 10px;
  color: #FFFFFF;
  font-size: 14px;
}

.date-separator {
  color: #D3E5FF;
  white-space: nowrap;
}

.search-btn-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
}

.search-btn,
.reset-btn {
  height: 32px;
  border-radius: 2px;
  border: none;
  padding: 0 15px;
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
}

.search-btn {
  background: #1890FF;
  color: #FFFFFF;
}

.reset-btn {
  background: rgba(24, 144, 255, 0.1);
  border: 1px solid #1890FF;
  color: #1890FF;
}

.search-btn:hover {
  background: #40A9FF;
}

.reset-btn:hover {
  background: rgba(24, 144, 255, 0.2);
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow-y: auto;
  min-height: 390px;
  position: relative;
}

/* Loading样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 22, 72, 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 141, 242, 0.3);
  border-top: 3px solid #3B8DF2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-text {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #D3E5FF;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

:deep(.scroll-table td) {
  line-height: 40px;
}

/* 文本省略样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.page-info {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-btn,
.page-number {
  min-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 2px;
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.page-number:hover {
  background: rgba(59, 141, 242, 0.2);
}

.page-number.active {
  background: #1890FF;
  color: #FFFFFF;
}

.page-btn.disabled {
  cursor: not-allowed;
  color: rgba(255, 255, 255, 0.3);
}

/* 状态样式 */
.level-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-pending {
  color: #FF6D28;
}

.status-resolved {
  color: #3FD87C;
}

.status-processing {
  color: #FFC75A;
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>