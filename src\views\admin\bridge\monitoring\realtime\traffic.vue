<template>
  <div class="traffic-monitoring-container">
    <div class="content-wrapper">
      <!-- 左侧桥梁列表 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3 class="panel-title">桥梁名称</h3>
          <el-input
            v-model="bridgeSearchText"
            placeholder="桥梁名称"
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="bridge-list">
          <div
            v-for="bridge in filteredBridgeList"
            :key="bridge.id"
            :class="['bridge-item', { active: selectedBridgeId === bridge.id }]"
            @click="handleBridgeSelect(bridge)"
          >
            {{ bridge.bridgeName }}
          </div>
        </div>
      </div>

      <!-- 右侧数据列表 -->
      <div class="right-panel">
        <h3 class="panel-title">当前车流量：{{trafficFlow}}</h3>
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-form">
            <div class="form-item">
              <span class="label">监测设备:</span>
              <el-select
                v-model="searchForm.deviceId"
                class="form-select"
                placeholder="请选择监测设备"
                clearable
              >
                <el-option
                  v-for="device in deviceOptions"
                  :key="device.value"
                  :label="device.label"
                  :value="device.value"
                />
              </el-select>
            </div>
            <div class="form-item">
              <span class="label">过车时间:</span>
              <el-date-picker
                v-model="searchForm.passTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="form-datetime"
              />
            </div>
            <div class="form-item">
              <span class="label">车重:</span>
              <div class="weight-range">
                <el-input-number
                  v-model="searchForm.minWeight"
                  :min="0"
                  :precision="2"
                  placeholder="最小重量"
                  class="weight-input"
                />
                <span class="range-separator">-</span>
                <el-input-number
                  v-model="searchForm.maxWeight"
                  :min="0"
                  :precision="2"
                  placeholder="最大重量"
                  class="weight-input"
                />
                <span class="unit-text">kg</span>
              </div>
            </div>
            <div class="form-item">
              <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
              <el-button class="reset-btn" @click="handleReset">重置</el-button>
            </div>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-container" ref="tableContainerRef">
          <el-table
            :data="tableData"
            style="width: 100%"
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName"
            @row-click="handleRowClick"
            :height="tableHeight"
            empty-text="暂无数据"
            v-loading="loading"
          >
          <el-table-column label="序号" min-width="60">
            <template #default="{ $index }">
              {{ (currentPage - 1) * pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="监测设备" min-width="120" />
          <el-table-column label="过车时间" min-width="160">
            <template #default="{ row }">
              {{ formatPassTime(row.passTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="plateNumber" label="车牌号" min-width="100" />
          <el-table-column prop="carType" label="车牌类型" min-width="100" />
          <el-table-column label="车速" min-width="80">
            <template #default="{ row }">
              {{ row.speed }}km/h
            </template>
          </el-table-column>
          <el-table-column label="车重" min-width="100">
            <template #default="{ row }">
              {{ row.weight }}kg
            </template>
          </el-table-column>
          <el-table-column prop="axleCount" label="轴数" min-width="60" />
          <el-table-column label="车牌编码" min-width="120">
            <template #default="{ row }">
              {{ row.plateCode || '------' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120">
            <template #default="{ row }">
              <div class="operation-btns">
                <el-button type="primary" link @click.stop="handleDetail(row)">查看明细</el-button>
              </div>
            </template>
          </el-table-column>
          </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 对话框区域 -->
    <TrafficDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElInput, ElSelect, ElOption, ElDatePicker, ElInputNumber, ElButton, ElTable, ElTableColumn, ElPagination, ElIcon } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import {
  getTrafficLoadMonitorPage,
  getTrafficLoadMonitorDetail,
  getBridgeBasicInfoList,
  getMonitorDeviceListByBridge
} from '@/api/bridge'
import moment from 'moment'
import TrafficDialog from './components/TrafficDialog.vue'

// 桥梁相关
const bridgeList = ref([])
const selectedBridgeId = ref('')
const selectedBridgeName = ref('')
const bridgeSearchText = ref('')

// 设备选项
const deviceOptions = ref([])

// 搜索表单
const searchForm = ref({
  deviceId: '',
  passTimeRange: [],
  minWeight: null,
  maxWeight: null
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableData = ref([])
const trafficFlow = ref(0)
const loading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('view')
const dialogData = ref({})

const tableHeight = ref(400)
const tableContainerRef = ref(null)

// 计算属性：过滤后的桥梁列表
const filteredBridgeList = computed(() => {
  if (!bridgeSearchText.value) {
    return bridgeList.value
  }
  return bridgeList.value.filter(bridge =>
    bridge.bridgeName.toLowerCase().includes(bridgeSearchText.value.toLowerCase())
  )
})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList({})
    if (res && res.data) {
      bridgeList.value = res.data
      // 默认选中第一个桥梁
      if (bridgeList.value.length > 0) {
        const firstBridge = bridgeList.value[0]
        selectedBridgeId.value = firstBridge.id
        selectedBridgeName.value = firstBridge.bridgeName
        // 获取对应的设备列表和交通数据
        fetchDeviceList()
        fetchTrafficData()
      }
    }
  } catch (error) {
    console.error('获取桥梁列表失败:', error)
    ElMessage.error('获取桥梁列表失败')
  }
}

// 获取监测设备列表
const fetchDeviceList = async () => {
  if (!selectedBridgeId.value) {
    deviceOptions.value = []
    return
  }

  try {
    const res = await getMonitorDeviceListByBridge(selectedBridgeId.value)
    if (res && res.data) {
      deviceOptions.value = res.data.map(device => ({
        label: device.deviceName || device.name,
        value: device.id
      }))
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    deviceOptions.value = []
  }
}

// 获取交通荷载数据
const fetchTrafficData = async () => {
  if (!selectedBridgeId.value) {
    tableData.value = []
    total.value = 0
    return
  }

  try {
    loading.value = true;
    const params = {
      bridgeId: selectedBridgeId.value,
      deviceId: searchForm.value.deviceId || undefined,
      minWeight: searchForm.value.minWeight || undefined,
      maxWeight: searchForm.value.maxWeight || undefined
    }

    // 处理时间范围
    if (searchForm.value.passTimeRange && searchForm.value.passTimeRange.length === 2) {
      params.startTime = searchForm.value.passTimeRange[0]
      params.endTime = searchForm.value.passTimeRange[1]
    }

    const res = await getTrafficLoadMonitorPage(currentPage.value, pageSize.value, params)

    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取交通荷载数据失败:', error)
    ElMessage.error('获取交通荷载数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 格式化过车时间
const formatPassTime = (passTime) => {
  if (!passTime) return '------'
  
  // 如果是时间对象格式，转换为时间戳
  if (typeof passTime === 'object' && passTime.time) {
    return moment(passTime.time).format('YYYY-MM-DD HH:mm:ss')
  }
  
  // 如果是时间戳或字符串
  return moment(passTime).format('YYYY-MM-DD HH:mm:ss')
}

// 处理桥梁选择
const handleBridgeSelect = (bridge) => {
  if (selectedBridgeId.value !== bridge.id) {
    selectedBridgeId.value = bridge.id
    selectedBridgeName.value = bridge.bridgeName
    // 重置搜索条件
    searchForm.value = {
      deviceId: '',
      passTimeRange: [],
      minWeight: null,
      maxWeight: null
    }
    currentPage.value = 1
    fetchDeviceList()
    fetchTrafficData()
  }
}

// 监听选中桥梁变化
watch(() => selectedBridgeId.value, () => {
  if (selectedBridgeId.value) {
    currentPage.value = 1
    fetchDeviceList()
    fetchTrafficData()
  }
})

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchTrafficData()
}

// 处理重置
const handleReset = () => {
  searchForm.value = {
    deviceId: '',
    passTimeRange: [],
    minWeight: null,
    maxWeight: null
  }
  currentPage.value = 1
  fetchTrafficData()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchTrafficData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchTrafficData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理查看明细
const handleDetail = async (row) => {
  try {
    const res = await getTrafficLoadMonitorDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainerRef.value) {
      const containerHeight = tableContainerRef.value.offsetHeight;
      if (containerHeight > 0) {
        tableHeight.value = Math.max(containerHeight, 500);
      } else {
        tableHeight.value = 600;
        setTimeout(calculateTableHeight, 100);
      }
    }
  });
};

// 窗口大小改变时重新计算表格高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchBridgeList();
    calculateTableHeight();
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.traffic-monitoring-container {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 180px);
  box-sizing: border-box;
  overflow: hidden;
}

.content-wrapper {
  flex: 1;
  display: flex;
  height: 100%;
  min-height: 0;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  border-right: 1px solid #E4E7ED;
  display: flex;
  flex-direction: column;
  background-color: #FAFAFA;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #E4E7ED;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 12px 0;
}

.search-input {
  width: 100%;
}

.bridge-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.bridge-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #F0F0F0;
  color: #333;
  font-size: 14px;
  transition: all 0.2s;
}

.bridge-item:hover {
  background-color: #E6F7FF;
  color: #0277FD;
}

.bridge-item.active {
  background-color: #0277FD;
  color: white;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
  min-width: 0;
  box-sizing: border-box;
  overflow: hidden;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 16px;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-select {
  width: 180px;
  height: 32px;
}

.form-datetime {
  width: 360px;
}

.weight-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weight-input {
  width: 150px;
}

.range-separator {
  color: #666;
  font-size: 14px;
}

.unit-text {
  color: #666;
  font-size: 14px;
  margin-left: 4px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

:deep(.el-select .el-input__wrapper) {
  height: 32px;
}

:deep(.el-date-editor) {
  height: 32px;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
  height: 100% !important;
  display: flex;
  flex-direction: column;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .left-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }
  
  .left-panel {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #E4E7ED;
  }
  
  .bridge-list {
    flex-direction: row;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .bridge-item {
    display: inline-block;
    min-width: 120px;
    border-right: 1px solid #F0F0F0;
    border-bottom: none;
  }
}
</style>
