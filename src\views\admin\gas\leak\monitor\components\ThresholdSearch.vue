<template>
  <div class="threshold-search">
    <div class="search-form">
      <div class="form-item">
        <span class="label">规则名称:</span>
        <el-input v-model="formData.ruleName" class="form-input" placeholder="请输入规则名称" />
      </div>
      <div class="form-item">
        <span class="label">是否生效:</span>
        <el-select v-model="formData.isEnabled" class="form-input" placeholder="全部">
          <el-option label="是" value="true" />
          <el-option label="否" value="false" />
        </el-select>
      </div>
      <div class="form-item" style="margin-left: auto;">
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElInput, ElSelect, ElOption, ElButton } from 'element-plus';

const emit = defineEmits(['search', 'reset']);

const formData = ref({
  ruleName: '',
  isEnabled: ''
});

const handleSearch = () => {
  emit('search', formData.value);
};

const handleReset = () => {
  formData.value = {
    ruleName: '',
    isEnabled: ''
  };
  emit('reset');
};
</script>

<style scoped>
.threshold-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style>