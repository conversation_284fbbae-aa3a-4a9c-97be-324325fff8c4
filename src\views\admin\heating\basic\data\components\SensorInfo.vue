<template>
  <div class="sensor-info-container">
    <!-- 搜索区域 -->
    <div class="sensor-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">设备类型:</span>
          <el-select v-model="formData.deviceType" class="form-input" placeholder="全部">
            <el-option v-for="item in deviceTypes" :key="item.deviceType" :label="item.deviceTypeName" :value="item.deviceType" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">设备状态:</span>
          <el-select v-model="formData.onlineStatus" class="form-input" placeholder="全部">
            <el-option v-for="item in DEVICE_STATUS_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.keyWord" class="form-input" placeholder="设备名称/编码" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header" style="display: none;">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
      <el-table-column prop="indexCode" label="设备编码" min-width="120" show-overflow-tooltip />
      <el-table-column prop="monitorIndexName" label="监测指标" min-width="150" show-overflow-tooltip />
      <el-table-column label="采集频率" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.collectFrequency }}分钟/次
        </template>
      </el-table-column>
      <el-table-column prop="monitorTargetName" label="监测对象" min-width="120" show-overflow-tooltip />
      <el-table-column prop="address" label="位置" min-width="150" show-overflow-tooltip />
      <el-table-column label="设备状态" min-width="100">
        <template #default="{ row }">
          <span :class="row.onlineStatus === 1 ? 'status-online' : 'status-offline'">
            {{ DEVICE_STATUS_MAP[row.onlineStatus] || '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="180" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleView(row)">查看详情</span>
              <span class="operation-divider">|</span>
              <!-- <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
              <span class="operation-divider">|</span> -->
              <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 传感器信息弹窗 -->
    <SensorDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, onUnmounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getSensorDevicePage, getSensorDeviceDetail, deleteSensorDevice, getDeviceType } from '@/api/heating';
import { misPosition } from '@/hooks/gishooks';
import { DEVICE_STATUS_OPTIONS, DEVICE_STATUS_MAP } from '@/constants/heating';
import SensorDialog from './SensorDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 设备类型选项
const deviceTypes = ref([]);

// 表单数据
const formData = ref({
  deviceType: '',
  onlineStatus: '',
  keyWord: ''
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchSensorData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    deviceType: '',
    onlineStatus: '',
    keyWord: ''
  };
  currentPage.value = 1;
  fetchSensorData();
};

// 获取传感器分页数据
const fetchSensorData = async () => {
  try {
    loading.value = true;
    const params = {
      deviceType: formData.value.deviceType,
      onlineStatus: formData.value.onlineStatus,
      keyWord: formData.value.keyWord
    };
    
    const res = await getSensorDevicePage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取传感器数据失败:', error);
    ElMessage.error('获取传感器数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchSensorData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchSensorData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理查看详情
const handleView = async (row) => {
  try {
    const res = await getSensorDeviceDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '获取传感器详情失败');
    }
  } catch (error) {
    console.error('获取传感器详情失败:', error);
    ElMessage.error('获取传感器详情失败');
  }
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getSensorDeviceDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '获取传感器详情失败');
    }
  } catch (error) {
    console.error('获取传感器详情失败:', error);
    ElMessage.error('获取传感器详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该传感器信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteSensorDevice(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchSensorData();
      } else {
        ElMessage.error(res?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除传感器信息失败:', error);
      ElMessage.error('删除传感器信息失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude !== 0 &&
    row.longitude &&
    row.longitude !== 0
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理弹窗成功
const handleDialogSuccess = () => {
  fetchSensorData();
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理导入
const handleImport = () => {
  console.log('导入');
  ElMessage.info('导入功能待实现');
};

// 获取设备类型数据
const fetchDeviceTypes = async () => {
  try {
    const res = await getDeviceType();
    if (res && res.code === 200) {
      deviceTypes.value = res.data || [];
    }
  } catch (error) {
    console.error('获取设备类型失败:', error);
    ElMessage.error('获取设备类型失败');
    deviceTypes.value = [];
  }
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.sensor-info-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.sensor-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchDeviceTypes(),
      fetchSensorData()
    ]);
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.sensor-info-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 搜索区域样式 */
.sensor-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 80px;
  height: 32px;
  padding: 0;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

.status-online {
  color: #52c41a;
}

.status-offline {
  color: #ff4d4f;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  min-height: 200px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>