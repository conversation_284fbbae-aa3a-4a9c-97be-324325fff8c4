<template>
  <PanelBox title="预警统计">
    <div class="panel-content">
      <!-- 上部分：预警总数和处置情况 -->
      <div class="top-section">
        <!-- 左侧：预警总数 -->
        <div class="alarm-total">
          <div class="alarm-total-bg"></div>
          <div class="alarm-total-content">
            <div class="alarm-total-label">预警总数</div>
            <div class="alarm-total-value">{{ warningData.totalCount }}</div>
          </div>
        </div>
        
        <!-- 右侧：处置情况 -->
        <div class="alarm-status">
          <div class="status-row">
            <div class="status-item">
              <div class="status-dot-container">
                <div class="status-dot blue-dot">
                  <div class="status-dot-inner"></div>
                </div>
              </div>
              <div class="status-text">已解除</div>
              <div class="status-value blue-value">{{ warningData.released }}</div>
            </div>
            <div class="status-item">
              <div class="status-dot-container">
                <div class="status-dot green-dot">
                  <div class="status-dot-inner"></div>
                </div>
              </div>
              <div class="status-text">已处置</div>
              <div class="status-value green-value">{{ warningData.handled }}</div>
            </div>
          </div>
          <div class="status-row">
            <div class="status-item">
              <div class="status-dot-container">
                <div class="status-dot orange-dot">
                  <div class="status-dot-inner"></div>
                </div>
              </div>
              <div class="status-text">处置中</div>
              <div class="status-value orange-value">{{ warningData.handling }}</div>
            </div>
            <div class="status-item">
              <div class="status-dot-container">
                <div class="status-dot red-dot">
                  <div class="status-dot-inner"></div>
                </div>
              </div>
              <div class="status-text">未处置</div>
              <div class="status-value red-value">{{ warningData.pendingHandle }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 下部分：预警级别 -->
      <div class="bottom-section">
        <div class="alarm-level level-1">
          <div class="level-content">
            <div class="level-text">一级预警</div>
            <div class="level-value">{{ levelData.level1Count }}</div>
          </div>
        </div>
        <div class="alarm-level level-2">
          <div class="level-content">
            <div class="level-text">二级预警</div>
            <div class="level-value">{{ levelData.level2Count }}</div>
          </div>
        </div>
        <div class="alarm-level level-3">
          <div class="level-content">
            <div class="level-text">三级预警</div>
            <div class="level-value">{{ levelData.level3Count }}</div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getLinkageWarningStatistics, getLinkageWarningLevelStatistics } from '@/api/comprehensive'

// 综合态势总览左上面板组件

// 预警统计数据
const warningData = ref({
  totalCount: 0,
  pendingHandle: 0,
  handling: 0,
  handled: 0,
  released: 0
})

// 预警级别统计数据
const levelData = ref({
  level1Count: 0,
  level2Count: 0,
  level3Count: 0
})

// 加载状态
const loading = ref(false)

// 定时器
let refreshTimer = null

/**
 * 获取预警统计数据
 */
const fetchWarningStatistics = async () => {
  try {
    const response = await getLinkageWarningStatistics()
    if (response.code === 200 && response.data) {
      warningData.value = {
        totalCount: response.data.totalCount || 0,
        pendingHandle: response.data.pendingHandle || 0,
        handling: response.data.handling || 0,
        handled: response.data.handled || 0,
        released: response.data.released || 0
      }
    }
  } catch (error) {
    console.error('获取预警统计数据失败:', error)
  }
}

/**
 * 获取预警级别统计数据
 */
const fetchWarningLevelStatistics = async () => {
  try {
    const response = await getLinkageWarningLevelStatistics({
      startTime: '',
      endTime: ''
    })
    if (response.code === 200 && response.data) {
      levelData.value = {
        level1Count: response.data.level1Count || 0,
        level2Count: response.data.level2Count || 0,
        level3Count: response.data.level3Count || 0
      }
    }
  } catch (error) {
    console.error('获取预警级别统计数据失败:', error)
  }
}

/**
 * 加载所有数据
 */
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchWarningStatistics(),
      fetchWarningLevelStatistics()
    ])
  } finally {
    loading.value = false
  }
}

/**
 * 启动数据刷新定时器
 */
const startDataRefresh = () => {
  // 每5分钟刷新一次数据
  refreshTimer = setInterval(() => {
    loadData()
  }, 300000)
}

/**
 * 停止数据刷新定时器
 */
const stopDataRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  // 初始加载数据
  loadData()
  // 启动定时刷新
  startDataRefresh()
})

onUnmounted(() => {
  // 清理定时器
  stopDataRefresh()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 上部分样式 */
.top-section {
  display: flex;
  height: 80px;
  gap: 15px;
}

/* 预警总数样式 */
.alarm-total {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alarm-total-bg {
  position: absolute;
  top: 0.5rem;
  left: 3.1rem;
  width: 108px;
  height: 120px;
  background-image: url('@/assets/images/screen/comprehensive/alarmtotal.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.alarm-total-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.alarm-total-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  margin-bottom: 5px;
}

.alarm-total-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
}

/* 处置情况样式 */
.alarm-status {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 2rem;
  gap: 1.5rem;
}

.status-row {
  display: flex;
  justify-content: space-between;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
}

.status-dot-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
}

.status-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
}

.blue-dot {
  background: rgba(0, 172, 255, 0.4);
}

.blue-dot .status-dot-inner {
  background: #00ACFF;
}

.green-dot {
  background: rgba(63, 216, 124, 0.4);
}

.green-dot .status-dot-inner {
  background: #3FD87C;
}

.orange-dot {
  background: rgba(255, 199, 90, 0.4);
}

.orange-dot .status-dot-inner {
  background: #FFC75A;
}

.red-dot {
  background: rgba(255, 109, 40, 0.4);
}

.red-dot .status-dot-inner {
  background: #FF6D28;
}

.status-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.status-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
}

.blue-value {
  background: linear-gradient(90deg, #FFFFFF 0%, #00ABFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.green-value {
  background: linear-gradient(90deg, #43DF81 0%, #A6FED0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.orange-value {
  background: linear-gradient(90deg, #FFC24C 0%, #FEDFA6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.red-value {
  background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 下部分样式 */
.bottom-section {
  display: flex;
  justify-content: space-between;
  height: 50px;
  gap: 15px;
  margin-top: 10%;
}

.alarm-level {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.level-1 {
  background-image: url('@/assets/images/screen/comprehensive/leve1_bg.png');
}

.level-2 {
  background-image: url('@/assets/images/screen/comprehensive/level2_bg.png');
}

.level-3 {
  background-image: url('@/assets/images/screen/comprehensive/level3_bg.png');
}

.level-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.level-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  /* background: linear-gradient(90deg, #FF5717 0%, #FFCD72 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent; */
  margin-bottom: -2px;
}

.level-value {
  font-family: DINAlternate, DINAlternate;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
}
</style>