<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="public-alarm-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报警标题" prop="alarmTitle">
            <el-input v-model="formData.alarmTitle" placeholder="请输入报警标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报警编号" prop="alarmCode">
            <el-input v-model="formData.alarmCode" placeholder="请输入报警编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报警来源" prop="alarmSource">
            <el-select v-model="formData.alarmSource" placeholder="请选择" class="w-full" @change="handleAlarmSourceChange">
              <el-option v-for="item in alarmSourceOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报警状态" prop="alarmStatus">
            <el-select v-model="formData.alarmStatus" placeholder="请选择" class="w-full" @change="handleAlarmStatusChange">
              <el-option v-for="item in alarmStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full" @change="handleRelatedBusinessChange">
              <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="紧急程度" prop="urgentLevel">
            <el-select v-model="formData.urgentLevel" placeholder="请选择" class="w-full" @change="handleUrgentLevelChange">
              <el-option v-for="item in urgentLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发生报警时间" prop="alarmTime">
            <el-date-picker
              v-model="formData.alarmTime"
              type="datetime"
              placeholder="请选择发生报警时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报时间" prop="reportTime">
            <el-date-picker
              v-model="formData.reportTime"
              type="datetime"
              placeholder="请选择上报时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上报人员" prop="reportPerson">
            <el-input v-model="formData.reportPerson" placeholder="请输入上报人员" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人联系方式" prop="reportContact">
            <el-input v-model="formData.reportContact" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处置单位" prop="handleUnit">
            <el-tree-select
              v-model="formData.handleUnit"
              :data="deptTreeOptions"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children'
              }"
              placeholder="请选择处置单位"
              class="w-full"
              @change="handleDeptChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通报区域" prop="notifyArea">
            <el-select 
              v-model="formData.notifyArea" 
              multiple 
              placeholder="请选择通报区域" 
              class="w-full"
              @change="handleNotifyAreaChange"
            >
              <el-option 
                v-for="item in notifyAreaOptions" 
                :key="item.code" 
                :label="item.name" 
                :value="item.code" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="报警描述" prop="alarmDesc">
            <el-input
              v-model="formData.alarmDesc"
              type="textarea"
              :rows="3"
              placeholder="请输入报警描述"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  savePublicAlarm,
  updatePublicAlarm,
} from '@/api/comprehensive'
import { ALARM_SOURCE_OPTIONS, ALARM_STATUS_OPTIONS, URGENT_LEVEL_OPTIONS,RELATED_BUSINESS_OPTIONS } from '@/constants/comprehensive'
import { getDeptListTree } from '@/api/system'
import { AREA_OPTIONS } from '@/constants/gas'
import { collectShow } from "@/hooks/gishooks"
import bus from '@/utils/mitt'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增报警上报信息',
    edit: '编辑报警上报信息',
    view: '报警详情'
  }
  return titles[props.mode] || '报警信息'
})

// 下拉选项数据
const alarmSourceOptions = ref(ALARM_SOURCE_OPTIONS)
const alarmStatusOptions = ref(ALARM_STATUS_OPTIONS)
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)
const urgentLevelOptions = ref(URGENT_LEVEL_OPTIONS)
const deptTreeOptions = ref([])
const areaOptions = ref(AREA_OPTIONS)
const notifyAreaOptions = ref([])

// 表单数据
const formData = reactive({
  id: '',
  alarmTitle: '',
  alarmCode: '',
  alarmSource: '',
  alarmSourceName: '',
  alarmStatus: '',
  alarmStatusName: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  urgentLevel: '',
  urgentLevelName: '',
  alarmTime: '',
  reportTime: '',
  reportPerson: '',
  reportContact: '',
  handleUnit: '',
  handleUnitName: '',
  notifyArea: [],
  notifyAreaName: '',
  address: '',
  longitude: '',
  latitude: '',
  alarmDesc: '',
  remark: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
})

// 表单验证规则
const formRules = {
  alarmTitle: [{ required: true, message: '请输入报警标题', trigger: 'blur' }],
  alarmCode: [{ required: true, message: '请输入报警编号', trigger: 'blur' }],
  alarmSource: [{ required: true, message: '请选择报警来源', trigger: 'change' }],
  alarmStatus: [{ required: true, message: '请选择报警状态', trigger: 'change' }],
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  urgentLevel: [{ required: true, message: '请选择紧急程度', trigger: 'change' }],
  alarmTime: [{ required: true, message: '请选择发生报警时间', trigger: 'change' }],
  reportPerson: [{ required: true, message: '请输入上报人员', trigger: 'blur' }],
  reportContact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  alarmDesc: [{ required: true, message: '请输入报警描述', trigger: 'blur' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'notifyArea') {
      formData[key] = []
    } else {
      formData[key] = ''
    }
  })
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        if (key === 'notifyArea' && newVal[key]) {
          // 处理通报区域，转换为数组
          formData[key] = typeof newVal[key] === 'string' ? newVal[key].split(',') : newVal[key]
        } else {
          formData[key] = newVal[key]
        }
      }
    })
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 处理报警来源变化
const handleAlarmSourceChange = (value) => {
  const selected = alarmSourceOptions.value.find(item => item.value === value)
  if (selected) {
    formData.alarmSourceName = selected.label
  }
}

// 处理报警状态变化
const handleAlarmStatusChange = (value) => {
  const selected = alarmStatusOptions.value.find(item => item.value === value)
  if (selected) {
    formData.alarmStatusName = selected.label
  }
}

// 处理所属行业变化
const handleRelatedBusinessChange = (value) => {
  const selected = relatedBusinessOptions.value.find(item => item.value === value)
  if (selected) {
    formData.relatedBusinessName = selected.label
  }
}

// 处理紧急程度变化
const handleUrgentLevelChange = (value) => {
  const selected = urgentLevelOptions.value.find(item => item.value === value)
  if (selected) {
    formData.urgentLevelName = selected.label
  }
}

// 处理部门选择变化
const handleDeptChange = (value, option) => {
  if (option) {
    formData.handleUnitName = option.deptName
  }
}

// 处理通报区域变化
const handleNotifyAreaChange = (values) => {
  const selectedNames = values.map(code => {
    const area = notifyAreaOptions.value.find(item => item.code === code)
    return area ? area.name : ''
  }).filter(name => name)
  
  formData.notifyAreaName = selectedNames.join(',')
}

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1]
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town)
    if (selectedArea) {
      formData.townName = selectedArea.name
    }
  }
}

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code)
      if (found) return found
    }
  }
  return null
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptListTree({})
    if (res && res.status === 200) {
      deptTreeOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取部门树失败', error)
  }
}

// 初始化通报区域选项
const initNotifyAreaOptions = () => {
  // 取东明县的children作为通报区域选项
  const dongming = areaOptions.value.find(area => area.name === '东明县')
  if (dongming && dongming.children) {
    notifyAreaOptions.value = dongming.children
  }
}

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true
  bus.off("getCollectLocation", handleCollectLocation)
  bus.on("getCollectLocation", handleCollectLocation)
}

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude
    formData.latitude = params.latitude
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }
    
    // 处理通报区域，转换为逗号分隔的字符串
    if (submitData.notifyArea && Array.isArray(submitData.notifyArea)) {
      submitData.notifyArea = submitData.notifyArea.join(',')
    }

    let res
    if (props.mode === 'add') {
      res = await savePublicAlarm(submitData)
    } else if (props.mode === 'edit') {
      res = await updatePublicAlarm(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation)
})

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree()
  initNotifyAreaOptions()
})
</script>

<style scoped>
.public-alarm-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 