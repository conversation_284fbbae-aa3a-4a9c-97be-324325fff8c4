<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="alarm-handle-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="处置状态" prop="handleStatus">
        <el-radio-group v-model="formData.handleStatus">
          <el-radio :label="94001" size="large">处置中</el-radio>
          <el-radio :label="94002" size="large">已处置</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="处置描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入处置描述"
        />
      </el-form-item>

      <el-form-item label="处置时间" prop="handleTime">
        <el-date-picker
          v-model="formData.handleTime"
          type="datetime"
          placeholder="请选择处置时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="处置人员" prop="handleUser">
        <el-input v-model="formData.handleUser" placeholder="请输入处置人员" />
      </el-form-item>

      <el-form-item label="处置人单位" prop="unit">
        <el-input v-model="formData.unit" placeholder="请输入处置人单位" />
      </el-form-item>

      <el-form-item label="处置照片">
        <template v-if="mode !== 'view'">
          <el-upload
            class="upload-demo"
            :http-request="handlePhotoUpload"
            :file-list="photoFileList"
            :on-error="handleUploadError"
            :on-remove="handlePhotoRemove"
            :limit="3"
            list-type="picture-card"
            :auto-upload="true"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">最多上传3张照片，支持jpg、png格式，单张不超过5MB</div>
            </template>
          </el-upload>
        </template>
        <template v-else>
          <div class="photo-preview">
            <el-image
              v-for="(photo, index) in photoPreviewList"
              :key="index"
              :src="photo"
              :preview-src-list="photoPreviewList"
              style="width: 100px; height: 100px; margin-right: 10px"
              fit="cover"
            />
          </div>
        </template>
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'" :loading="submitLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { saveOrUpdateAlarmHandle } from '@/api/gas';
import { uploadFile } from '@/api/upload';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  alarmData: {
    type: Object,
    default: () => ({})
  },
  handleData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增处置',
    edit: '编辑处置',
    view: '处置详情'
  };
  return titles[props.mode] || '处置';
});

// 提交加载状态
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  alarmId: '',
  handleStatus: 94001, // 默认处置中
  description: '',
  handleTime: '',
  handleUser: '',
  unit: '',
  picUrls: '',
  remarks: ''
});

// 照片文件列表
const photoFileList = ref([]);
const photoPreviewList = ref([]);

// 表单验证规则
const formRules = {
  handleStatus: [
    { required: true, message: '请选择处置状态', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入处置描述', trigger: 'blur' }
  ],
  handleTime: [
    { required: true, message: '请选择处置时间', trigger: 'change' }
  ],
  handleUser: [
    { required: true, message: '请输入处置人员', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入处置人单位', trigger: 'blur' }
  ]
};

// 照片上传处理
const handlePhotoUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      // 添加到照片列表
      const currentUrls = formData.picUrls ? formData.picUrls.split(',').filter(Boolean) : [];
      currentUrls.push(res.data.url);
      formData.picUrls = currentUrls.join(',');
      
      // 更新文件列表显示
      photoFileList.value.push({ 
        name: options.file.name, 
        url: res.data.url,
        status: 'success'
      });
      
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传照片失败:', error);
    ElMessage.error('上传失败');
  }
};

// 照片删除处理
const handlePhotoRemove = (file) => {
  const currentUrls = formData.picUrls ? formData.picUrls.split(',').filter(Boolean) : [];
  const index = currentUrls.indexOf(file.url);
  if (index > -1) {
    currentUrls.splice(index, 1);
    formData.picUrls = currentUrls.join(',');
  }
};

// 上传错误处理
const handleUploadError = () => {
  ElMessage.error('上传失败');
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置数据
  Object.keys(formData).forEach(key => {
    if (key === 'handleStatus') {
      formData[key] = 94001;
    } else {
      formData[key] = '';
    }
  });
  photoFileList.value = [];
  photoPreviewList.value = [];
  submitLoading.value = false;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitLoading.value = true;
    
    // 准备提交数据
    const submitData = {
      ...formData,
      alarmId: props.alarmData.id
    };
    
    // 如果是编辑模式且没有id，说明数据有误
    if (props.mode === 'edit' && !submitData.id) {
      ElMessage.error('编辑数据有误');
      return;
    }
    
    // 如果是新增模式，删除id字段
    if (props.mode === 'add') {
      delete submitData.id;
    }
    
    const res = await saveOrUpdateAlarmHandle(submitData);
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.message || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('处置提交失败:', error);
    ElMessage.error('提交失败');
  } finally {
    submitLoading.value = false;
  }
};

// 监听props变化，初始化表单数据
watch(() => [props.visible, props.handleData], ([visible, handleData]) => {
  if (visible) {
    if (props.mode === 'edit' || props.mode === 'view') {
      // 编辑或查看模式，填充数据
      Object.keys(formData).forEach(key => {
        if (handleData[key] !== undefined) {
          formData[key] = handleData[key];
        }
      });
      
      // 处理时间字段映射
      if (handleData.createTime && !formData.handleTime) {
        formData.handleTime = handleData.createTime;
      }
      
      // 处理照片显示
      if (handleData.picUrls) {
        const urls = handleData.picUrls.split(',').filter(Boolean);
        photoPreviewList.value = urls;
        
        if (props.mode === 'edit') {
          photoFileList.value = urls.map((url, index) => ({
            name: `照片${index + 1}`,
            url: url,
            status: 'success'
          }));
        }
      }
    } else {
      // 新增模式，重置数据
      Object.keys(formData).forEach(key => {
        if (key === 'handleStatus') {
          formData[key] = 94001;
        } else if (key === 'alarmId') {
          formData[key] = props.alarmData.id || '';
        } else {
          formData[key] = '';
        }
      });
      photoFileList.value = [];
      photoPreviewList.value = [];
    }
  }
}, { immediate: true, deep: true });
</script>

<style scoped>
.alarm-handle-form-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo {
  :deep(.el-upload-list) {
    width: 100%;
  }
}

.photo-preview {
  display: flex;
  flex-wrap: wrap;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}
</style> 