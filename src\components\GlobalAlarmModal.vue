<template>
  <teleport to="body">
    <transition name="fade">
            <div v-if="showModal && latestMessage" class="global-alarm-modal-overlay">
        <div class="global-alarm-modal" :class="modalClass">
          <!-- 弹窗头部 -->
          <div class="modal-header">
            <div class="header-left">
              <div class="header-icon">
                <SvgIcon :raw="alarmIconSvg" color="#FF4949" size="20px" />
              </div>
              <div class="header-title">
                <span class="title-text">全局报警消息</span>
              </div>
            </div>
            <div class="header-actions">
              <button class="action-btn close-btn" @click="closeModal">
                <SvgIcon :raw="closeIconSvg" size="16px" />
              </button>
            </div>
          </div>

          <!-- 弹窗内容 -->
          <div class="modal-body">
            <div class="modal-content" v-if="latestMessage">
              <div 
                class="alarm-item"
                :class="{ 'unread': !latestMessage.isRead }"
              >
                <div class="alarm-icon">
                  <div class="level-indicator" :class="getAlarmLevelClass(latestMessage.level)">
                    <SvgIcon :raw="getAlarmLevelIcon(latestMessage.level)" size="16px" />
                  </div>
                </div>
                
                <div class="alarm-content">
                  <div class="alarm-header">
                    <span class="alarm-type">{{ getSpecialTypeName(getSpecialTypeByMessage(latestMessage)) }}</span>
                    <span class="alarm-time">{{ formatTime(latestMessage.time || latestMessage.receivedAt) }}</span>
                  </div>
                  <div class="alarm-message">{{ latestMessage.content }}</div>
                  <div class="alarm-details">
                    <span class="device-info">设备: {{ latestMessage.deviceId }}</span>
                    <span v-if="latestMessage.value" class="alarm-value">值: {{ latestMessage.value }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="modal-footer">
              <div class="footer-info">
                <span>{{ countdown }} 秒后自动关闭</span>
              </div>
              <div class="footer-actions">
                <button class="footer-btn view-btn" @click="handleViewClick">
                  <SvgIcon :raw="viewIconSvg" size="14px" />
                  查看
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { computed, watch, ref, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAlarmStore } from '@/stores/alarm'
import SvgIcon from '@/components/SvgIcon.vue'
import bus from '@/utils/mitt'

// Props
const props = defineProps({
  // 界面模式：screen(大屏) 或 admin(管理端)
  mode: {
    type: String,
    default: 'screen',
    validator: (value) => ['screen', 'admin'].includes(value)
  }
})

// 路由
const router = useRouter()

// Store
const alarmStore = useAlarmStore()

// 计算属性
const latestMessage = computed(() => alarmStore.latestMessage)
const showModal = computed(() => alarmStore.showGlobalAlarmModal && latestMessage.value)
const isScreenMode = computed(() => props.mode === 'screen')
const modalClass = computed(() => ({
  'screen-mode': isScreenMode.value,
  'admin-mode': !isScreenMode.value
}))

// Countdown logic
const countdown = ref(30)
let timer = null

const stopCountdown = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

const startCountdown = () => {
  stopCountdown()
  countdown.value = 30
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      closeModal()
    }
  }, 1000)
}

// 方法
const closeModal = () => {
  alarmStore.closeGlobalModal()
}

const handleViewClick = () => {
  if (latestMessage.value) {
    handleGotoDetail(latestMessage.value)
  }
}

// 根据消息获取专项类型
const getSpecialTypeByMessage = (message) => {
  return alarmStore.getSpecialTypeByType(message.type)
}

// 获取专项中文名称
const getSpecialTypeName = (type) => {
  return alarmStore.getSpecialTypeName(type)
}

// 处理跳转详情
const handleGotoDetail = (message) => {
  const specialType = getSpecialTypeByMessage(message)
  
  if (isScreenMode.value) {
    handleScreenNavigation(message, specialType)
  } else {
    handleAdminNavigation(message, specialType)
  }
  
  alarmStore.markAsRead(message.id)
  closeModal()
}

// 打开设备详情弹窗
// const openDetailModal = (message, specialType) => {
//   console.log('打开详情弹窗:', message,specialType)
//   const params = {
//     specialType, // 燃气:gas; 排水:drainage; 供暖:heating; 桥梁:bridge;
//     deviceId: message.deviceId, // 设备ID
//     deviceType: message.type, // 设备类型
//   }
//   bus.emit('screenTableRowFocusToGisPoint', params)
// }

// 大屏端导航处理
const handleScreenNavigation = (message, specialType) => {
  let targetRoute = ''

  switch (specialType) {
    case 'gas':
      targetRoute = '/gas/monitoring'
      break
    case 'drainage':
      targetRoute = '/drainage/monitoring'
      break
    case 'heating':
      targetRoute = '/heating/monitoring'
      break
    case 'bridge':
      targetRoute = '/bridge'
      break
  }

  router.push(targetRoute).then(() => {
    if (specialType !== 'bridge') {
      setTimeout(() => {
        const params = {
          specialType,
          deviceId: message.deviceId,
          deviceType: message.type
        }
        bus.emit('screenTableRowFocusToGisPoint', params)

        // 额外调用打开设备详情弹窗
        // setTimeout(() => {
        //   openDetailModal(message, specialType)
        // }, 200)
      }, 500)
    }
  })
}

// 管理端导航处理
const handleAdminNavigation = (message, specialType) => {
  let targetRoute = ''
  
  switch (specialType) {
    case 'drainage':
      targetRoute = '/drainage/monitoringMis/alarm/info'
      break
    case 'heating':
      targetRoute = '/heating/monitoringMis/warning/network'
      break
    case 'bridge':
      targetRoute = '/bridge/alarm/management/info'
      break
    case 'gas':
      targetRoute = '/gas/leak/monitor/alert'
      break
  }
  
  router.push(targetRoute)
}

// 获取报警等级样式类
const getAlarmLevelClass = (level) => {
  switch (level) {
    case '1': case 'high': case 'critical': return 'level-critical'
    case '2': case 'medium': case 'warning': return 'level-warning'
    case '3': case 'low': case 'info': return 'level-info'
    default: return 'level-default'
  }
}

// 获取报警等级图标
const getAlarmLevelIcon = (level) => {
  return `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0ZM8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C11.5899 1.5 14.5 4.41015 14.5 8C14.5 11.5899 11.5899 14.5 8 14.5Z" fill="currentColor"/><path d="M7.25 3.5V8.75H10.75V7.25H8.75V3.5H7.25Z" fill="currentColor"/></svg>`
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return date.toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })
}

// SVG 图标定义
const alarmIconSvg = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 2C5.79086 2 4 3.79086 4 6V7.5L2.5 9V11H13.5V9L12 7.5V6C12 3.79086 10.2091 2 8 2Z" fill="currentColor"/><path d="M6.5 12.5C6.5 13.6046 7.39543 14.5 8.5 14.5C9.60457 14.5 10.5 13.6046 10.5 12.5" stroke="currentColor" stroke-width="1"/></svg>`
const closeIconSvg = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" fill="currentColor"/></svg>`
const viewIconSvg = `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 3C4.5 3 1.73 5.11 1 8c.73 2.89 3.5 5 7 5s6.27-2.11 7-5c-.73-2.89-3.5-5-7-5zM8 11.5c-1.93 0-3.5-1.57-3.5-3.5S6.07 4.5 8 4.5s3.5 1.57 3.5 3.5S9.93 11.5 8 11.5zM8 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="currentColor"/></svg>`

// 监听
watch(showModal, (newVal) => {
  if (newVal) {
    document.addEventListener('keydown', handleKeydown)
    startCountdown()
  } else {
    stopCountdown()
    document.removeEventListener('keydown', handleKeydown)
  }
})

const handleKeydown = (e) => {
  if (e.key === 'Escape') {
    closeModal()
  }
}

onUnmounted(() => {
  stopCountdown()
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 基础样式 */
.global-alarm-modal-overlay {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  bottom: auto;
  background: transparent;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 9999;
  pointer-events: none;
}

.global-alarm-modal {
  pointer-events: auto;
  background: white;
  border-radius: 8px;
  width: 95%;
  max-width: 1200px; /* 增加最大宽度 */
  min-width: 800px; /* 设置最小宽度 */
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transform: scale(1);
  transition: all 0.3s ease;
}

/* 大屏模式样式 */
.global-alarm-modal.screen-mode {
  background: linear-gradient(180deg, #1A2332 0%, #0F1419 100%);
  border: 1px solid rgba(0, 163, 255, 0.3);
  color: #FFFFFF;
}

.global-alarm-modal.screen-mode .modal-header {
  background: linear-gradient(90deg, rgba(0, 163, 255, 0.2) 0%, rgba(0, 163, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 163, 255, 0.2);
}

.global-alarm-modal.screen-mode .alarm-item {
  background: rgba(0, 163, 255, 0.05);
  border: 1px solid rgba(0, 163, 255, 0.1);
}

.global-alarm-modal.screen-mode .alarm-item.unread {
  background: rgba(255, 109, 40, 0.1);
  border-color: rgba(255, 109, 40, 0.3);
}

/* 管理端模式样式 */
.global-alarm-modal.admin-mode {
  background: #FFFFFF;
  color: #333333;
}

.global-alarm-modal.admin-mode .modal-header {
  background: #F8F9FA;
  border-bottom: 1px solid #E9ECEF;
}

.global-alarm-modal.admin-mode .alarm-item {
  background: #FFFFFF;
  border: 1px solid #E9ECEF;
}

.global-alarm-modal.admin-mode .alarm-item.unread {
  background: rgba(255, 193, 7, 0.1);
  border-color: #FFC107;
}

/* 头部样式 */
.modal-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px 8px 0 0;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 73, 73, 0.1);
}

.header-title .title-text {
  font-size: 16px;
  font-weight: 600;
}

.header-actions .close-btn {
  background: transparent;
  color: currentColor;
  opacity: 0.7;
  padding: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.header-actions .close-btn:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* 新增 body 样式 */
.modal-body {
  display: flex;
  flex-direction: column;
  padding: 16px 20px;
  gap: 12px;
}

/* 内容样式 */
.modal-content {
  padding: 0;
  flex: 1;
}

.alarm-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  position: relative;
  flex: 1;
}

.alarm-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: #FF6D28;
  border-radius: 0 2px 2px 0;
}

.alarm-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.level-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-critical { background: rgba(252, 73, 73, 0.1); color: #FC4949; border: 1px solid rgba(252, 73, 73, 0.3); }
.level-warning { background: rgba(255, 199, 90, 0.1); color: #FFC75A; border: 1px solid rgba(255, 199, 90, 0.3); }
.level-info { background: rgba(59, 130, 246, 0.1); color: #3B82F6; border: 1px solid rgba(59, 130, 246, 0.3); }
.level-default { background: rgba(156, 163, 175, 0.1); color: #9CA3AF; border: 1px solid rgba(156, 163, 175, 0.3); }

.alarm-content {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 20px;
}

.alarm-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 0;
  flex-shrink: 0;
}

.alarm-type {
  font-size: 12px;
  font-weight: 500;
  color: #0066CC;
  background: rgba(0, 102, 204, 0.1);
  padding: 4px 8px;
  border-radius: 3px;
  white-space: nowrap;
  flex-shrink: 0;
}

.screen-mode .alarm-type {
  color: #3AA1FF;
  background: rgba(58, 161, 255, 0.2);
}

.alarm-time {
  font-size: 11px;
  opacity: 0.6;
  white-space: nowrap;
  flex-shrink: 0;
}

.alarm-message {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 200px;
}

.alarm-details {
  font-size: 12px;
  opacity: 0.7;
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  flex-shrink: 0;
}

.alarm-details .device-info,
.alarm-details .alarm-value {
  white-space: nowrap;
  flex-shrink: 0;
}

/* 底部样式 */
.modal-footer {
  padding: 12px 0 0 0;
  border-top: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  gap: 20px;
  margin-top: auto;
}

.admin-mode .modal-footer { border-top-color: #E9ECEF; }

.footer-info {
  font-size: 12px;
  opacity: 0.7;
  flex: 1;
  display: flex;
  align-items: center;
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  flex-shrink: 0;
  min-width: 80px;
  justify-content: center;
}

.view-btn {
  background: #0066CC;
  color: white;
  border-color: #0066CC;
}
.view-btn:hover { background: #0052A3; }

.screen-mode .view-btn {
  background: #00A3FF;
  border-color: #00A3FF;
}
.screen-mode .view-btn:hover { background: #0082CC; }


/* 过渡动画 */
.fade-enter-active, .fade-leave-active { transition: opacity 0.3s ease, transform 0.3s ease; }
.fade-enter-from, .fade-leave-to { opacity: 0; transform: scale(0.9); }

/* 响应式设计 */
@media (max-width: 1024px) {
  .global-alarm-modal {
    width: 95%;
    min-width: 600px;
  }

  .alarm-content {
    gap: 12px;
  }

  .alarm-details {
    gap: 12px;
  }

  .footer-btn {
    padding: 6px 16px;
    min-width: 70px;
  }
}

@media (max-width: 768px) {
  .global-alarm-modal {
    width: 95%;
    min-width: 400px;
  }

  .alarm-message {
    min-width: 150px;
  }

  .modal-footer {
    gap: 12px;
  }

  .footer-btn {
    padding: 6px 12px;
    min-width: 60px;
    font-size: 13px;
  }

  .footer-info {
    font-size: 11px;
  }
}
</style>