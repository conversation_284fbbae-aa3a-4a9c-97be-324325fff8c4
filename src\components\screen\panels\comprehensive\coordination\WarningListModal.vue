<template>
  <teleport to="body">
    <transition name="fade">
      <div class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">预警列表</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          
          <!-- 查询条件区域 -->
          <div class="modal-filters">
            <el-form :model="filters" class="filter-form">
              <div class="form-row">
                <el-form-item label="预警编号:" class="form-item-full">
                  <el-input v-model="filters.warningCode" placeholder="请输入报警编号" />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="所属专项:" class="form-item-third">
                  <CommonSelect v-model="filters.relatedBusiness" :options="businessOptions" placeholder="全部" popper-class="screen-dialog-popper" />
                </el-form-item>
                <el-form-item label="预警等级:" class="form-item-third">
                  <CommonSelect v-model="filters.warningLevel" :options="levelOptions" placeholder="全部" popper-class="screen-dialog-popper" />
                </el-form-item>
                <el-form-item label="预警状态:" class="form-item-third">
                  <CommonSelect v-model="filters.warningStatus" :options="statusOptions" placeholder="全部" popper-class="screen-dialog-popper" />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="预警时间:" class="form-item-time">
                  <el-date-picker
                    v-model="filters.timeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :clearable="true"
                    popper-class="screen-dialog-popper"
                  />
                </el-form-item>
                <el-form-item class="form-item-buttons">
                  <el-button @click="handleSearch" type="primary">查询</el-button>
                  <el-button @click="handleReset">重置</el-button>
                </el-form-item>
              </div>
            </el-form>
          </div>

          <!-- 数据表格区域 -->
          <div class="modal-content" v-loading="loading" element-loading-background="rgba(0, 22, 72, 0.5)">
            <div class="table-container">
              <el-table :data="warningList" style="width: 100%" height="100%">
                <!-- <el-table-column type="selection" width="55" /> -->
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="warningTypeName" label="预警类型" />
                <el-table-column prop="warningCode" label="预警编号" width="160" />
                <el-table-column prop="warningLevelName" label="预警等级">
                  <template #default="scope">
                    <span :class="getLevelClass(scope.row.warningLevelName)">{{ scope.row.warningLevelName || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="relatedBusinessName" label="所属专项" />
                <el-table-column prop="warningTime" label="预警时间" :formatter="row => formatTime(row.warningTime || row.publishTime)" width="180" />
                <el-table-column label="操作" width="120" align="center">
                  <template #default="scope">
                    <div class="action-buttons">
                      <div class="action-btn" @click="viewWarningDetail(scope.row)" title="查看详情">
                        <div class="view-icon"></div>
                      </div>
                      <div class="action-btn" @click="locateWarning(scope.row)" title="定位">
                        <div class="location-icon"></div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <NoData v-if="!loading && warningList.length === 0" />
            </div>
            
            <!-- 分页组件 -->
            <div v-if="warningList.length > 0" class="pagination-container">
               <el-pagination
                v-if="pagination.total > 0"
                v-model:current-page="pagination.current"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="changePage"
              />
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>

  <!-- 预警详情弹窗 -->
  <WarningDetailModal
    v-if="showDetailModal"
    :warning-id="selectedWarningId"
    @close="closeDetailModal"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import moment from 'moment'
import { getWarningInfoPageScreen } from '@/api/comprehensive.js'
import NoData from '@/components/common/NoData.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import WarningDetailModal from './WarningDetailModal.vue'

const emit = defineEmits(['close'])

const businessOptions = [
  { label: '全部', value: '' },
  { label: '燃气', value: 7000501 },
  { label: '排水', value: 7000502 },
  { label: '供热', value: 7000503 },
  { label: '桥梁', value: 7000504 },
];

const levelOptions = [
  { label: '全部', value: '' },
  { label: '一级预警', value: 7002201 },
  { label: '二级预警', value: 7002202 },
  { label: '三级预警', value: 7002203 },
];

const statusOptions = [
  { label: '全部', value: '' },
  { label: '待处置', value: 7002301 },
  { label: '处置中', value: 7002302 },
  { label: '已处置', value: 7002303 },
  { label: '已解除', value: 7002304 },
];

// 筛选条件
const filters = ref({
  warningCode: '',
  relatedBusiness: '',
  warningLevel: '',
  warningStatus: '',
  timeRange: []
})

// 加载状态
const loading = ref(false)

// 分页信息
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 预警数据列表
const warningList = ref([])

// 详情弹窗相关
const showDetailModal = ref(false)
const selectedWarningId = ref('')

/**
 * 关闭弹窗
 */
const closeModal = () => {
  emit('close')
}

/**
 * 获取预警信息列表
 */
const fetchWarningList = async () => {
  try {
    loading.value = true
    
    const params = {
      warningLevel: filters.value.warningLevel,
      warningStatus: filters.value.warningStatus,
      relatedBusiness: filters.value.relatedBusiness,
      warningCode: filters.value.warningCode,
      startTime: filters.value.timeRange?.[0] ? moment(filters.value.timeRange[0]).format('YYYY-MM-DD HH:mm:ss') : '',
      endTime: filters.value.timeRange?.[1] ? moment(filters.value.timeRange[1]).format('YYYY-MM-DD HH:mm:ss') : ''
    }
    
    const response = await getWarningInfoPageScreen(
      pagination.value.current,
      pagination.value.pageSize,
      params
    )
    
    if (response.code === 200 && response.data) {
      const { records, total } = response.data
      warningList.value = processWarningData(records || [])
      pagination.value.total = total || 0
    }
  } catch (error) {
    console.error('获取预警列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理预警数据格式
 */
const processWarningData = (records) => {
  return records.map(item => ({
    id: item.id,
    warningCode: item.warningCode || '',
    warningTypeName: item.warningTypeName || '',
    warningLevelName: item.warningLevelName || '',
    relatedBusinessName: item.relatedBusinessName || '',
    warningTime: item.warningTime || item.publishTime,
    warningLocation: item.warningLocation,
    originalData: item
  }))
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.value.current = 1
  fetchWarningList()
}

/**
 * 重置筛选条件
 */
const handleReset = () => {
  filters.value = {
    warningCode: '',
    warningStatus: '',
    warningLevel: '',
    relatedBusiness: '',
    timeRange: []
  }
  pagination.value.current = 1
  fetchWarningList()
}

/**
 * 切换页码
 */
const changePage = (page) => {
  pagination.value.current = page
  fetchWarningList()
}

const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  fetchWarningList()
}

/**
 * 获取预警级别样式类
 */
const getLevelClass = (level) => {
  const classMap = {
    '一级': 'level-one',
    '二级': 'level-two', 
    '三级': 'level-three'
  }
  return classMap[level] || 'level-three'
}

/**
 * 格式化时间
 */
const formatTime = (time) => {
  if (!time) return '-'
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 查看预警详情
 */
const viewWarningDetail = (item) => {
  selectedWarningId.value = item.id
  showDetailModal.value = true
}

/**
 * 关闭详情弹窗
 */
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedWarningId.value = ''
}

/**
 * 定位预警
 */
const locateWarning = (item) => {
  console.log('定位预警:', item)
}

onMounted(() => {
  fetchWarningList()
})
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 900px;
  max-height: 80vh;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-filters {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.modal-content {
  flex: 1;
  padding: 15px 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: auto;
}

.level-one { color: #FF3900; }
.level-two { color: #FF6817; }
.level-three { color: #FFD32E; }

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.action-btn {
  width: 24px;
  height: 24px;
  cursor: pointer;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1);
  }
}

.view-icon {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkMxMS4zIDIgMTQgNC43IDE0IDhTMTEuMyAxNCA4IDE0IDIgMTEuMyAyIDggNC43IDIgOCAyWk04IDEyQzEwLjIgMTIgMTIgMTAuMiAxMiA4UzEwLjIgNCA4IDQgNCA1LjggNCA4IDUuOCAxMiA4IDEyWiIgZmlsbD0iIzNBOERGMiIvPgo8cGF0aCBkPSJNOCA2QzYuOSA2IDYgNi45IDYgOFM2LjkgMTAgOCAxMCAxMCA5LjEgMTAgOCA5LjEgNiA4IDZaIiBmaWxsPSIjM0E4REYyIi8+Cjwvc3ZnPgo=') no-repeat center/cover;
}

.location-icon {
  width: 16px;
  height: 16px;
  background: url('@/assets/images/screen/common/location.svg') no-repeat center/cover;
}

.pagination-container {
  padding: 15px 0;
  border-top: 1px solid rgba(59, 141, 242, 0.3);
  margin-top: 15px;
  flex-shrink: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.filter-form {
  .form-row {
    display: flex;
    gap: 15px;
    align-items: flex-end;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-item-full {
    flex: 1;
  }

  .form-item-third {
    flex: 1;
    min-width: 0;
  }

  .form-item-time {
    flex: 2;
    min-width: 0;
  }

  .form-item-buttons {
    flex-shrink: 0;
    margin-left: auto;
  }
}

:deep(.el-form) {
  .el-form-item__label {
    color: #D3E5FF;
  }

  .el-select, .el-input__wrapper {
    background-color: rgba(0, 19, 47, 0.35) !important;
    box-shadow: none !important;
    border: 1px solid rgba(59, 141, 242, 0.5);
  }

  .el-button--primary {
    background: #1890FF;
    border-color: #1890FF;
    color: #FFFFFF;
  }
  .el-button {
    background: rgba(24, 144, 255, 0.1);
    border: 1px solid #1890FF;
    color: #1890FF;
  }
}

:deep(.el-date-editor.el-range-editor) {
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: none;

  .el-range-input {
    color: #fff;
    background: transparent;
     &::placeholder {
        color: rgba(255, 255, 255, 0.3);
    }
  }

  .el-range-separator {
    color: #fff;
  }
   .el-input__wrapper {
      background: transparent;
      box-shadow: none;
      border: none;
  }
}

:deep(.el-table) {
  background: transparent;
  color: #fff;
  --el-table-border-color: rgba(59, 141, 242, 0.2);
  --el-table-header-bg-color: rgba(0, 35, 91, 0.8);
  --el-table-tr-bg-color: transparent;
  --el-table-row-hover-bg-color: rgba(0, 163, 255, 0.2);
}

:deep(.el-table th.el-table__cell) {
    background-color: rgba(0, 35, 91, 0.8);
}

:deep(.el-pagination) {
  --el-pagination-text-color: rgba(255, 255, 255, 0.9);
  --el-pagination-button-disabled-bg-color: transparent;
  --el-pagination-button-bg-color: transparent;
  --el-pagination-bg-color: transparent;
  --el-pagination-border-radius: 2px;
  --el-pagination-input-height: 28px;

  .el-pagination__jump .el-input__wrapper {
    background-color: rgba(0, 19, 47, 0.35) !important;
    box-shadow: none !important;
    border: 1px solid rgba(59, 141, 242, 0.5);
  }
   .el-pagination__jump .el-input__inner {
    color: #fff;
  }
}
</style>