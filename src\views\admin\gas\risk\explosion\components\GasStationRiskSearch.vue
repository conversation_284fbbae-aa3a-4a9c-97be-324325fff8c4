<template>
  <div class="gas-station-risk-search">
    <el-form :model="formData" label-width="100px" :inline="true" size="default">
      <el-form-item label="风险编码">
        <el-input v-model="formData.riskCode" placeholder="请输入风险编码" clearable size="small"/>
      </el-form-item>
      
      <el-form-item label="风险等级">
        <el-select v-model="formData.riskLevel" placeholder="请选择风险等级" clearable>
          <el-option
            v-for="item in riskLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="管控状态">
        <el-select v-model="formData.stationStatus" placeholder="请选择管控状态" clearable>
          <el-option
            v-for="item in controlStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="评估时间">
        <el-date-picker
          v-model="formData.assessmentDate"
          type="date"
          placeholder="请选择评估时间"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="评估人">
        <el-input v-model="formData.assessor" placeholder="请输入评估人" clearable size="small"/>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { RISK_LEVEL, CONTROL_STATUS } from '@/constants/gas';

const emit = defineEmits(['search', 'reset']);

// 表单数据
const formData = reactive({
  riskCode: '',
  riskLevel: '',
  stationStatus: '',
  assessmentDate: '',
  assessor: ''
});

// 风险等级选项
const riskLevelOptions = [
  { label: '重大风险', value: RISK_LEVEL.CRITICAL_RISK },
  { label: '较大风险', value: RISK_LEVEL.MAJOR_RISK },
  { label: '一般风险', value: RISK_LEVEL.NORMAL_RISK },
  { label: '低风险', value: RISK_LEVEL.LOW_RISK }
];

// 管控状态选项
const controlStatusOptions = [
  { label: '无需管控', value: CONTROL_STATUS.NO_CONTROL_NEEDED },
  { label: '未管控', value: CONTROL_STATUS.UNCONTROLLED },
  { label: '已管控', value: CONTROL_STATUS.CONTROLLED }
];

// 处理搜索
const handleSearch = () => {
  emit('search', { ...formData });
};

// 处理重置
const handleReset = () => {
  // 重置表单
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  emit('reset');
};
</script>

<style scoped>  
.gas-station-risk-search {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

:deep(.el-input),
:deep(.el-select) {
  width: 220px;
}

:deep(.el-date-editor) {
  width: 220px;
}

:deep(.el-form-item__label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}
</style>