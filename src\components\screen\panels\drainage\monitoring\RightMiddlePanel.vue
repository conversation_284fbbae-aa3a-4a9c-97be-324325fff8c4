<template>
  <PanelBox title="报警趋势分析">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-icon total"></span>
          <span class="legend-text">总数</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-one"></span>
          <span class="legend-text">一级报警</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-two"></span>
          <span class="legend-text">二级报警</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-three"></span>
          <span class="legend-text">三级报警</span>
        </div>
      </div>
      <div class="chart-title-container">
        <span class="unit-label">单位（个）</span>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import moment from 'moment'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getMonitorAnalysisTrendStatistics } from '@/api/drainage.js'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围对应的dayIndex映射
const getTimeRangeDayIndex = (range) => {
  const mapping = {
    week: 7,
    month: 30,
    year: 165
  }
  return mapping[range] || 7
}

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 预设颜色
const colors = {
  total: '#055ADB',
  levelOne: '#FF311D',
  levelTwo: '#FF6817',
  levelThree: '#FFD32E'
}

// 图表数据
const chartData = ref({
  xAxis: [],
  series: [
    {
      name: '总数',
      data: [],
      color: colors.total
    },
    {
      name: '一级报警',
      data: [],
      color: colors.levelOne
    },
    {
      name: '二级报警',
      data: [],
      color: colors.levelTwo
    },
    {
      name: '三级报警',
      data: [],
      color: colors.levelThree
    }
  ]
})

// 获取趋势统计数据
const fetchTrendStatistics = async () => {
  try {
    const dayIndex = getTimeRangeDayIndex(timeRange.value)
    const response = await getMonitorAnalysisTrendStatistics(dayIndex)

    if (response.code === 200 && response.data && response.data.statistics) {
      const statistics = response.data.statistics

      // 如果接口返回数据为空，按照最近一周时间数据补0
      if (statistics.length === 0) {
        generateEmptyData()
        return
      }

      // 处理接口返回的数据
      const xAxisData = []
      const totalData = []
      const levelOneData = []
      const levelTwoData = []
      const levelThreeData = []

      statistics.forEach(item => {
        // 格式化日期显示
        const date = moment(item.date)
        let label = ''
        if (timeRange.value === 'week') {
          const dayOfWeek = date.day() // 0-6，0是周日
          const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
          label = weekDays[dayOfWeek]
        } else if (timeRange.value === 'month') {
          label = date.format('DD') + '日'
        } else {
          label = date.format('MM') + '月'
        }

        xAxisData.push(label)
        totalData.push(item.totalCount || 0)
        levelOneData.push(item.level1Count || 0)
        levelTwoData.push(item.level2Count || 0)
        levelThreeData.push(item.level3Count || 0)
      })

      // 更新图表数据
      chartData.value.xAxis = xAxisData
      chartData.value.series[0].data = totalData
      chartData.value.series[1].data = levelOneData
      chartData.value.series[2].data = levelTwoData
      chartData.value.series[3].data = levelThreeData
    } else {
      // 接口返回异常，生成空数据
      generateEmptyData()
    }
  } catch (error) {
    console.error('获取趋势统计数据失败:', error)
    // 出错时生成空数据
    generateEmptyData()
  }
}

// 生成空数据（当接口返回为空或出错时）
const generateEmptyData = () => {
  const xAxisData = []
  const emptyData = []

  // 根据时间范围生成对应的空数据
  let dataPoints = 7 // 默认一周
  if (timeRange.value === 'month') {
    dataPoints = 30
  } else if (timeRange.value === 'year') {
    dataPoints = 12
  }

  for (let i = 1; i <= dataPoints; i++) {
    let label = ''
    if (timeRange.value === 'week') {
      label = i === 7 ? '周日' : `周${i}`
    } else if (timeRange.value === 'month') {
      label = `${i}日`
    } else {
      label = `${i}月`
    }
    xAxisData.push(label)
    emptyData.push(0)
  }

  // 更新图表数据为0
  chartData.value.xAxis = xAxisData
  chartData.value.series[0].data = [...emptyData]
  chartData.value.series[1].data = [...emptyData]
  chartData.value.series[2].data = [...emptyData]
  chartData.value.series[3].data = [...emptyData]
}

// 处理时间范围变化
const handleTimeChange = async (value) => {
  console.log('时间范围变更为:', value)
  await fetchTrendStatistics()
  updateChart()
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '总数',
        type: 'line',
        data: chartData.value.series[0].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.total
        },
        lineStyle: {
          color: colors.total,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(5, 90, 219, 0.5)' },
              { offset: 1, color: 'rgba(5, 90, 219, 0)' }
            ]
          }
        }
      },
      {
        name: '一级报警',
        type: 'line',
        data: chartData.value.series[1].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.levelOne
        },
        lineStyle: {
          color: colors.levelOne,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 49, 29, 0.5)' },
              { offset: 1, color: 'rgba(255, 49, 29, 0)' }
            ]
          }
        }
      },
      {
        name: '二级报警',
        type: 'line',
        data: chartData.value.series[2].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.levelTwo
        },
        lineStyle: {
          color: colors.levelTwo,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 104, 23, 0.5)' },
              { offset: 1, color: 'rgba(255, 104, 23, 0)' }
            ]
          }
        }
      },
      {
        name: '三级报警',
        type: 'line',
        data: chartData.value.series[3].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.levelThree
        },
        lineStyle: {
          color: colors.levelThree,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 219, 35, 0.5)' },
              { offset: 1, color: 'rgba(255, 196, 35, 0)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: chartData.value.xAxis
    },
    series: [
      { data: chartData.value.series[0].data },
      { data: chartData.value.series[1].data },
      { data: chartData.value.series[2].data },
      { data: chartData.value.series[3].data }
    ]
  })
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  // 获取初始数据
  await fetchTrendStatistics()
  // 初始化图表
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.com-select {
  margin-right: 20px;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 20px;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  padding: 1px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 2px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.level-one {
  background: #FF311D;
}

.legend-icon.level-two {
  background: #FF6817;
}

.legend-icon.level-three {
  background: #FFD32E;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  min-height: 150px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }

  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .chart-wrapper {
    min-height: 170px;
  }
}
</style>