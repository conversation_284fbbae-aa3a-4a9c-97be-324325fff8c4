<template>
  <div class="component-legend">
    <div class="title-header">图例</div>
    <div class="content-list">
      <div
        v-for="(item, index) in legendData"
        :key="index"
        class="content-item"
      >
        <div class="child-title">
          {{ item.label }}
        </div>
        <div v-if="item.special" v-for="(pChild, j) in item.children" :key="j" class="content-item">
          <div class="child-special">
              {{ pChild.label }}
          </div>
          <div v-for="(child, i) in pChild.children" :key="i" class="child-item">
                <img v-if="child.icon !== 'default'" :src="child.icon" alt="" />
                <span
                        v-else
                        class="custom-icon"
                        :style="{ background: child.color }"
                ></span>
                <div class="text-label">
                    {{ child.label }}
                </div>
            </div>
        </div>
        <div v-else v-for="(child, i) in item.children" :key="i" class="child-item">
          <img v-if="child.icon !== 'default'" :src="child.icon" alt="" />
          <span
            v-else
            class="custom-icon"
            :style="{ background: child.color }"
          ></span>
          <div class="text-label">
            {{ child.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { legendListMap } from "./config";
import { useRoute } from "vue-router";
import {computed} from "vue";
const route = useRoute();

const legendData = computed(() => {
  return legendListMap?.[route.path] ?? [];
});
</script>

<style lang="scss" scoped>
.component-legend {
  pointer-events: all;
  width: 168px;
  min-height: 30px;
  max-height: 200px;
  background: rgba(13, 37, 82, 0.8);
  border-radius: 4px;
  border: 1px solid #324256;
  display: flex;
  flex-direction: column;
  padding: 15px 8px 15px 12px;
  margin-bottom: 8px;
  .title-header {
    color: #fff;
    font-size: 14px;
    font-weight: 800;
    padding-bottom: 12px;
  }
  .content-list {
    flex: 1;
    overflow: auto;

    .child-title {
      color: #cae1f8;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .child-special {
      color: #cae1f8;
      font-size: 13px;
      font-weight: 400;
      margin-bottom: 8px;
      padding-left: 5px;
    }

    .child-item {
      padding-left: 12px;
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .custom-icon {
        width: 9px;
        height: 9px;
        margin:0px 9px 0px 5px;
        transform: skew(-20deg);
      }
      .text-label {
        font-size: 12px;
        font-weight: 400;
        color: #cae1f8;
      }

      img {
        width: 18px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }
}
</style>
