<template>
  <div class="heating-alarm-container">
    <!-- 上部分区域：报警统计信息 -->
    <div class="alert-stats-section">
      <!-- 卡片1：全部报警 -->
      <div class="alert-card blue-card">
        <div class="alert-title">全部报警</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.totalAlarms }}</span>
        </div>
      </div>

      <!-- 卡片2：待确认 -->
      <div class="alert-card orange-card">
        <div class="alert-title">待确认</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.pendingConfirm }}</span>
          <span class="percentage">{{ alarmStats.pendingConfirmRate }}</span>
        </div>
      </div>

      <!-- 卡片3：待处置 -->
      <div class="alert-card yellow-card">
        <div class="alert-title">待处置</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.pendingHandle }}</span>
          <span class="percentage">{{ alarmStats.pendingHandleRate }}</span>
        </div>
      </div>

      <!-- 卡片4：处置中 -->
      <div class="alert-card cyan-card">
        <div class="alert-title">处置中</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.handling }}</span>
          <span class="percentage">{{ alarmStats.handlingRate }}</span>
        </div>
      </div>

      <!-- 卡片5：已处置 -->
      <div class="alert-card gray-card">
        <div class="alert-title">已处置</div>
        <div class="alert-value">
          <span class="value">{{ alarmStats.handled }}</span>
          <span class="percentage">{{ alarmStats.handledRate }}</span>
        </div>
      </div>

      <!-- 卡片6：级别统计 -->
      <div class="alert-card level-card">
        <template v-for="(item, index) in alarmLevelStats" :key="item.alarmLevel">
          <div class="level-item">
            <div class="level-title">{{ item.alarmLevelName }}报警</div>
            <div class="level-value">
              <span
                :class="['level-num', index === 0 ? 'level-first' : index === 1 ? 'level-second' : 'level-third']">{{
                  item.totalCount }}</span>
              <span class="level-percent">| {{ item.percent }}</span>
            </div>
          </div>
          <div class="vertical-divider" v-if="index < alarmLevelStats.length - 1"></div>
        </template>
      </div>
    </div>
    
    <!-- 搜索区域 -->
    <div class="heating-alarm-search">
      <HeatingAlarmSearch @search="handleSearch" @reset="handleReset" />
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <!-- <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button> -->
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table ref="dataTable" :data="tableData" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" style="width: 100%" @row-click="handleRowClick"
      :max-height="tableMaxHeight" empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" width="60" align="center">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="报警来源" width="120" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ HEATING_ALARM_SOURCE_MAP[scope.row.alarmSource] || scope.row.alarmSource }}
        </template>
      </el-table-column>
      <el-table-column prop="alarmCode" label="报警编号" width="140" align="center" show-overflow-tooltip />
      <el-table-column prop="alarmTime" label="报警时间" width="170" align="center">
        <template #default="scope">
          {{ formatAlarmTime(scope.row.alarmTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceId" label="设备编码" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="报警类型" width="140" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ HEATING_ALARM_TYPE_MAP[scope.row.alarmType] || scope.row.alarmTypeName }}
        </template>
      </el-table-column>
      <el-table-column prop="monitorIndexName" label="监测对象" width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="alarmValue" label="报警值" width="100" align="center">
        <template #default="scope">
          {{ scope.row.alarmValue }} {{ scope.row.alarmValueUnit }}
        </template>
      </el-table-column>
      <el-table-column prop="alarmLocation" label="报警位置" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column label="报警级别" width="120" align="center" show-overflow-tooltip>
        <template #default="scope">
          <div :class="getAlarmLevelClass(scope.row.alarmLevel)">
            {{ getAlarmLevelText(scope.row.alarmLevel) || scope.row.alarmLevelName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="报警状态" width="120" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ HEATING_ALARM_STATUS_MAP[scope.row.alarmStatus] || scope.row.alarmStatusName }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleDetail(scope.row)">详情</el-button>
            <template v-if="scope.row.alarmStatus === 2001801">
              <el-button type="primary" link @click.stop="handleConfirm(scope.row)">确认</el-button>
            </template>
            <template v-if="scope.row.alarmStatus === 2001803 || scope.row.alarmStatus === 2001804">
              <el-button type="primary" link @click.stop="handleDisposal(scope.row)">处置</el-button>
            </template>
            <el-button type="primary" link @click.stop="handleLocation(scope.row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
        layout="total, prev, pager, next, jumper, sizes" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <!-- 报警详情弹窗 -->
    <HeatingAlarmDialog v-model:visible="dialogVisible" :alarm-id="currentAlarmId" />

    <!-- 报警确认弹窗 -->
    <HeatingAlarmConfirmDialog
      v-model:visible="confirmDialogVisible"
      :alarm-data="currentAlarmData"
      @success="handleConfirmSuccess"
    />

    <!-- 报警处置弹窗 -->
    <HeatingAlarmDisposalDialog
      v-model:visible="disposalDialogVisible"
      :alarm-data="currentAlarmData"
      @success="handleDisposalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElMessage, ElButton } from 'element-plus';
import HeatingAlarmSearch from './components/HeatingAlarmSearch.vue';
import HeatingAlarmDialog from './components/HeatingAlarmDialog.vue';
import HeatingAlarmConfirmDialog from './components/HeatingAlarmConfirmDialog.vue';
import HeatingAlarmDisposalDialog from './components/HeatingAlarmDisposalDialog.vue';
import { getHeatingAlarmList, getHeatingAlarmStatistics, getHeatingAlarmLevelStatistics } from '@/api/heating';
import { 
  HEATING_ALARM_LEVEL_MAP, 
  HEATING_ALARM_STATUS_MAP, 
  HEATING_ALARM_SOURCE_MAP, 
  HEATING_ALARM_TYPE_MAP,
} from '@/constants/heating';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 报警统计数据
const alarmStats = reactive({
  totalAlarms: 0,
  pendingConfirm: 0,
  pendingConfirmRate: '0%',
  pendingHandle: 0,
  pendingHandleRate: '0%',
  handling: 0,
  handlingRate: '0%',
  handled: 0,
  handledRate: '0%'
});

// 报警等级统计数据
const alarmLevelStats = ref([]);

// 详情弹窗相关
const dialogVisible = ref(false);
const currentAlarmId = ref('');

// 报警确认弹窗相关
const confirmDialogVisible = ref(false);
const currentAlarmData = ref({});

// 报警处置弹窗相关
const disposalDialogVisible = ref(false);

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return HEATING_ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    2001701: 'alarm-level-first',
    2001702: 'alarm-level-second', 
    2001703: 'alarm-level-third',
    2001704: 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 格式化报警时间
const formatAlarmTime = (timeObj) => {
  if (!timeObj) return '';
  // 如果是时间戳对象格式
  if (typeof timeObj === 'object' && timeObj.time) {
    return new Date(timeObj.time).toLocaleString('zh-CN');
  }
  // 如果是字符串格式
  if (typeof timeObj === 'string') {
    return timeObj;
  }
  return '';
};

// 处理搜索
const handleSearch = (formData) => {
  // 转换查询参数
  const params = {};

  if (formData.alarmSource && formData.alarmSource !== '') {
    params.alarmSource = formData.alarmSource;
  }

  if (formData.alarmLevel && formData.alarmLevel !== '') {
    params.alarmLevel = parseInt(formData.alarmLevel);
  }

  if (formData.alarmType && formData.alarmType !== '') {
    params.alarmType = parseInt(formData.alarmType);
  }

  if (formData.timeRange && formData.timeRange.length === 2) {
    params.startTime = formData.timeRange[0];
    params.endTime = formData.timeRange[1];
  }

  if (formData.alarmStatus && formData.alarmStatus !== '') {
    params.alarmStatus = parseInt(formData.alarmStatus);
  }

  if (formData.code) {
    params.code = formData.code;
  }

  queryParams.value = params;
  currentPage.value = 1; // 重置到第一页
  fetchAlarmData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchAlarmData();
};

// 获取报警处置状态统计数据
const fetchAlarmStatusStatistics = async () => {
  try {
    const params = {
      startDate: queryParams.value.startTime || '',
      endDate: queryParams.value.endTime || ''
    };
    const res = await getHeatingAlarmStatistics(params);
    if (res.code === 200 && res.data) {
      Object.assign(alarmStats, res.data);
    }
  } catch (error) {
    console.error('获取报警处置状态统计数据失败:', error);
  }
};

// 获取报警等级统计数据
const fetchAlarmLevelStatistics = async () => {
  try {
    const params = {
      startDate: queryParams.value.startTime || '',
      endDate: queryParams.value.endTime || ''
    };
    const res = await getHeatingAlarmLevelStatistics(params);
    if (res.code === 200 && res.data && res.data.statistics) {
      alarmLevelStats.value = res.data.statistics;
    }
  } catch (error) {
    console.error('获取报警等级统计数据失败:', error);
  }
};

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    loading.value = true;
    const res = await getHeatingAlarmList(currentPage.value, pageSize.value, queryParams.value);
    if (res.code === 200 && res.data) {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取报警数据失败:', error);
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchAlarmData();
};

// 表头样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleConfirm = (row) => {
  console.log('确认报警:', row);
  currentAlarmData.value = row;
  confirmDialogVisible.value = true;
};

const handleDisposal = (row) => {
  console.log('处置报警:', row);
  currentAlarmData.value = row;
  disposalDialogVisible.value = true;
};

const handleDetail = (row) => {
  currentAlarmId.value = row.id + "";
  dialogVisible.value = true;
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 确认成功回调
const handleConfirmSuccess = () => {
  fetchAlarmData(); // 重新获取数据
  fetchAlarmStatusStatistics(); // 重新获取统计数据
  fetchAlarmLevelStatistics(); // 重新获取等级统计数据
};

// 处置成功回调
const handleDisposalSuccess = () => {
  fetchAlarmData(); // 重新获取数据
  fetchAlarmStatusStatistics(); // 重新获取统计数据
  fetchAlarmLevelStatistics(); // 重新获取等级统计数据
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.heating-alarm-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const statsSection = container.querySelector('.alert-stats-section');
    const statsHeight = statsSection ? statsSection.offsetHeight : 132;
    const searchSection = container.querySelector('.heating-alarm-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 180;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - statsHeight - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchAlarmStatusStatistics();
  fetchAlarmLevelStatistics();
  fetchAlarmData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.heating-alarm-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 报警统计区域样式 */
.alert-stats-section {
  width: 100%;
  background: #FFFFFF;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.alert-card {
  height: 100px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 24px;
}

.blue-card {
  width: 210px;
  background: #3B98FF;
}

.orange-card {
  width: 210px;
  background: #FF8D5B;
}

.yellow-card {
  width: 210px;
  background: #FFD466;
}

.cyan-card {
  width: 210px;
  background: #40C3FA;
}

.gray-card {
  width: 210px;
  background: #AAAAAA;
}

.level-card {
  width: 500px;
  background: #FFFFFF;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 0;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.alert-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.alert-value {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 30px;
  color: #FFFFFF;
  margin-right: 4px;
}

.percentage {
  font-size: 14px;
  color: #FFFFFF;
}

/* 级别卡片内部样式 */
.level-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.level-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  margin-bottom: 8px;
}

.level-value {
  display: flex;
  align-items: baseline;
}

.level-num {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 26px;
  margin-right: 4px;
}

.level-first {
  color: #FF0000;
}

.level-second {
  color: #FF6400;
}

.level-third {
  color: #FFC600;
}

.level-percent {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  color: #000000;
}

.vertical-divider {
  width: 1px;
  height: 40px;
  border: 1px solid #D6E7E9;
}

/* 搜索区域样式 */
.heating-alarm-search {
  width: 100%;
  margin-bottom: 16px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 报警等级标签样式 */
.alarm-level-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 0 auto;
}

.alarm-level-first {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.alarm-level-second {
  background: rgba(255, 133, 0, 0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.alarm-level-third {
  background: rgba(255, 211, 0, 0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>