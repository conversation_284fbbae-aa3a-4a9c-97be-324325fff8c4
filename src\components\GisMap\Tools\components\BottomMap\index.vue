<template>
    <div class="bottom-map-container">
        <div
                v-for="(item, idx) in mapTypes"
                :key="item.value"
                :class="['map-card', { selected: selectedType === item.value }]"
                @click="selectMapType(item.value)"
        >
            <img :src="item.icon" class="map-card-img" alt=""/>
            <div class="map-card-label">{{ item.label }}</div>
        </div>
    </div>
</template>

<script setup>
import {ref} from 'vue'
import bus from "@/utils/mitt.js";
const selectedType = ref('map3d');
const mapTypes = [
    {
        label: '二维地图', value: 'image', icon: new URL(
            `./images/image.png`,
            import.meta.url
        ).href
    },
    {
        label: '三维建模', value: 'map3d', icon: new URL(
            `./images/map3d.png`,
            import.meta.url
        ).href
    },
    /*{
       label: '电子地图', value: 'vector', icon: new URL(
           `./images/vector.png`,
           import.meta.url
       ).href
   },
    {
        label: '卫星云图', value: 'satellite', icon: new URL(
            `./images/satellite.png`,
            import.meta.url
        ).href
    },
    {
        label: '气象雷达', value: 'radar', icon: new URL(
            `./images/radar.png`,
            import.meta.url).href
    },*/
]

const selectMapType = (type) => {
    if (selectedType.value !== type) {
        selectedType.value = type;
        bus.emit('changeBottomMapType', type);
    }
}
</script>

<style scoped>
.bottom-map-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    background: rgba(16,49,92,0.8);
    backdrop-filter: blur(2px);
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #112845;
}

.map-card {
    width: 72px;
    height: 72px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    border-radius: 4px;
    overflow: hidden;
    border: 1px dashed #7ea6e6;
    background: rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: background 0.2s;
    position: relative;
}

.map-card.selected {
    background: linear-gradient(0deg, #3a7cff 0%, #5ebeff 100%);
    border: 1px solid rgba(26, 142, 231, 1);
}

.map-card-img {
    width: 100%;
    height: 70px;
    object-fit: cover;
    display: block;
}

.map-card-label {
    width: 100%;
    background: rgba(0, 0, 0, 0.4);
    padding: 6px 0;
    position: absolute;
    bottom: 0;
    left: 0;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 12px;
    color: #FFFFFF;
    text-align: center;
    line-height: 16px;
}

.map-card.selected .map-card-label {
    background: rgba(26, 142, 231, 0.8);
}
</style>