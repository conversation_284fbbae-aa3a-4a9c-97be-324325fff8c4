
<template>
  <div class="notice-container">
    <!-- 搜索区域 -->
    <div class="notice-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">标题:</span>
          <el-input v-model="formData.title" class="form-input" placeholder="输入标题关键词" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="source" label="公告来源" min-width="120" show-overflow-tooltip />
      <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="publishDeptName" label="发布范围" min-width="180" show-overflow-tooltip />
      <el-table-column prop="mainTypeName" label="主体分类" min-width="100" />
      <el-table-column prop="documentType" label="公文种类" min-width="100" />
      <el-table-column prop="importantLevelName" label="重要程度" min-width="100">
        <template #default="{ row }">
          <el-tag :type="row.importantLevel === '7000201' ? 'danger' : 'info'" size="small">
            {{ row.importantLevelName || '重要' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="发布时间" min-width="170" />
      <el-table-column label="公开附件" min-width="100">
        <template #default="{ row }">
          <el-button v-if="row.fileUrls" type="primary" link size="small" @click.stop="handleDownloadFiles(row)">
            {{ row.fileUrls }}
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="300" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleCirculation(row)">流转</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDownloadFiles(row)">下载附件</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <!-- 对话框区域 -->
    <NoticeDialog v-model:visible="dialogVisible" :mode="dialogMode" :data="dialogData"
      @success="handleDialogSuccess" />

    <!-- 流转对话框 -->
    <el-dialog v-model="circulationVisible" title="流转" width="500px" :close-on-click-modal="false">
      <el-form :model="circulationForm" label-width="120px">
        <el-form-item label="流转部门:" prop="circulationDeptId">
          <el-tree-select v-model="circulationForm.circulationDeptId" :data="deptTreeOptions" placeholder="请选择"
            class="w-full" @change="handleCirculationDeptChange" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="circulationVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleConfirmCirculation">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus'
import { getNoticePage, deleteNotice, getNoticeDetail, updateNotice } from '@/api/comprehensive'
import { getDeptTree } from '@/api/system'
import { IMPORTANT_LEVEL_OPTIONS, MAIN_TYPE_OPTIONS, DOCUMENT_TYPE_OPTIONS } from '@/constants/comprehensive'
import NoticeDialog from './components/NoticeDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 下拉选项数据
const importantLevelOptions = ref(IMPORTANT_LEVEL_OPTIONS)
const mainTypeOptions = ref(MAIN_TYPE_OPTIONS)
const deptTreeOptions = ref([])

// 表单数据
const formData = ref({
  title: '',
  mainType: '',
  importantLevel: '',
  publishDeptId: []
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 流转对话框相关
const circulationVisible = ref(false)
const circulationForm = reactive({
  id: '',
  circulationDeptId: '',
  circulationDeptName: ''
})

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchNoticeData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    title: '',
    mainType: '',
    importantLevel: '',
    publishDeptId: []
  }
  currentPage.value = 1
  fetchNoticeData()
}

// 获取通知公告分页数据
const fetchNoticeData = async () => {
  loading.value = true;
  try {
    const params = {
      title: formData.value.title,
      mainType: formData.value.mainType,
      importantLevel: formData.value.importantLevel,
      publishDeptId: Array.isArray(formData.value.publishDeptId)
        ? formData.value.publishDeptId.join(',')
        : formData.value.publishDeptId
    }

    const res = await getNoticePage(currentPage.value, pageSize.value, params)

    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取通知公告数据失败:', error)
    ElMessage.error('获取通知公告数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200) {
      // 转换数据格式以适配el-tree-select
      const convertTree = (nodes) => {
        return nodes.map(node => ({
          value: node.id,
          label: node.name,
          children: node.children ? convertTree(node.children) : undefined
        }))
      }
      deptTreeOptions.value = convertTree(res.data || [])
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchNoticeData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchNoticeData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getNoticeDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取通知公告详情失败')
    }
  } catch (error) {
    console.error('获取通知公告详情失败:', error)
    ElMessage.error('获取通知公告详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getNoticeDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取通知公告详情失败')
    }
  } catch (error) {
    console.error('获取通知公告详情失败:', error)
    ElMessage.error('获取通知公告详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该通知公告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteNotice(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchNoticeData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除通知公告失败:', error)
      ElMessage.error('删除通知公告失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理流转
const handleCirculation = (row) => {
  circulationForm.id = row.id
  circulationForm.circulationDeptId = ''
  circulationForm.circulationDeptName = ''
  circulationVisible.value = true
}

// 处理流转部门选择变化
const handleCirculationDeptChange = (value) => {
  const findDeptName = (nodes, targetId) => {
    for (const node of nodes) {
      if (node.value === targetId) {
        return node.label
      }
      if (node.children) {
        const found = findDeptName(node.children, targetId)
        if (found) return found
      }
    }
    return ''
  }

  if (value) {
    circulationForm.circulationDeptName = findDeptName(deptTreeOptions.value, value)
  } else {
    circulationForm.circulationDeptName = ''
  }
}

// 确认流转
const handleConfirmCirculation = async () => {
  if (!circulationForm.circulationDeptId) {
    ElMessage.error('请选择流转部门')
    return
  }

  try {
    // 获取当前记录详情
    const res = await getNoticeDetail(circulationForm.id)
    if (res && res.code === 200) {
      const updateData = {
        ...res.data,
        circulationDeptId: circulationForm.circulationDeptId,
        circulationDeptName: circulationForm.circulationDeptName
      }

      const updateRes = await updateNotice(updateData)
      if (updateRes && updateRes.code === 200) {
        ElMessage.success('流转成功')
        circulationVisible.value = false
        fetchNoticeData()
      } else {
        ElMessage.error(updateRes?.msg || '流转失败')
      }
    }
  } catch (error) {
    console.error('流转失败:', error)
    ElMessage.error('流转失败')
  }
}

// 处理下载附件
const handleDownloadFiles = (row) => {
  if (!row.fileUrls) {
    ElMessage.warning('该记录没有附件')
    return
  }

  const urls = row.fileUrls.split(',').filter(url => url.trim())
  if (urls.length === 0) {
    ElMessage.warning('该记录没有附件')
    return
  }

  // 如果只有一个文件，直接下载
  if (urls.length === 1) {
    const link = document.createElement('a')
    link.href = urls[0].trim()
    link.download = `${row.title}_附件`
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    // 多个文件，依次下载
    urls.forEach((url, index) => {
      setTimeout(() => {
        const link = document.createElement('a')
        link.href = url.trim()
        link.download = `${row.title}_附件_${index + 1}`
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }, index * 500) // 每个文件间隔500ms下载
    })
  }
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchNoticeData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.notice-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 48;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchDeptTree(),
      fetchNoticeData()
    ])
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.notice-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.notice-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.w-full {
  width: 100%;
}
</style>
