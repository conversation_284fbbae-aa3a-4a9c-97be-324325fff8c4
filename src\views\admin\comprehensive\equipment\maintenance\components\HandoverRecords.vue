<template>
  <div class="handover-records-container">
    <!-- 搜索区域 -->
    <div class="handover-records-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">值班日期:</span>
          <el-date-picker
            v-model="formData.scheduleTime"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            class="form-input"
          />
        </div>
        <div class="form-item">
          <span class="label">班次:</span>
          <el-select v-model="formData.shiftId" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in shiftOptions" :key="item.id" :label="item.shiftName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.userName" class="form-input" placeholder="输入人员姓名" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="scheduleTime" label="值班日期" min-width="120">
        <template #default="{ row }">
          {{ formatDate(row.scheduleTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="shiftName" label="班次" min-width="100" />
      <el-table-column prop="changeRecord" label="交班记录" min-width="200" show-overflow-tooltip />
      <el-table-column prop="handoverTime" label="交班时间" min-width="150">
        <template #default="{ row }">
          {{ formatDateTime(row.handoverTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="handoverUserName" label="交班人员" min-width="120" />
      <el-table-column prop="takeoverTime" label="接班时间" min-width="150">
        <template #default="{ row }">
          {{ formatDateTime(row.takeoverTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="takeoverUserName" label="接班人员" min-width="120" />
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <HandoverDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  getDutyShiftChangePage, 
  deleteDutyShiftChange, 
  getDutyShiftChangeDetail,
  getDutyShiftList
} from '@/api/comprehensive'
import HandoverDialog from './HandoverDialog.vue'
import moment from 'moment'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 下拉选项数据
const shiftOptions = ref([])

// 表单数据
const formData = ref({
  scheduleTime: '',
  shiftId: '',
  userName: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return moment(dateStr).format('YYYY-MM-DD')
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  return moment(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchHandoverData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    scheduleTime: '',
    shiftId: '',
    userName: ''
  }
  currentPage.value = 1
  fetchHandoverData()
}

// 获取交接班记录分页数据
const fetchHandoverData = async () => {
  loading.value = true;
  try {
    const params = {
      scheduleTime: formData.value.scheduleTime,
      shiftId: formData.value.shiftId,
      userName: formData.value.userName
    }
    
    const res = await getDutyShiftChangePage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取交接班记录数据失败:', error)
    ElMessage.error('获取交接班记录数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 获取班次列表
const fetchShiftList = async () => {
  try {
    const res = await getDutyShiftList({ overNight: '' })
    if (res && res.code === 200) {
      shiftOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取班次列表失败', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchHandoverData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchHandoverData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getDutyShiftChangeDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取交接班记录详情失败')
    }
  } catch (error) {
    console.error('获取交接班记录详情失败:', error)
    ElMessage.error('获取交接班记录详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getDutyShiftChangeDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取交接班记录详情失败')
    }
  } catch (error) {
    console.error('获取交接班记录详情失败:', error)
    ElMessage.error('获取交接班记录详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该交接班记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDutyShiftChange(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchHandoverData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除交接班记录失败:', error)
      ElMessage.error('删除交接班记录失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchHandoverData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.handover-records-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.handover-records-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 48;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchShiftList(),
      fetchHandoverData()
    ])
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.handover-records-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.handover-records-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>