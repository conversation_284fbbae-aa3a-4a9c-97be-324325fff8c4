<template>
  <el-dialog
    v-model="dialogVisible"
    title="催办记录"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="urging-record-dialog"
  >
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">催办部门:</span>
          <el-input v-model="searchForm.urgingUnitName" placeholder="请输入" class="form-input" />
        </div>
        <div class="form-item">
          <span class="label">催办时间:</span>
          <el-date-picker
            v-model="searchForm.urgingTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="form-input"
          />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column label="序号" width="80" align="center">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="催办时间" width="150" />
        <el-table-column prop="urgingUnitName" label="催办部门" width="150" />
        <el-table-column prop="urgingType" label="通知类型" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.notifySms" type="success" size="small" style="margin-right: 5px;">短信</el-tag>
            <el-tag v-if="row.notifySystem" type="info" size="small">系统</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="urgingContent" label="催办内容" min-width="300" show-overflow-tooltip />
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getUrgingPage } from '@/api/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 搜索表单
const searchForm = reactive({
  urgingUnitName: '',
  urgingTime: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)

// 获取催办记录数据
const fetchUrgingRecords = async () => {
  try {
    loading.value = true
    
    const params = {
      urgingUnitName: searchForm.urgingUnitName,
      startTime: searchForm.urgingTime && searchForm.urgingTime[0] ? searchForm.urgingTime[0] : '',
      endTime: searchForm.urgingTime && searchForm.urgingTime[1] ? searchForm.urgingTime[1] : ''
    }
    
    const res = await getUrgingPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data?.records || res.data || []
      total.value = res.data?.total || tableData.value.length || 0
    }
  } catch (error) {
    console.error('获取催办记录失败:', error)
    ElMessage.error('获取催办记录失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchUrgingRecords()
}

// 处理重置
const handleReset = () => {
  searchForm.urgingUnitName = ''
  searchForm.urgingTime = ''
  currentPage.value = 1
  fetchUrgingRecords()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchUrgingRecords()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchUrgingRecords()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 重置搜索条件
  searchForm.urgingUnitName = ''
  searchForm.urgingTime = ''
  currentPage.value = 1
  tableData.value = []
  total.value = 0
}

// 监听对话框可见性变化，加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchUrgingRecords()
  }
})
</script>

<style scoped>
.urging-record-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  height: 600px;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

.form-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 200px;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
  min-height: 400px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}
</style> 