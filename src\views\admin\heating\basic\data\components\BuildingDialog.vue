<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="building-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑名称" prop="buildingName">
            <el-input v-model="formData.buildingName" placeholder="请输入建筑名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑编号" prop="buildingCode">
            <el-input v-model="formData.buildingCode" placeholder="请输入建筑编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑类型" prop="buildingType">
            <el-select v-model="formData.buildingType" placeholder="请选择" class="w-full">
              <el-option v-for="item in buildingTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属换热站" prop="stationId">
            <el-select v-model="formData.stationId" placeholder="请选择" class="w-full" @change="handleStationChange">
              <el-option v-for="item in heatStationOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属机组" prop="unitId">
            <el-select v-model="formData.unitId" placeholder="请选择" class="w-full" @change="handleUnitChange">
              <el-option v-for="item in unitOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采暖面积" prop="heatingArea">
            <div class="flex items-center">
              <el-input-number v-model="formData.heatingArea" :min="0" :precision="2" class="w-full-unit" />
              <span class="unit-label">m²</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建造年代" prop="constructionYear">
            <el-select v-model="formData.constructionYear" placeholder="请选择" class="w-full">
              <el-option v-for="item in constructionYearOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供热企业" prop="ownershipUnit">
            <el-select v-model="formData.ownershipUnit" placeholder="请选择" class="w-full" @change="handleEnterpriseChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="能耗分类" prop="energyType">
            <el-input v-model="formData.energyType" placeholder="请输入能耗分类" />
          </el-form-item>
        </el-col>
      
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveBuilding,
  updateBuilding,
  getAllEnterpriseList,
  getAllHeatStationList,
  getAllUnitList
} from '@/api/heating';
import { BUILDING_TYPE_OPTIONS, CONSTRUCTION_YEAR_OPTIONS } from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const buildingTypeOptions = BUILDING_TYPE_OPTIONS;
const constructionYearOptions = CONSTRUCTION_YEAR_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增建筑信息',
    edit: '编辑建筑信息',
    view: '建筑信息详情'
  };
  return titles[props.mode] || '建筑信息';
});

// 下拉选项数据
const enterpriseOptions = ref([]);
const heatStationOptions = ref([]);
const unitOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  buildingName: '',
  buildingCode: '',
  buildingType: '',
  buildingTypeName: '',
  stationId: '',
  stationName: '',
  unitId: '',
  unitName: '',
  heatingArea: 0,
  constructionYear: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  energyType: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  buildingName: [{ required: true, message: '请输入建筑名称', trigger: 'blur' }],
  buildingCode: [{ required: true, message: '请输入建筑编号', trigger: 'blur' }],
  buildingType: [{ required: true, message: '请选择建筑类型', trigger: 'change' }],
  stationId: [{ required: true, message: '请选择所属换热站', trigger: 'change' }],
  unitId: [{ required: true, message: '请选择所属机组', trigger: 'change' }],
  heatingArea: [{ required: true, message: '请输入采暖面积', trigger: 'blur' }],
  constructionYear: [{ required: true, message: '请选择建造年代', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择供热企业', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'heatingArea') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 建筑类型
  const selectedBuildingType = buildingTypeOptions.find(item => item.value === formData.buildingType);
  if (selectedBuildingType) {
    formData.buildingTypeName = selectedBuildingType.label;
  }

  // 换热站
  const selectedStation = heatStationOptions.value.find(item => item.value === formData.stationId);
  if (selectedStation) {
    formData.stationName = selectedStation.label;
  }

  // 机组
  const selectedUnit = unitOptions.value.find(item => item.value === formData.unitId);
  if (selectedUnit) {
    formData.unitName = selectedUnit.label;
  }

  // 供热企业
  const selectedEnterprise = enterpriseOptions.value.find(item => item.value === formData.ownershipUnit);
  if (selectedEnterprise) {
    formData.ownershipUnitName = selectedEnterprise.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.buildingType, (val) => {
  if (val) {
    const selected = buildingTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.buildingTypeName = selected.label;
    }
  }
});

// 处理换热站变化
const handleStationChange = (value) => {
  const selected = heatStationOptions.value.find(item => item.value === value);
  if (selected) {
    formData.stationName = selected.label;
  }
};

// 处理机组变化
const handleUnitChange = (value) => {
  const selected = unitOptions.value.find(item => item.value === value);
  if (selected) {
    formData.unitName = selected.label;
  }
};

// 处理企业变化
const handleEnterpriseChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.ownershipUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 获取换热站列表
const fetchHeatStations = async () => {
  try {
    const res = await getAllHeatStationList();
    if (res && res.data) {
      heatStationOptions.value = res.data.map(item => ({
        label: item.stationName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取换热站列表失败', error);
  }
};

// 获取机组列表
const fetchUnits = async () => {
  try {
    const res = await getAllUnitList();
    if (res && res.data) {
      unitOptions.value = res.data.map(item => ({
        label: item.unitName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取机组列表失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveBuilding(submitData);
    } else if (props.mode === 'edit') {
      res = await updateBuilding(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
  fetchHeatStations();
  fetchUnits();
});
</script>

<style scoped>
.building-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 40px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 35px;
  margin-left: 5px;
}
</style>