<template>
  <PanelBox title="报警等级" class="gas-monitoring-right-top-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-header">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-icon total"></span>
            <span class="legend-text">总数</span>
          </div>
          <div class="legend-item">
            <span class="legend-icon deployed"></span>
            <span class="legend-text">已处置</span>
          </div>
        </div>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getMonitorAnalysisLevelStatistics } from '@/api/gas'

// 时间选择
const timeRange = ref('month')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到dayIndex
const timeRangeMap = {
  week: 7,
  month: 30,
  year: 365
}

// 页面可见性状态
let isPageVisible = true
// 定时器ID
let refreshTimer = null

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 图表数据
const chartData = reactive({
  categories: [],
  series: [
    { name: '总数', values: [] },
    { name: '已处置', values: [] }
  ]
})

// 获取报警等级数据
const fetchAlarmLevelData = async () => {
  try {
    const dayIndex = timeRangeMap[timeRange.value] || 30
    const response = await getMonitorAnalysisLevelStatistics({ dayIndex })
    if (response?.data?.statistics) {
      const statistics = response.data.statistics

      // 更新图表数据
      chartData.categories = statistics.map(item => `${item.alarmLevelName}级报警`)
      chartData.series[0].values = statistics.map(item => item.totalCount)
      chartData.series[1].values = statistics.map(item => item.handledCount)
    }

    // 如果图表已初始化，则更新图表
    if (chartInstance) {
      updateChart()
    }
  } catch (error) {
    console.error('获取报警等级数据失败:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '16%',
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
      formatter: function (params) {
        const categoryName = params[0].name;
        let html = `<div style="font-weight:bold;margin-bottom:8px;color:#3CF3FF;">${categoryName}</div>`;

        params.forEach(item => {
          let color = '';
          let unit = '';

          if (item.seriesName === '总数') {
            color = '#055ADB';
            unit = '个';
          } else if (item.seriesName === '已处置') {
            color = '#23CAFF';
            unit = '个';
          }

          html += `<div style="display:flex;justify-content:space-between;margin:5px 0;align-items:center;">
            <span style="margin-right:15px;display:inline-block;">
              <span style="display:inline-block;width:8px;height:8px;border-radius:4px;background-color:${color};margin-right:5px;"></span>
              ${item.seriesName}:
            </span>
            <span style="font-weight:bold;color:${color};">${item.value}${unit}</span>
          </div>`;
        });

        return html;
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(5, 122, 255, 0.1)'
        }
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.categories,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 16,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '单位(个)',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        padding: [0, -18, 0, 0]
      },
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '总数',
        type: 'bar',
        data: chartData.series[0].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#7AAFFF' },
              { offset: 1, color: '#055ADB' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#0A93FF',
          borderWidth: 1,
          shadowColor: 'rgba(5, 122, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(5, 90, 219, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(5, 90, 219, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '已处置',
        type: 'bar',
        data: chartData.series[1].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFFFFF' },
              { offset: 1, color: '#23CAFF' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#34D6FF',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      }
    ]
  };
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)

  window.addEventListener('resize', handleResize)
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  fetchAlarmLevelData()
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  if (document.hidden) {
    isPageVisible = false
    // 页面不可见时清除定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  } else {
    isPageVisible = true
    // 页面可见时重新获取数据并启动定时器
    fetchAlarmLevelData()
    startDataRefresh()
  }
}

// 启动数据刷新
const startDataRefresh = () => {
  if (!refreshTimer && isPageVisible) {
    const refreshInterval = 600000 // 10分钟刷新一次
    refreshTimer = setInterval(() => {
      fetchAlarmLevelData()
    }, refreshInterval)
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  await fetchAlarmLevelData()
  // 启动数据刷新
  startDataRefresh()
  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  // 清理事件监听和定时器
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.gas-monitoring-right-top-panel {
  height: 280px;
  /* 默认高度为280px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.deployed {
  background: #23CAFF;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 180px;
}

/* 时间选择器样式 */
.com-select {
  margin-right: 20px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-right-top-panel {
    height: 280px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-right-top-panel {
    height: 240px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-right-top-panel {
    height: 340px;
  }
}
</style>