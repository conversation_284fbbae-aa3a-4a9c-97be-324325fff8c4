
<template>
  <div class="emergency-event-container">
    <!-- 搜索区域 -->
    <div class="emergency-event-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">事件标题:</span>
          <el-input v-model="formData.eventTitle" class="form-input" placeholder="输入事件标题" />
        </div>
        <div class="form-item">
          <span class="label">事件分级:</span>
          <el-select v-model="formData.eventLevel" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in eventLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">处置状态:</span>
          <el-select v-model="formData.eventStatus" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in eventStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">所属行业:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="eventTitle" label="事件标题" min-width="150" />
      <el-table-column prop="eventCode" label="事件编号" min-width="120" />
      <el-table-column prop="eventLevelName" label="事件分级" min-width="100" />
      <el-table-column prop="eventTypeName" label="事件分类" min-width="120" />
      <el-table-column prop="eventStatusName" label="处置状态" min-width="100">
        <template #default="{ row }">
          <el-tag
            :type="getStatusTagType(row.eventStatus)"
            size="small"
          >
            {{ row.eventStatusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="relatedBusinessName" label="所属行业" min-width="100" />
      <el-table-column prop="ownershipUnitName" label="处置单位" min-width="120" />
      <el-table-column prop="eventTime" label="事件时间" min-width="150" />
      <el-table-column prop="contactInfo" label="联系方式" min-width="120" />
      <el-table-column label="是否伤亡" min-width="80">
        <template #default="{ row }">
          <el-tag
            :type="row.isCasualty ? 'danger' : 'success'"
            size="small"
          >
            {{ row.isCasualty ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="位置" min-width="150" />
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <EmergencyEventDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElTag } from 'element-plus';
import { 
  getEmergencyEventPage, 
  deleteEmergencyEvent, 
  getEmergencyEventDetail
} from '@/api/comprehensive';
import {
  EVENT_LEVEL_OPTIONS,
  EVENT_STATUS_OPTIONS,
  RELATED_BUSINESS_OPTIONS
} from '@/constants/comprehensive';
import { misPosition } from '@/hooks/gishooks';
import EmergencyEventDialog from './components/EmergencyEventDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const tableMaxHeight = ref(500);

// 下拉选项数据
const eventLevelOptions = EVENT_LEVEL_OPTIONS;
const eventStatusOptions = EVENT_STATUS_OPTIONS;
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS;

// 表单数据
const formData = ref({
  eventTitle: '',
  eventLevel: '',
  eventStatus: '',
  relatedBusiness: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 7002001: // 未处置
      return 'danger';
    case 7002002: // 处置中
      return 'warning';
    case 7002003: // 已处置
      return 'success';
    default:
      return '';
  }
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchEmergencyEventData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    eventTitle: '',
    eventLevel: '',
    eventStatus: '',
    relatedBusiness: ''
  };
  currentPage.value = 1;
  fetchEmergencyEventData();
};

// 获取应急事件分页数据
const fetchEmergencyEventData = async () => {
  loading.value = true;
  try {
    const params = {
      eventTitle: formData.value.eventTitle,
      eventLevel: formData.value.eventLevel,
      eventStatus: formData.value.eventStatus,
      relatedBusiness: formData.value.relatedBusiness
    };
    
    const res = await getEmergencyEventPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取应急事件数据失败:', error);
    ElMessage.error('获取应急事件数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchEmergencyEventData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchEmergencyEventData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getEmergencyEventDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取应急事件详情失败');
    }
  } catch (error) {
    console.error('获取应急事件详情失败:', error);
    ElMessage.error('获取应急事件详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getEmergencyEventDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取应急事件详情失败');
    }
  } catch (error) {
    console.error('获取应急事件详情失败:', error);
    ElMessage.error('获取应急事件详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该应急事件信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEmergencyEvent(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchEmergencyEventData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除应急事件失败:', error);
      ElMessage.error('删除应急事件失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchEmergencyEventData();
};

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.emergency-event-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.emergency-event-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 48;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchEmergencyEventData();
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.emergency-event-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.emergency-event-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
