<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :before-close="handleClose" class="danger-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="mode === 'view'">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="危险源编码" prop="dangerCode">
            <el-input v-model="formData.dangerCode" placeholder="请输入危险源编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="危险源名称" prop="dangerName">
            <el-input v-model="formData.dangerName" placeholder="请输入危险源名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑类型" prop="buildingType">
            <el-select v-model="formData.buildingType" placeholder="请选择" class="w-full" @change="handleBuildingTypeChange">
              <el-option v-for="item in buildingTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否重大危险源" prop="isMajor">
            <el-radio-group v-model="formData.isMajor">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="特征描述" prop="featureDesc">
            <el-input v-model="formData.featureDesc" type="textarea" :rows="3" placeholder="请输入特征描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险等级" prop="riskLevelName">
            <el-input v-model="formData.riskLevelName" placeholder="请输入风险等级" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="影响半径 (KM)" prop="influenceRadius">
            <el-input v-model="formData.influenceRadius" placeholder="请输入影响半径" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="财产损失" prop="propertyLoss">
            <el-input v-model="formData.propertyLoss" placeholder="请输入财产损失" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full" @change="handleManagementUnitChange">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName"
                :value="unit.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactUser">
            <el-input v-model="formData.contactUser" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置" prop="location">
            <div class="flex items-center">
              <el-cascader v-model="formData.town" :options="areaOptions" :props="{
                value: 'code',
                label: 'name',
                children: 'children'
              }" placeholder="请选择所属区域" style="width: 240px;" @change="handleAreaChange" />
              <span class="mx-2">详细位置</span>
              <el-input v-model="formData.address" placeholder="请输入详细位置" style="width: 240px;" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2" />
              <el-input v-model="formData.latitude" placeholder="纬度" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"
                :disabled="mode === 'view'"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveGasDanger, updateGasDanger, getManagementUnits } from '@/api/gas';
import { AREA_OPTIONS,BUILDING_TYPE_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增危险源',
    edit: '编辑危险源',
    view: '危险源详情'
  };
  return titles[props.mode] || '危险源信息';
});

// 表单数据
const formData = reactive({
  id: '',
  dangerCode: '',
  dangerName: '',
  buildingType: '',
  buildingTypeName: '',
  featureDesc: '',
  riskLevelName: '',
  influenceRadius: '',
  propertyLoss: '',
  isMajor: '0',
  managementUnit: '',
  managementUnitName: '',
  contactUser: '',
  contactInfo: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  dangerCode: [{ required: true, message: '请输入危险源编码', trigger: 'blur' }],
  dangerName: [{ required: true, message: '请输入危险源名称', trigger: 'blur' }],
  buildingType: [{ required: true, message: '请选择建筑类型', trigger: 'change' }],
  featureDesc: [{ required: true, message: '请输入特征描述', trigger: 'blur' }],
  isMajor: [{ required: true, message: '请选择是否重大危险源', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
  contactUser: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactInfo: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号码', 
      trigger: 'blur' 
    }
  ],
};

// 建筑类型选项
const buildingTypeOptions = ref(BUILDING_TYPE_OPTIONS);

// 权属单位列表
const managementUnits = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getManagementUnits();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理建筑类型变化
const handleBuildingTypeChange = (value) => {
  const selected = buildingTypeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.buildingTypeName = selected.label;
  } else {
    formData.buildingTypeName = '';
  }
};

// 处理管理单位变化
const handleManagementUnitChange = (value) => {
  const selected = managementUnits.value.find(item => item.id === value);
  if (selected) {
    formData.managementUnitName = selected.enterpriseName;
  } else {
    formData.managementUnitName = '';
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    // 这里我们假设最后一个是乡镇代码
    formData.town = value[value.length - 1];
    
    // 查找对应的名称
    const findTownName = (options, code) => {
      for (const option of options) {
        if (option.code === code) {
          return option.name;
        }
        if (option.children) {
          const found = findTownName(option.children, code);
          if (found) return found;
        }
      }
      return '';
    };
    
    formData.townName = findTownName(areaOptions.value, formData.town);
    
    // 如果有区县信息
    if (value.length > 1) {
      formData.county = value[0];
      const findCountyName = (options, code) => {
        for (const option of options) {
          if (option.code === code) {
            return option.name;
          }
        }
        return '';
      };
      formData.countyName = findCountyName(areaOptions.value, formData.county);
    }
  }
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 设置级联选择器的值
    if (formData.town) {
      // 这里处理级联选择器需要的数组格式
      if (formData.county) {
        formData.town = [formData.county, formData.town];
      } else {
        formData.town = [formData.town];
      }
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = '';
    } else if (key === 'isMajor') {
      formData[key] = '0';
    } else {
      formData[key] = '';
    }
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理town字段，API需要的是字符串而不是数组
    if (Array.isArray(submitData.town)) {
      submitData.town = submitData.town[submitData.town.length - 1];
    }

    // 确保经纬度为数值类型
    if (submitData.longitude) submitData.longitude = Number(submitData.longitude);
    if (submitData.latitude) submitData.latitude = Number(submitData.latitude);
    if (submitData.influenceRadius) submitData.influenceRadius = Number(submitData.influenceRadius);

    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveGasDanger(submitData);
    } else if (props.mode === 'edit') {
      res = await updateGasDanger(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.danger-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mx-2 {
  margin: 0 8px;
}
</style> 