import { useAlarmStore } from '@/stores/alarm'

/**
 * 测试报警消息工具函数
 * 用于在开发环境下模拟报警消息的接收
 */

// 模拟报警消息数据
const mockAlarmMessages = [
  {
    type: 'laserMethane',
    content: '激光甲烷传感器检测到气体浓度超标',
    level: '1',
    deviceId: 'LM_001_SH001',
    value: '85.6 ppm',
    time: new Date().toISOString()
  },
  {
    type: 'waterLevel',
    content: '排水管网水位超过警戒线',
    level: '2',
    deviceId: 'WL_002_PD001',
    value: '2.8m',
    time: new Date(Date.now() - 300000).toISOString() // 5分钟前
  },
  {
    type: 'temperature',
    content: '供热管网温度异常下降',
    level: '3',
    deviceId: 'TEMP_003_GR001',
    value: '45°C',
    time: new Date(Date.now() - 600000).toISOString() // 10分钟前
  },
  {
    type: 'strain',
    content: '桥梁应变传感器检测到异常变形',
    level: '1',
    deviceId: 'ST_004_BR001',
    value: '2500 με',
    time: new Date(Date.now() - 900000).toISOString() // 15分钟前
  }
]

/**
 * 添加单个测试报警消息
 * @param {number} index 消息索引 (0-4)
 */
export function addTestAlarmMessage(index = 0) {
  const alarmStore = useAlarmStore()
  const message = mockAlarmMessages[index % mockAlarmMessages.length]
  
  // 添加随机时间戳确保唯一性
  const testMessage = {
    ...message,
    id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    time: new Date().toISOString(),
    timestamp: Date.now()
  }
  
  alarmStore.addAlarmMessage(testMessage)
  console.log('添加测试报警消息:', testMessage)
}

/**
 * 批量添加测试报警消息
 * @param {number} count 消息数量
 * @param {number} interval 间隔时间(毫秒)
 */
export function addBatchTestAlarmMessages(count = 3, interval = 1000) {
  let index = 0
  
  const addMessage = () => {
    if (index < count) {
      addTestAlarmMessage(index)
      index++
      setTimeout(addMessage, interval)
    }
  }
  
  addMessage()
}

/**
 * 模拟实时报警消息推送
 * @param {number} interval 推送间隔(毫秒)，默认30秒
 */
export function startMockAlarmPush(interval = 30000) {
  const pushMessage = () => {
    const randomIndex = Math.floor(Math.random() * mockAlarmMessages.length)
    addTestAlarmMessage(randomIndex)
  }
  
  // 立即推送一条
  pushMessage()
  
  // 定时推送
  const timer = setInterval(pushMessage, interval)
  
  console.log(`开始模拟报警推送，间隔: ${interval}ms`)
  
  // 返回清除定时器的函数
  return () => {
    clearInterval(timer)
    console.log('停止模拟报警推送')
  }
}

/**
 * 清空所有测试报警消息
 */
export function clearTestAlarmMessages() {
  const alarmStore = useAlarmStore()
  alarmStore.clearAllMessages()
  console.log('已清空所有报警消息')
}

// 开发环境下将测试函数挂载到 window 对象上
if (process.env.NODE_ENV === 'development') {
  window.alarmTest = {
    addOne: addTestAlarmMessage,
    addBatch: addBatchTestAlarmMessages,
    startPush: startMockAlarmPush,
    clear: clearTestAlarmMessages
  }
  
  console.log('报警测试函数已挂载到 window.alarmTest:')
  console.log('- window.alarmTest.addOne(index) - 添加单个测试消息')
  console.log('- window.alarmTest.addBatch(count, interval) - 批量添加测试消息')
  console.log('- window.alarmTest.startPush(interval) - 开始模拟推送')
  console.log('- window.alarmTest.clear() - 清空所有消息')
}