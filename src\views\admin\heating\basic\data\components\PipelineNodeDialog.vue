<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="pipeline-node-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管点编码" prop="pointCode">
            <el-input v-model="formData.pointCode" placeholder="请输入管点编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管点类型" prop="pointType">
            <el-select v-model="formData.pointType" placeholder="请选择" class="w-full">
              <el-option v-for="item in pointTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋深" prop="depth">
            <div class="flex items-center">
              <el-input-number v-model="formData.depth" :min="0" :precision="2" class="w-full-unit" />
              <span class="unit-label">m</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="高程" prop="elevation">
            <div class="flex items-center">
              <el-input-number v-model="formData.elevation" :min="0" :precision="2" class="w-full-unit" />
              <span class="unit-label">m</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联管线" prop="connectedPipelineId">
            <el-select v-model="formData.connectedPipelineId" placeholder="请选择" class="w-full" @change="handlePipelineChange">
              <el-option v-for="item in pipelineOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联窖井" prop="connectedWellId">
            <el-select v-model="formData.connectedWellId" placeholder="请选择" class="w-full" @change="handleWellChange">
              <el-option v-for="item in wellOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full" @change="handleEnterpriseChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus">
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in usageStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="安装时间" prop="installTime">
            <el-date-picker
              v-model="formData.installTime"
              type="datetime"
              placeholder="请选择安装时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="道路名称" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入道路名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附属设施" prop="attachedFacilities">
            <el-input v-model="formData.attachedFacilities" type="textarea" :rows="3" placeholder="请输入附属设施信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  savePipelineNode,
  updatePipelineNode,
  getAllEnterpriseList,
  getPipelineList,
  getWellList
} from '@/api/heating';
import { POINT_TYPE_OPTIONS, POINT_USAGE_STATUS_OPTIONS } from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import moment from 'moment';

// 使用从常量文件导入的选项
const pointTypeOptions = POINT_TYPE_OPTIONS;
const usageStatusOptions = POINT_USAGE_STATUS_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增管点信息',
    edit: '编辑管点信息',
    view: '管点信息详情'
  };
  return titles[props.mode] || '管点信息';
});

// 下拉选项数据
const enterpriseOptions = ref([]);
const pipelineOptions = ref([]);
const wellOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  pointCode: '',
  pointType: '',
  pointTypeName: '',
  depth: 0,
  elevation: 0,
  connectedPipelineId: '',
  connectedPipeline: '',
  connectedWellId: '',
  connectedWell: '',
  managementUnit: '',
  managementUnitName: '',
  usageStatus: '',
  usageStatusName: '',
  installTime: '',
  roadName: '',
  address: '',
  longitude: '',
  latitude: '',
  attachedFacilities: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  geomText: ''
});

// 表单验证规则
const formRules = {
  pointCode: [{ required: true, message: '请输入管点编码', trigger: 'blur' }],
  pointType: [{ required: true, message: '请选择管点类型', trigger: 'change' }],
  depth: [{ required: true, message: '请输入埋深', trigger: 'blur' }],
  elevation: [{ required: true, message: '请输入高程', trigger: 'blur' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'depth' || key === 'elevation') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 管点类型
  const selectedPointType = pointTypeOptions.find(item => item.value === formData.pointType);
  if (selectedPointType) {
    formData.pointTypeName = selectedPointType.label;
  }

  // 使用状态
  const selectedUsageStatus = usageStatusOptions.find(item => item.value === formData.usageStatus);
  if (selectedUsageStatus) {
    formData.usageStatusName = selectedUsageStatus.label;
  }

  // 权属单位
  const selectedEnterprise = enterpriseOptions.value.find(item => item.value === formData.managementUnit);
  if (selectedEnterprise) {
    formData.managementUnitName = selectedEnterprise.label;
  }

  // 关联管线
  const selectedPipeline = pipelineOptions.value.find(item => item.value === formData.connectedPipelineId);
  if (selectedPipeline) {
    formData.connectedPipeline = selectedPipeline.label;
  }

  // 关联窖井
  const selectedWell = wellOptions.value.find(item => item.value === formData.connectedWellId);
  if (selectedWell) {
    formData.connectedWell = selectedWell.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理管线变化
const handlePipelineChange = (value) => {
  const selected = pipelineOptions.value.find(item => item.value === value);
  if (selected) {
    formData.connectedPipeline = selected.label;
  }
};

// 处理窖井变化
const handleWellChange = (value) => {
  const selected = wellOptions.value.find(item => item.value === value);
  if (selected) {
    formData.connectedWell = selected.label;
  }
};

// 处理企业变化
const handleEnterpriseChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.managementUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 获取管线列表
const fetchPipelines = async () => {
  try {
    const res = await getPipelineList();
    if (res && res.data) {
      pipelineOptions.value = res.data.map(item => ({
        label: item.pipelineCode,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 获取窖井列表
const fetchWells = async () => {
  try {
    const res = await getWellList();
    if (res && res.data) {
      wellOptions.value = res.data.map(item => ({
        label: item.wellCode,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取窖井列表失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };
    
    // 处理时间字段格式
    if (submitData.installTime) {
      submitData.installTime = moment(submitData.installTime).format('YYYY-MM-DD HH:mm:ss');
    }

    let res;
    if (props.mode === 'add') {
      res = await savePipelineNode(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePipelineNode(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
  fetchPipelines();
  fetchWells();
});
</script>

<style scoped>
.pipeline-node-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 40px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 35px;
  margin-left: 5px;
}
</style> 