<template>
    <div class="component-model-list">
        <div
                v-for="item in modelData"
                :key="item.title"
                class="butt-normal"
                :class="[state.selectedTitle === item.id ? 'butt-active' : '']"
                @click="handleClick(item.id)"
        >
            {{ item.title }}
        </div>
        <div class="model_level2"
             :class="[state.selectedTitle === 2 ? 'select_model_2':
           state.selectedTitle === 3 ? 'select_model_3': '']"
             v-show="state.selectedTitle === 2 || state.selectedTitle === 3"
        >
            <div
                    v-for="item in model_l2Data"
                    :key="item.title"
                    class="butt-normal"
                    :class="[state.selectedLevel2 === item.id ? 'butt-active' : '']"
                    @click="handleClickLevel2(item.id)"
            >
                {{ item.title }}
            </div>
        </div>
    </div>
</template>

<script setup>
import bus from "@/utils/mitt.js";
import {reactive} from "vue";

const state = reactive({
    selectedTitle: 1,
    selectedLevel2: 4,
});
const modelData = reactive([
    {
        id: 1,
        title: "泄漏溯源分析",
    },
    {
        id: 2,
        title: "扩散范围分析",
    },
    {
        id: 3,
        title: "爆炸损伤范围分析",
    },
]);
const model_l2Data = reactive([
    {
        id: 4,
        title: "开放空间致灾后果预测",
    },
    {
        id: 5,
        title: "地下空间爆炸后果预测",
    },
]);

const handleClick = (e) => {
    //todo 激活模型
    state.selectedTitle = e;
    if (e === 2 || e === 3) {
        state.selectedLevel2 = 4;
    }
};

const handleClickLevel2 = (e) => {
    //todo 激活模型
    state.selectedLevel2 = e;
};
</script>

<style lang="scss" scoped>
.component-model-list {
  pointer-events: all;
  width: 178px;

  .model_level2 {
    width: 178px;
    margin-left: 30px;
    margin-top: 10px;
  }

  .select_model_2 {
      position: absolute;
      top: 79px;
      left: 162px;
  }

  .select_model_3 {
      position: absolute;
      top: 127px;
      left: 162px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(13, 37, 82, 0.8);
    border: 1px solid #182d54;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.8);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.8);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
