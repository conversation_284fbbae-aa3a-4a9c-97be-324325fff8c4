
<template>
  <div class="hidden-danger-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="statistics-row">
        <!-- 隐患状态统计 -->
        <div class="statistics-card status-card">
          <div class="card-header">
            <span class="card-title">隐患状态统计</span>
          </div>
          <div class="statistics-content">
            <div class="status-stats">
              <div class="stat-item total">
                <div class="stat-number">{{ statusStatistics.totalCount || 0 }}</div>
                <div class="stat-label">全部隐患</div>
              </div>
              <div class="stat-item unhandled">
                <div class="stat-number">{{ statusStatistics.unRectifyCount || 0 }}</div>
                <div class="stat-label">待整改</div>
              </div>
              <div class="stat-item handling">
                <div class="stat-number">{{ statusStatistics.rectifyingCount || 0 }}</div>
                <div class="stat-label">整改中</div>
              </div>
              <div class="stat-item review">
                <div class="stat-number">{{ statusStatistics.reviewCount || 0 }}</div>
                <div class="stat-label">待复查</div>
              </div>
              <div class="stat-item completed">
                <div class="stat-number">{{ statusStatistics.rectifiedCount || 0 }}</div>
                <div class="stat-label">已整改</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 隐患等级统计 -->
        <div class="statistics-card level-card">
          <div class="card-header">
            <span class="card-title">隐患等级统计</span>
          </div>
          <div class="statistics-content">
            <div class="level-stats">
              <div class="level-item major">
                <div class="level-number">{{ levelStatistics.bigRiskCount || 0 }}</div>
                <div class="level-label">重大隐患</div>
              </div>
              <div class="level-item large">
                <div class="level-number">{{ levelStatistics.largerRiskCount || 0 }}</div>
                <div class="level-label">较大隐患</div>
              </div>
              <div class="level-item general">
                <div class="level-number">{{ levelStatistics.generalRiskCount || 0 }}</div>
                <div class="level-label">一般隐患</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">隐患类型:</span>
          <el-select v-model="formData.dangerType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in dangerTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">隐患等级:</span>
          <el-select v-model="formData.dangerLevel" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in dangerLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">隐患状态:</span>
          <el-select v-model="formData.dangerStatus" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in dangerStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.keyWord" class="form-input" placeholder="输入上报人" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="dangerDesc" label="隐患描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="sourceTypeName" label="隐患来源" min-width="100" />
      <el-table-column prop="dangerTypeName" label="隐患类型" min-width="120" />
      <el-table-column prop="dangerLevelName" label="隐患等级" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getDangerLevelTagType(row.dangerLevel)">
            {{ row.dangerLevelName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="dangerStatusName" label="隐患状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getDangerStatusTagType(row.dangerStatus)">
            {{ row.dangerStatusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="ownershipUnitName" label="权属单位" min-width="120" show-overflow-tooltip />
      <el-table-column prop="responsibleUserName" label="责任人" min-width="100" />
      <el-table-column prop="reportUser" label="上报人" min-width="100" />
      <el-table-column prop="reportTime" label="上报时间" min-width="160" />
      <el-table-column label="操作" fixed="right" min-width="250" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleEdit(row)">编辑</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleLocation(row)">定位</span>
              <span class="operation-divider" v-if="showRectificationButton(row.dangerStatus) || showReviewButton(row.dangerStatus)">|</span>
              <span class="operation-btn-text" v-if="showRectificationButton(row.dangerStatus)" @click.stop="handleRectification(row)">整改</span>
              <span class="operation-btn-text" v-if="showReviewButton(row.dangerStatus)" @click.stop="handleReview(row)">复查</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <HiddenDangerDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />

    <!-- 整改管理弹窗 -->
    <RectificationDialog
      v-model:visible="rectificationDialogVisible"
      :danger-id="selectedDangerId"
      @success="handleDialogSuccess"
    />

    <!-- 复查弹窗 -->
    <ReviewDialog
      v-model:visible="reviewDialogVisible"
      :danger-id="selectedDangerId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus'
import { 
  getHiddenDangerPage, 
  deleteHiddenDanger, 
  getHiddenDangerDetail,
  getHiddenDangerStatusStatistics,
  getHiddenDangerLevelStatistics,
} from '@/api/comprehensive'
import { HIDDEN_DANGER_TYPE_OPTIONS, HIDDEN_DANGER_LEVEL_OPTIONS, HIDDEN_DANGER_STATUS_OPTIONS } from '@/constants/comprehensive'
import { misPosition } from '@/hooks/gishooks'
import HiddenDangerDialog from './components/HiddenDangerDialog.vue'
import RectificationDialog from './components/RectificationDialog.vue'
import ReviewDialog from './components/ReviewDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 统计数据
const statusStatistics = ref({})
const levelStatistics = ref({})

// 下拉选项数据
const dangerTypeOptions = ref(HIDDEN_DANGER_TYPE_OPTIONS)
const dangerLevelOptions = ref(HIDDEN_DANGER_LEVEL_OPTIONS)
const dangerStatusOptions = ref(HIDDEN_DANGER_STATUS_OPTIONS)

// 表单数据
const formData = ref({
  dangerType: '',
  dangerLevel: '',
  dangerStatus: '',
  keyWord: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 整改弹窗相关
const rectificationDialogVisible = ref(false)
const selectedDangerId = ref('')

// 复查弹窗相关
const reviewDialogVisible = ref(false)

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 获取隐患等级标签类型
const getDangerLevelTagType = (level) => {
  switch (level) {
    case 7002701: return 'danger'  // 重大隐患
    case 7002702: return 'warning' // 较大隐患
    case 7002703: return 'info'    // 一般隐患
    default: return 'info'
  }
}

// 获取隐患状态标签类型
const getDangerStatusTagType = (status) => {
  switch (status) {
    case 7003301: return 'danger'   // 待整改
    case 7003302: return 'warning'  // 整改中
    case 7003303: return 'primary'  // 待复查
    case 7003304: return 'success'  // 已整改
    default: return 'info'
  }
}

// 判断是否显示整改按钮（待整改、整改中状态）
const showRectificationButton = (status) => {
  return status === 7003301 || status === 7003302 // 待整改、整改中
}

// 判断是否显示复查按钮（待复查状态）
const showReviewButton = (status) => {
  return status === 7003303 // 待复查
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchHiddenDangerData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    dangerType: '',
    dangerLevel: '',
    dangerStatus: '',
    keyWord: ''
  }
  currentPage.value = 1
  fetchHiddenDangerData()
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 获取状态统计
    const statusRes = await getHiddenDangerStatusStatistics()
    if (statusRes && statusRes.code === 200) {
      statusStatistics.value = statusRes.data || {}
    }

    // 获取等级统计
    const levelRes = await getHiddenDangerLevelStatistics()
    if (levelRes && levelRes.code === 200) {
      levelStatistics.value = levelRes.data || {}
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取隐患分页数据
const fetchHiddenDangerData = async () => {
  loading.value = true;
  try {
    const params = {
      dangerType: formData.value.dangerType,
      dangerLevel: formData.value.dangerLevel,
      dangerStatus: formData.value.dangerStatus,
      keyWord: formData.value.keyWord
    }
    
    const res = await getHiddenDangerPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取隐患数据失败:', error)
    ElMessage.error('获取隐患数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchHiddenDangerData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchHiddenDangerData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getHiddenDangerDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取隐患详情失败')
    }
  } catch (error) {
    console.error('获取隐患详情失败:', error)
    ElMessage.error('获取隐患详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getHiddenDangerDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取隐患详情失败')
    }
  } catch (error) {
    console.error('获取隐患详情失败:', error)
    ElMessage.error('获取隐患详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该隐患信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteHiddenDanger(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchHiddenDangerData()
        fetchStatistics() // 重新获取统计数据
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除隐患失败:', error)
      ElMessage.error('删除隐患失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
}

// 处理整改
const handleRectification = (row) => {
  selectedDangerId.value = row.id
  rectificationDialogVisible.value = true
}

// 处理复查
const handleReview = (row) => {
  selectedDangerId.value = row.id
  reviewDialogVisible.value = true
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchHiddenDangerData()
  fetchStatistics() // 重新获取统计数据
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.hidden-danger-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const statisticsSection = container.querySelector('.statistics-section');
    const statisticsHeight = statisticsSection ? statisticsSection.offsetHeight : 0;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 48;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - statisticsHeight - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatistics(),
      fetchHiddenDangerData()
    ])
    setTimeout(() => {
      calculateTableMaxHeight();
    }, 100);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.hidden-danger-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  margin-bottom: 16px;
}

.statistics-row {
  display: flex;
  gap: 16px;
}

.statistics-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.statistics-content {
  padding: 20px;
}

.status-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.total .stat-number { color: #666; }
.unhandled .stat-number { color: #f56565; }
.handling .stat-number { color: #ed8936; }
.review .stat-number { color: #4299e1; }
.completed .stat-number { color: #48bb78; }

.level-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.level-item {
  text-align: center;
  flex: 1;
}

.level-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.level-label {
  font-size: 14px;
  color: #666;
}

.major .level-number { color: #e53e3e; }
.large .level-number { color: #dd6b20; }
.general .level-number { color: #38b2ac; }

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
