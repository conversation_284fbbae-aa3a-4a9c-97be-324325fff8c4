import request from '@/utils/request'
import moment from 'moment'

// 获取易涝点统计数据
export function getFloodPointRiskStatistics() {
  return request({
    url: '/drain/usmBasicFloodPoint/riskStatistics',
    method: 'get'
  })
}

// 获取雨水管网基础设施统计数据
export function getRainInfrastructureStatistics() {
  return request({
    url: '/drain/api/v1/situation/rain/infrastructure/statistics',
    method: 'get'
  })
}

// 获取污水管网基础设施统计数据
export function getSewageInfrastructureStatistics() {
  return request({
    url: '/drain/api/v1/situation/sewage/infrastructure/statistics',
    method: 'get'
  })
}

// 获取监测设备统计数据
export function getMonitorDeviceStatistics() {
  return request({
    url: '/drain/api/v1/monitor/analysis/device/statistics',
    method: 'get'
  })
}

// 获取风险监测设备统计数据
export function getRiskMonitorDeviceStatistics() {
  return request({
    url: '/drain/api/v1/situation/monitor/device/statistics',
    method: 'get'
  })
}

// 获取雨水管线风险统计数据（按风险级别）
export function getRainPipelineRiskStatistics() {
  return request({
    url: '/drain/api/v1/pipeline/risk/rain/risk/statistics',
    method: 'get'
  })
}

// 获取污水管线风险统计数据（按风险级别）
export function getSewagePipelineRiskStatistics() {
  return request({
    url: '/drain/api/v1/pipeline/risk/sewage/risk/statistics',
    method: 'get'
  })
}

// 获取污水厂风险统计数据（按风险级别）
export function getFactoryPipelineRiskStatistics() {
  return request({
    url: '/drain/api/v1/pipeline/risk/factory/risk/statistics',
    method: 'get'
  })
}

// 获取泵站风险统计数据（按风险级别）
export function getStationPipelineRiskStatistics() {
  return request({
    url: '/drain/api/v1/pipeline/risk/station/risk/statistics',
    method: 'get'
  })
}

// 获取雨水管线区域管线风险统计数据
export function getRainRegionRiskStatistics() {
  return request({
    url: '/drain/api/v1/pipeline/risk/rain/region/statistics',
    method: 'get'
  })
}

// 获取污水管线区域管线风险统计数据
export function getSewageRegionRiskStatistics() {
  return request({
    url: '/drain/api/v1/pipeline/risk/sewage/region/statistics',
    method: 'get'
  })
}

// 获取隐患统计数据
export function getHiddenDangerStatisticsScreen(pageNum = 1, pageSize = 10, dayIndex = 7) {
  return request({
    url: '/drain/api/v1/pipeline/risk/hiddenDanger/statistics',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      dayIndex
    }
  })
}

// 获取隐患类型统计数据
export function getHiddenDangerTypeStatistics(dayIndex = 7) {
  return request({
    url: '/drain/api/v1/pipeline/risk/hiddenDanger/type/statistics',
    method: 'get',
    params: {
      dayIndex
    }
  })
}

// 获取隐患整改分析数据
export function getHiddenDangerRectifyAnalysis(dayIndex = 7) {
  return request({
    url: '/drain/api/v1/pipeline/risk/hiddenDanger/rectify/analysis',
    method: 'get',
    params: {
      dayIndex
    }
  })
}

// 获取雨水管线风险清单
export function getRainPipelineRiskList(pageNum = 1, pageSize = 10) {
  return request({
    url: '/drain/api/v1/pipeline/risk/rain/pipeline/list',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 获取污水管线风险清单
export function getSewagePipelineRiskList(pageNum = 1, pageSize = 10) {
  return request({
    url: '/drain/api/v1/pipeline/risk/sewage/pipeline/list',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 获取污水厂风险清单
export function getFactoryRiskList(pageNum = 1, pageSize = 10) {
  return request({
    url: '/drain/api/v1/pipeline/risk/factory/list',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 获取泵站风险清单
export function getStationRiskList(pageNum = 1, pageSize = 10) {
  return request({
    url: '/drain/api/v1/pipeline/risk/station/list',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 获取雨水管线风险统计数据（旧接口，保持兼容性）
export function getRainRiskStatistics() {
  return request({
    url: '/drain/api/v1/situation/rain/risk/statistics',
    method: 'get'
  })
}

// 获取污水管线风险统计数据（旧接口，保持兼容性）
export function getSewageRiskStatistics() {
  return request({
    url: '/drain/api/v1/situation/sewage/risk/statistics',
    method: 'get'
  })
}

// 获取污水厂风险统计数据（旧接口，保持兼容性）
export function getFactoryRiskStatistics() {
  return request({
    url: '/drain/api/v1/situation/factory/risk/statistics',
    method: 'get'
  })
}

// 获取泵站风险统计数据（旧接口，保持兼容性）
export function getStationRiskStatistics() {
  return request({
    url: '/drain/api/v1/situation/station/risk/statistics',
    method: 'get'
  })
}

// 获取隐患整改统计数据
export function getHiddenDangerStatistics() {
  return request({
    url: '/drain/api/v1/situation/hiddenDanger/statistics',
    method: 'get'
  })
}

// 获取报警处置统计数据
export function getAlarmStatistics(params = {}) {
  return request({
    url: '/drain/api/v1/situation/monitor/alarm/statistics',
    method: 'get',
    params
  })
}

// 获取事故处置统计数据
export function getEventStatistics(params = {}) {
  return request({
    url: '/drain/api/v1/situation/monitor/event/statistics',
    method: 'get',
    params
  })
}

// 获取CCTV检测分页数据
export function getCCTVPage(params) {
  return request({
    url: `/drain/usmBasicCctv/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取CCTV检测详情
export function getCCTVDetail(id) {
  return request({
    url: `/drain/usmBasicCctv/${id}`,
    method: 'get'
  })
}

// 新增CCTV检测信息
export function saveCCTV(data) {
  const formattedData = { ...data };
  if (formattedData.detectTime) {
    formattedData.detectTime = moment(formattedData.detectTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicCctv/save',
    method: 'post',
    data: formattedData
  })
}

// 更新CCTV检测信息
export function updateCCTV(data) {
  const formattedData = { ...data };
  if (formattedData.detectTime) {
    formattedData.detectTime = moment(formattedData.detectTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicCctv/update',
    method: 'post',
    data: formattedData
  })
}

// 删除CCTV检测信息
export function deleteCCTV(id) {
  return request({
    url: `/drain/usmBasicCctv/${id}`,
    method: 'delete'
  })
}

// 获取排水口分页数据
export function getOutletPage(params) {
  return request({
    url: `/drain/usmBasicDrainOutlet/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取排水口详情
export function getOutletDetail(id) {
  return request({
    url: `/drain/usmBasicDrainOutlet/${id}`,
    method: 'get'
  })
}

// 新增排水口
export function saveOutlet(data) {
  return request({
    url: '/drain/usmBasicDrainOutlet/save',
    method: 'post',
    data: data
  })
}

// 更新排水口
export function updateOutlet(data) {
  return request({
    url: '/drain/usmBasicDrainOutlet/update',
    method: 'post',
    data: data
  })
}

// 删除排水口
export function deleteOutlet(id) {
  return request({
    url: `/drain/usmBasicDrainOutlet/${id}`,
    method: 'delete'
  })
}

// 获取管线分页数据
export function getPipelinePage(params) {
  return request({
    url: `/drain/usmBasicPipeline/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管线列表数据（不分页）
export function getPipelineList(params) {
  return request({
    url: '/drain/usmBasicPipeline/list',
    method: 'post',
    data: params
  })
}

// 获取权属单位列表
export function getEnterpriseList() {
  return request({
    url: '/drain/usmEnterpriseBasicInfo/list',
    method: 'post',
    data: {}
  })
}

// 新增管线
export function savePipeline(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicPipeline/save',
    method: 'post',
    data: formattedData
  })
}

// 更新管线
export function updatePipeline(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicPipeline/update',
    method: 'post',
    data: formattedData
  })
}

// 获取管线详情
export function getPipelineDetail(id) {
  return request({
    url: `/drain/usmBasicPipeline/${id}`,
    method: 'get'
  })
}

// 删除管线
export function deletePipeline(id) {
  return request({
    url: `/drain/usmBasicPipeline/${id}`,
    method: 'delete'
  })
}

// 获取管点分页数据
export function getPointPage(params) {
  return request({
    url: `/drain/usmBasicPoint/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管点详情
export function getPointDetail(id) {
  return request({
    url: `/drain/usmBasicPoint/${id}`,
    method: 'get'
  })
}

// 新增管点
export function savePoint(data) {
  const formattedData = { ...data };
  if (formattedData.installTime) {
    formattedData.installTime = moment(formattedData.installTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicPoint/save',
    method: 'post',
    data: formattedData
  })
}

// 更新管点
export function updatePoint(data) {
  const formattedData = { ...data };
  if (formattedData.installTime) {
    formattedData.installTime = moment(formattedData.installTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicPoint/update',
    method: 'post',
    data: formattedData
  })
}

// 删除管点
export function deletePoint(id) {
  return request({
    url: `/drain/usmBasicPoint/${id}`,
    method: 'delete'
  })
}

// 获取管线维修记录分页数据
export function getRepairPage(params) {
  return request({
    url: `/drain/usmBasicRepair/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管线维修记录详情
export function getRepairDetail(id) {
  return request({
    url: `/drain/usmBasicRepair/${id}`,
    method: 'get'
  })
}

// 新增管线维修记录
export function saveRepair(data) {
  const formattedData = { ...data };
  if (formattedData.repairTime) {
    formattedData.repairTime = moment(formattedData.repairTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicRepair/save',
    method: 'post',
    data: formattedData
  })
}

// 更新管线维修记录
export function updateRepair(data) {
  const formattedData = { ...data };
  if (formattedData.repairTime) {
    formattedData.repairTime = moment(formattedData.repairTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicRepair/update',
    method: 'post',
    data: formattedData
  })
}

// 删除管线维修记录
export function deleteRepair(id) {
  return request({
    url: `/drain/usmBasicRepair/${id}`,
    method: 'delete'
  })
}

// 获取雨水篦子分页数据
export function getGratePage(params) {
  return request({
    url: `/drain/usmBasicDrainGrate/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取雨水篦子详情
export function getGrateDetail(id) {
  return request({
    url: `/drain/usmBasicDrainGrate/${id}`,
    method: 'get'
  })
}

// 新增雨水篦子
export function saveGrate(data) {
  return request({
    url: '/drain/usmBasicDrainGrate/save',
    method: 'post',
    data: data
  })
}

// 更新雨水篦子
export function updateGrate(data) {
  return request({
    url: '/drain/usmBasicDrainGrate/update',
    method: 'post',
    data: data
  })
}

// 删除雨水篦子
export function deleteGrate(id) {
  return request({
    url: `/drain/usmBasicDrainGrate/${id}`,
    method: 'delete'
  })
}

// 获取窨井分页数据
export function getManholeWellPage(params) {
  return request({
    url: `/drain/usmBasicWell/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取窨井详情
export function getManholeWellDetail(id) {
  return request({
    url: `/drain/usmBasicWell/${id}`,
    method: 'get'
  })
}

// 新增窨井
export function saveManholeWell(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicWell/save',
    method: 'post',
    data: formattedData
  })
}

// 更新窨井
export function updateManholeWell(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicWell/update',
    method: 'post',
    data: formattedData
  })
}

// 删除窨井
export function deleteManholeWell(id) {
  return request({
    url: `/drain/usmBasicWell/${id}`,
    method: 'delete'
  })
}

// 获取泵站分页数据
export function getPumpStationPage(params) {
  return request({
    url: `/drain/usmBasicPumpStation/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取泵站详情
export function getPumpStationDetail(id) {
  return request({
    url: `/drain/usmBasicPumpStation/${id}`,
    method: 'get'
  })
}

// 新增泵站
export function savePumpStation(data) {
  return request({
    url: '/drain/usmBasicPumpStation/save',
    method: 'post',
    data: data
  })
}

// 更新泵站
export function updatePumpStation(data) {
  return request({
    url: '/drain/usmBasicPumpStation/update',
    method: 'post',
    data: data
  })
}

// 删除泵站
export function deletePumpStation(id) {
  return request({
    url: `/drain/usmBasicPumpStation/${id}`,
    method: 'delete'
  })
}

// 获取泵站列表数据（不分页）
export function getPumpStationList(params) {
  return request({
    url: '/drain/usmBasicPumpStation/list',
    method: 'post',
    data: params
  })
}

// 获取污水厂分页数据
export function getSewagePlantPage(params) {
  return request({
    url: `/drain/usmBasicSewageFactory/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取污水厂详情
export function getSewagePlantDetail(id) {
  return request({
    url: `/drain/usmBasicSewageFactory/${id}`,
    method: 'get'
  })
}

// 新增污水厂
export function saveSewagePlant(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicSewageFactory/save',
    method: 'post',
    data: formattedData
  })
}

// 更新污水厂
export function updateSewagePlant(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicSewageFactory/update',
    method: 'post',
    data: formattedData
  })
}

// 删除污水厂
export function deleteSewagePlant(id) {
  return request({
    url: `/drain/usmBasicSewageFactory/${id}`,
    method: 'delete'
  })
}

// 获取污水厂列表数据（不分页）
export function getSewageFactoryList(params) {
  return request({
    url: '/drain/usmBasicSewageFactory/list',
    method: 'post',
    data: params
  })
}

// 获取易涝点分页数据
export function getFloodPointPage(params) {
  return request({
    url: `/drain/usmBasicFloodPoint/page/${params.pageNum}/${params.pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取易涝点详情
export function getFloodPointDetail(id) {
  return request({
    url: `/drain/usmBasicFloodPoint/${id}`,
    method: 'get'
  })
}

// 新增易涝点
export function saveFloodPoint(data) {
  const formattedData = { ...data };
  if (formattedData.happenTime) {
    formattedData.happenTime = moment(formattedData.happenTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.rectificationTime) {
    formattedData.rectificationTime = moment(formattedData.rectificationTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicFloodPoint/save',
    method: 'post',
    data: formattedData
  })
}

// 更新易涝点
export function updateFloodPoint(data) {
  const formattedData = { ...data };
  if (formattedData.happenTime) {
    formattedData.happenTime = moment(formattedData.happenTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.rectificationTime) {
    formattedData.rectificationTime = moment(formattedData.rectificationTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmBasicFloodPoint/update',
    method: 'post',
    data: formattedData
  })
}

// 删除易涝点
export function deleteFloodPoint(id) {
  return request({
    url: `/drain/usmBasicFloodPoint/${id}`,
    method: 'delete'
  })
}

// 获取监测设备分页数据
export function getMonitorDevicePage(params) {
  const { pageNum, pageSize, ...data } = params
  return request({
    url: `/drain/usmMonitorDevice/page/${pageNum}/${pageSize}`,
    method: 'post',
    data
  })
}

// 获取监测设备详情
export function getMonitorDeviceDetail(id) {
  return request({
    url: `/drain/usmMonitorDevice/${id}`,
    method: 'get'
  })
}

// 新增监测设备
export function saveMonitorDevice(data) {
  return request({
    url: '/drain/usmMonitorDevice/save',
    method: 'post',
    data: data
  })
}

// 更新监测设备
export function updateMonitorDevice(data) {
  return request({
    url: '/drain/usmMonitorDevice/update',
    method: 'post',
    data: data
  })
}

// 删除监测设备
export function deleteMonitorDevice(id) {
  return request({
    url: `/drain/usmMonitorDevice/${id}`,
    method: 'delete'
  })
}

// 获取窨井列表数据（不分页）
export function getWellList(params) {
  return request({
    url: '/drain/usmBasicWell/list',
    method: 'post',
    data: params
  })
}

// 获取监测设备列表（用于设备选择下拉）
export function getMonitorDeviceList(params) {
  return request({
    url: '/drain/usmMonitorDevice/list',
    method: 'post',
    data: params
  })
}

// 获取监测指标列表（用于设备类型和监测指标下拉）
export function getMonitorIndicatorsList(params) {
  return request({
    url: '/drain/usmMonitorIndicators/list',
    method: 'post',
    data: params
  })
}

// 获取报警阈值分页数据
export function getAlarmThresholdPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmAlarmThreshold/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取报警阈值详情
export function getAlarmThresholdDetail(id) {
  return request({
    url: `/drain/usmAlarmThreshold/${id}`,
    method: 'get'
  })
}

// 新增报警阈值
export function saveAlarmThreshold(data) {
  return request({
    url: '/drain/usmAlarmThreshold/save',
    method: 'post',
    data: data
  })
}

// 更新报警阈值
export function updateAlarmThreshold(data) {
  return request({
    url: '/drain/usmAlarmThreshold/update',
    method: 'post',
    data: data
  })
}

// 删除报警阈值
export function deleteAlarmThreshold(id) {
  return request({
    url: `/drain/usmAlarmThreshold/${id}`,
    method: 'delete'
  })
}

// ============ 排水报警相关接口 ============

// 获取报警信息分页列表
export function getDrainAlarmList(page, size, params = {}) {
  return request({
    url: `/drain/usmMonitorAlarm/search/${page}/${size}`,
    method: 'post',
    data: params
  })
}

// 获取报警统计数据
export function getDrainAlarmStatistics(params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/statistics',
    method: 'post',
    data: params
  })
}
// 获取报警类型
export function getAlarmType() {
  return request({
    url: `/drain/usmMonitorAlarm/getAlarmType`,
    method: 'get'
  })
}
// 获取报警级别统计数据
export function getDrainAlarmLevelStatistics(params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/level/statistics',
    method: 'post',
    data: params
  })
}

// 获取报警详情
export function getDrainAlarmDetail(id) {
  return request({
    url: `/drain/usmMonitorAlarm/${id}`,
    method: 'get'
  })
}

// 获取报警监测曲线数据
export function getDrainAlarmMonitorCurve(params) {
  return request({
    url: '/drain/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  })
}

// 获取报警记录列表（根据设备ID）
export function getDrainAlarmRecords(deviceId) {
  return request({
    url: `/drain/usmMonitorAlarm/alarmRecord/${deviceId}`,
    method: 'get'
  })
}

// 获取报警状态记录列表
export function getDrainAlarmStatusList(params) {
  return request({
    url: '/drain/usmMonitorAlarmStatus/list',
    method: 'post',
    data: params
  })
}

// 确认报警
export function confirmDrainAlarm(data) {
  return request({
    url: '/drain/usmMonitorAlarm/alarm/confirm',
    method: 'post',
    data: data
  })
}

// 处置报警
export function handleDrainAlarm(data) {
  return request({
    url: '/drain/usmMonitorAlarm/handle',
    method: 'post',
    data: data
  })
}

// 误报上报
export function reportFalseDrainAlarm(data) {
  return request({
    url: '/drain/usmMonitorAlarm/false-report',
    method: 'post',
    data: data
  })
}

// 获取处置记录列表
export function getDrainDisposalRecords(alarmId) {
  return request({
    url: `/drain/usmMonitorAlarmDisposal/list/${alarmId}`,
    method: 'get'
  })
}

// 获取排水报警处置统计数据（使用级别统计接口）
export function getDrainAlarmDisposalStatistics(params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/level/statistics',
    method: 'post',
    data: params
  })
}

// 获取报警处置记录列表
export function getDrainAlarmHandleList(alarmId) {
  return request({
    url: `/drain/usmMonitorAlarm/alarm/handleList/${alarmId}`,
    method: 'get'
  })
}

// 新增报警处置记录
export function addDrainAlarmHandle(data) {
  return request({
    url: '/drain/usmMonitorAlarm/alarm/handle',
    method: 'post',
    data: data
  })
}

// 删除报警处置记录
export function deleteDrainAlarmHandle(id) {
  return request({
    url: `/drain/usmMonitorAlarmStatus/${id}`,
    method: 'delete'
  })
}

// ============ 新增报警统计分析接口 ============

// 获取处置、误报统计数据
export function getDrainAlarmDisposalSituation(params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/statistics/disposalSituation',
    method: 'post',
    data: params
  })
}

// 获取报警趋势分析统计数据
export function getDrainAlarmTrendStatistics(params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/trend/statistics',
    method: 'post',
    data: params
  })
}

// 获取高发报警设备统计数据
export function getDrainAlarmHighFrequencyDevices(params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/device/high-frequency',
    method: 'post',
    data: params
  })
}

// 获取企业报警信息统计数据
export function getDrainAlarmEnterpriseStatistics(pageNum = 1, pageSize = 10, params = {}) {
  return request({
    url: '/drain/usmAlarmStatisticsAnalysis/enterprise/statistics',
    method: 'post',
    data: {
      ...params,
      pageNum,
      pageSize
    }
  })
}

// ============ 基础数据统计分析相关接口 ============

// 获取雨水管网统计数据
export function getRainStatistics(params = {}) {
  return request({
    url: '/drain/usmBasicStatisticsAnalysis/statistics/rain',
    method: 'post',
    data: params
  })
}

// 获取污水管网统计数据
export function getSewageStatistics(params = {}) {
  return request({
    url: '/drain/usmBasicStatisticsAnalysis/statistics/sewage',
    method: 'post',
    data: params
  })
}

// 获取雨水管网管龄统计
export function getRainAgeStatistics(params = {}) {
  return request({
    url: '/drain/usmBasicStatisticsAnalysis/age/rain',
    method: 'post',
    data: params
  })
}

// 获取污水管网管龄统计
export function getSewageAgeStatistics(params = {}) {
  return request({
    url: '/drain/usmBasicStatisticsAnalysis/age/sewage',
    method: 'post',
    data: params
  })
}

// 获取雨水管网材质统计
export function getRainMaterialStatistics(params = {}) {
  return request({
    url: '/drain/usmBasicStatisticsAnalysis/material/rain',
    method: 'post',
    data: params
  })
}

// 获取污水管网材质统计
export function getSewageMaterialStatistics(params = {}) {
  return request({
    url: '/drain/usmBasicStatisticsAnalysis/material/sewage',
    method: 'post',
    data: params
  })
}

// ============ 防汛物资管理相关接口 ============

// 获取防汛物资分页数据
export function getFloodMaterialPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmFloodMaterials/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取防汛物资详情
export function getFloodMaterialDetail(id) {
  return request({
    url: `/drain/usmFloodMaterials/${id}`,
    method: 'get'
  })
}

// 新增防汛物资
export function saveFloodMaterial(data) {
  return request({
    url: '/drain/usmFloodMaterials/save',
    method: 'post',
    data: data
  })
}

// 更新防汛物资
export function updateFloodMaterial(data) {
  return request({
    url: '/drain/usmFloodMaterials/update',
    method: 'post',
    data: data
  })
}

// 删除防汛物资
export function deleteFloodMaterial(id) {
  return request({
    url: `/drain/usmFloodMaterials/${id}`,
    method: 'delete'
  })
}

// ============ 危险源信息管理相关接口 ============

// 获取危险源分页数据
export function getDangerPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskDanger/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取危险源详情
export function getDangerDetail(id) {
  return request({
    url: `/drain/usmRiskDanger/${id}`,
    method: 'get'
  })
}

// 新增危险源
export function saveDanger(data) {
  return request({
    url: '/drain/usmRiskDanger/save',
    method: 'post',
    data: data
  })
}

// 更新危险源
export function updateDanger(data) {
  return request({
    url: '/drain/usmRiskDanger/update',
    method: 'post',
    data: data
  })
}

// 删除危险源
export function deleteDanger(id) {
  return request({
    url: `/drain/usmRiskDanger/${id}`,
    method: 'delete'
  })
}

// ============ 防护目标信息管理相关接口 ============

// 获取防护目标分页数据
export function getProtectionPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskProtect/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取防护目标详情
export function getProtectionDetail(id) {
  return request({
    url: `/drain/usmRiskProtect/${id}`,
    method: 'get'
  })
}

// 新增防护目标
export function saveProtection(data) {
  return request({
    url: '/drain/usmRiskProtect/save',
    method: 'post',
    data: data
  })
}

// 更新防护目标
export function updateProtection(data) {
  return request({
    url: '/drain/usmRiskProtect/update',
    method: 'post',
    data: data
  })
}

// 删除防护目标
export function deleteProtection(id) {
  return request({
    url: `/drain/usmRiskProtect/${id}`,
    method: 'delete'
  })
}

// ============ 防汛应急辅助决策方案相关接口 ============

// 获取防汛应急辅助决策方案分页数据
export function getFloodEmergencySchemeePage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmFloodEmergencyScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取防汛应急辅助决策方案详情
export function getFloodEmergencySchemeDetail(id) {
  return request({
    url: `/drain/usmFloodEmergencyScheme/${id}`,
    method: 'get'
  })
}

// 新增防汛应急辅助决策方案
export function saveFloodEmergencyScheme(data) {
  return request({
    url: '/drain/usmFloodEmergencyScheme/save',
    method: 'post',
    data: data
  })
}

// 更新防汛应急辅助决策方案
export function updateFloodEmergencyScheme(data) {
  return request({
    url: '/drain/usmFloodEmergencyScheme/update',
    method: 'post',
    data: data
  })
}

// 删除防汛应急辅助决策方案
export function deleteFloodEmergencyScheme(id) {
  return request({
    url: `/drain/usmFloodEmergencyScheme/${id}`,
    method: 'delete'
  })
}

// ============ 排水应急事件管理相关接口 ============

// 获取应急事件分页数据
export function getEmergencyEventPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskEvent/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取应急事件详情
export function getEmergencyEventDetail(id) {
  return request({
    url: `/drain/usmRiskEvent/${id}`,
    method: 'get'
  })
}

// 新增应急事件
export function saveEmergencyEvent(data) {
  const formattedData = { ...data };
  if (formattedData.eventTime) {
    formattedData.eventTime = moment(formattedData.eventTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.receiveTime) {
    formattedData.receiveTime = moment(formattedData.receiveTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.handleTime) {
    formattedData.handleTime = moment(formattedData.handleTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskEvent/save',
    method: 'post',
    data: formattedData
  })
}

// 更新应急事件
export function updateEmergencyEvent(data) {
  const formattedData = { ...data };
  if (formattedData.eventTime) {
    formattedData.eventTime = moment(formattedData.eventTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.receiveTime) {
    formattedData.receiveTime = moment(formattedData.receiveTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.handleTime) {
    formattedData.handleTime = moment(formattedData.handleTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskEvent/update',
    method: 'post',
    data: formattedData
  })
}

// 删除应急事件
export function deleteEmergencyEvent(id) {
  return request({
    url: `/drain/usmRiskEvent/${id}`,
    method: 'delete'
  })
}

// 根据事件分类获取处置方案列表
export function getFloodEmergencySchemeList(params) {
  return request({
    url: '/drain/usmFloodEmergencyScheme/list',
    method: 'post',
    data: params
  })
}

// ============ 管网流量监测相关接口 ============

// 获取设备类型
export function getDeviceType(type) {
  return request({
    url: '/drain/usmMonitorDevice/getDeviceType',
    method: 'get',
    params: { type }
  })
}

// 获取设备状态统计
export function getDeviceTypeStatusStatistics(type) {
  return request({
    url: '/drain/usmMonitorDevice/getDeviceTypeStatusStatistics',
    method: 'get',
    params: { type }
  })
}

// 根据设备ID查询监测指标
export function getMonitorIndicators(deviceId) {
  return request({
    url: `/drain/usmMonitorRecord/monitorIndicators/${deviceId}`,
    method: 'get'
  })
}

// 获取监测曲线数据
export function getMonitorCurve(data) {
  const formattedData = { ...data };
  if (formattedData.startTime) {
    formattedData.startTime = moment(formattedData.startTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.endTime) {
    formattedData.endTime = moment(formattedData.endTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: formattedData
  })
}

// 获取监测历史记录分页数据
export function getMonitorRecordPage(pageNum, pageSize, data) {
  const formattedData = { ...data };
  if (formattedData.startTime) {
    formattedData.startTime = moment(formattedData.startTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.endTime) {
    formattedData.endTime = moment(formattedData.endTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: `/drain/usmMonitorRecord/monitorCurvePage/${pageNum}/${pageSize}`,
    method: 'post',
    data: formattedData
  })
}

// 获取设备离线记录分页数据
export function getOfflineRecordsPage(pageNum, pageSize, data) {
  const formattedData = { ...data };
  if (formattedData.startTime) {
    formattedData.startTime = moment(formattedData.startTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.endTime) {
    formattedData.endTime = moment(formattedData.endTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: `/drain/usmMonitorRecord/offlineRecords/${pageNum}/${pageSize}`,
    method: 'post',
    data: formattedData
  })
}

// ============ 排水管网风险评估相关接口 ============

// 获取管网按风险等级统计数据
export function getRiskAssessmentStatistics() {
  return request({
    url: '/drain/usmRiskAssessmentPipeline/statistics',
    method: 'get'
  })
}

// 获取风险评估分页数据
export function getRiskAssessmentPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskAssessmentPipeline/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取风险评估详情
export function getRiskAssessmentDetail(id) {
  return request({
    url: `/drain/usmRiskAssessmentPipeline/${id}`,
    method: 'get'
  })
}

// 更新风险评估
export function updateRiskAssessment(data) {
  const formattedData = { ...data };
  if (formattedData.assessmentDate) {
    formattedData.assessmentDate = moment(formattedData.assessmentDate).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskAssessmentPipeline/update',
    method: 'post',
    data: formattedData
  })
}

// ============ 污水厂风险评估相关接口 ============

// 获取污水厂按风险等级统计数据
export function getSewagePlantRiskAssessmentStatistics() {
  return request({
    url: '/drain/usmRiskAssessmentFactory/statistics',
    method: 'get'
  })
}

// 获取污水厂风险评估分页数据
export function getSewagePlantRiskAssessmentPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskAssessmentFactory/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取污水厂风险评估详情
export function getSewagePlantRiskAssessmentDetail(id) {
  return request({
    url: `/drain/usmRiskAssessmentFactory/${id}`,
    method: 'get'
  })
}

// 更新污水厂风险评估
export function updateSewagePlantRiskAssessment(data) {
  const formattedData = { ...data };
  if (formattedData.assessmentDate) {
    formattedData.assessmentDate = moment(formattedData.assessmentDate).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskAssessmentFactory/update',
    method: 'post',
    data: formattedData
  })
}

// ============ 泵站风险评估相关接口 ============

// 获取泵站按风险等级统计数据
export function getPumpStationRiskAssessmentStatistics() {
  return request({
    url: '/drain/usmRiskAssessmentStation/statistics',
    method: 'get'
  })
}

// 获取泵站风险评估分页数据
export function getPumpStationRiskAssessmentPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskAssessmentStation/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取泵站风险评估详情
export function getPumpStationRiskAssessmentDetail(id) {
  return request({
    url: `/drain/usmRiskAssessmentStation/${id}`,
    method: 'get'
  })
}

// 更新泵站风险评估
export function updatePumpStationRiskAssessment(data) {
  const formattedData = { ...data };
  if (formattedData.assessmentDate) {
    formattedData.assessmentDate = moment(formattedData.assessmentDate).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskAssessmentStation/update',
    method: 'post',
    data: formattedData
  })
}

// ============ 隐患信息管理相关接口 ============

// 获取隐患状态统计数据
export function getHiddenDangerStatusStatistics() {
  return request({
    url: '/drain/usmRiskHiddenDanger/statisticsByStatus',
    method: 'get'
  })
}

// 获取隐患等级统计数据
export function getHiddenDangerLevelStatistics() {
  return request({
    url: '/drain/usmRiskHiddenDanger/statisticsByLevel',
    method: 'get'
  })
}

// 获取隐患分页数据
export function getHiddenDangerPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmRiskHiddenDanger/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取隐患详情
export function getHiddenDangerDetail(id) {
  return request({
    url: `/drain/usmRiskHiddenDanger/${id}`,
    method: 'get'
  })
}

// 新增隐患信息
export function saveHiddenDanger(data) {
  const formattedData = { ...data };
  if (formattedData.reportTime) {
    formattedData.reportTime = moment(formattedData.reportTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.rectificationDeadline) {
    formattedData.rectificationDeadline = moment(formattedData.rectificationDeadline).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskHiddenDanger/save',
    method: 'post',
    data: formattedData
  })
}

// 更新隐患信息
export function updateHiddenDanger(data) {
  const formattedData = { ...data };
  if (formattedData.reportTime) {
    formattedData.reportTime = moment(formattedData.reportTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.rectificationDeadline) {
    formattedData.rectificationDeadline = moment(formattedData.rectificationDeadline).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskHiddenDanger/update',
    method: 'post',
    data: formattedData
  })
}

// 删除隐患信息
export function deleteHiddenDanger(id) {
  return request({
    url: `/drain/usmRiskHiddenDanger/${id}`,
    method: 'delete'
  })
}

// 获取隐患整改列表
export function getHiddenDangerHandleList(id) {
  return request({
    url: `/drain/usmRiskHiddenDanger/handleList/${id}`,
    method: 'get'
  })
}

// 新增/编辑隐患整改记录
export function addHiddenDangerHandle(data) {
  const formattedData = { ...data };
  if (formattedData.dealTime) {
    formattedData.dealTime = moment(formattedData.dealTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskHiddenDanger/addHandle',
    method: 'post',
    data: formattedData
  })
}

// 删除隐患整改记录
export function deleteHiddenDangerHandle(id) {
  return request({
    url: `/drain/usmRiskHiddenDanger/deleteHandle/${id}`,
    method: 'delete'
  })
}

// 隐患复查
export function reviewHiddenDanger(data) {
  const formattedData = { ...data };
  if (formattedData.dealTime) {
    formattedData.dealTime = moment(formattedData.dealTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmRiskHiddenDanger/review',
    method: 'post',
    data: formattedData
  })
}

// 获取隐患整改情况时间线
export function getHiddenDangerStatusList(params) {
  return request({
    url: '/drain/usmRiskHiddenDangerStatus/list',
    method: 'post',
    data: params
  })
}

// ============ 管网运维改造辅助决策相关接口 ============

// 获取管网改造辅助决策分页数据
export function getPipelineReformSchemePage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmPipelineReformScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取管网改造辅助决策详情
export function getPipelineReformSchemeDetail(id) {
  return request({
    url: `/drain/usmPipelineReformScheme/${id}`,
    method: 'get'
  })
}

// 新增管网改造辅助决策
export function savePipelineReformScheme(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmPipelineReformScheme/save',
    method: 'post',
    data: formattedData
  })
}

// 更新管网改造辅助决策
export function updatePipelineReformScheme(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmPipelineReformScheme/update',
    method: 'post',
    data: formattedData
  })
}

// 删除管网改造辅助决策
export function deletePipelineReformScheme(id) {
  return request({
    url: `/drain/usmPipelineReformScheme/${id}`,
    method: 'delete'
  })
}

// ============ 易涝点改造辅助决策相关接口 ============

// 获取易涝点改造辅助决策分页数据
export function getPointReformSchemePage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmPointReformScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取易涝点改造辅助决策详情
export function getPointReformSchemeDetail(id) {
  return request({
    url: `/drain/usmPointReformScheme/${id}`,
    method: 'get'
  })
}

// 新增易涝点改造辅助决策
export function savePointReformScheme(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmPointReformScheme/save',
    method: 'post',
    data: formattedData
  })
}

// 更新易涝点改造辅助决策
export function updatePointReformScheme(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmPointReformScheme/update',
    method: 'post',
    data: formattedData
  })
}

// 删除易涝点改造辅助决策
export function deletePointReformScheme(id) {
  return request({
    url: `/drain/usmPointReformScheme/${id}`,
    method: 'delete'
  })
}

// ============ 安全评估报告管理相关接口 ============

// 获取安全评估报告分页数据
export function getAssessReportPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmAssessReport/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取安全评估报告详情
export function getAssessReportDetail(id) {
  return request({
    url: `/drain/usmAssessReport/${id}`,
    method: 'get'
  })
}

// 新增安全评估报告
export function saveAssessReport(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmAssessReport/save',
    method: 'post',
    data: formattedData
  })
}

// 更新安全评估报告
export function updateAssessReport(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmAssessReport/update',
    method: 'post',
    data: formattedData
  })
}

// 删除安全评估报告
export function deleteAssessReport(id) {
  return request({
    url: `/drain/usmAssessReport/${id}`,
    method: 'delete'
  })
}

// 下载安全评估报告文件
export function downloadAssessReport(fileUrl) {
  return request({
    url: fileUrl,
    method: 'get',
    responseType: 'blob'
  })
}

// ============ 安全评估报告审核发布相关接口 ============

// 获取安全评估报告审核发布分页数据
export function getAssessReportSendPage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmAssessReportSend/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取安全评估报告审核发布详情
export function getAssessReportSendDetail(id) {
  return request({
    url: `/drain/usmAssessReportSend/${id}`,
    method: 'get'
  })
}

// 新增安全评估报告审核发布
export function saveAssessReportSend(data) {
  const formattedData = { ...data };
  if (formattedData.sendTime) {
    formattedData.sendTime = moment(formattedData.sendTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmAssessReportSend/save',
    method: 'post',
    data: formattedData
  })
}

// 更新安全评估报告审核发布
export function updateAssessReportSend(data) {
  const formattedData = { ...data };
  if (formattedData.sendTime) {
    formattedData.sendTime = moment(formattedData.sendTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmAssessReportSend/update',
    method: 'post',
    data: formattedData
  })
}

// 删除安全评估报告审核发布
export function deleteAssessReportSend(id) {
  return request({
    url: `/drain/usmAssessReportSend/${id}`,
    method: 'delete'
  })
}

// ============ 安全评估报告模板管理相关接口 ============

// 获取安全评估报告模板分页数据
export function getAssessReportTemplatePage(pageNum, pageSize, params) {
  return request({
    url: `/drain/usmAssessReportTemplate/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取安全评估报告模板详情
export function getAssessReportTemplateDetail(id) {
  return request({
    url: `/drain/usmAssessReportTemplate/${id}`,
    method: 'get'
  })
}

// 新增安全评估报告模板
export function saveAssessReportTemplate(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmAssessReportTemplate/save',
    method: 'post',
    data: formattedData
  })
}

// 更新安全评估报告模板
export function updateAssessReportTemplate(data) {
  const formattedData = { ...data };
  if (formattedData.createTime) {
    formattedData.createTime = moment(formattedData.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (formattedData.updateTime) {
    formattedData.updateTime = moment(formattedData.updateTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/drain/usmAssessReportTemplate/update',
    method: 'post',
    data: formattedData
  })
}

// 删除安全评估报告模板
export function deleteAssessReportTemplate(id) {
  return request({
    url: `/drain/usmAssessReportTemplate/${id}`,
    method: 'delete'
  })
}
 
// 获取监测分析统计数据
export function getMonitorAnalysisStatistics(dayIndex = 7) {
  return request({
    url: '/drain/api/v1/monitor/analysis/statistics',
    method: 'post',
    data: {
      dayIndex
    }
  })
}

// 获取监测分析统计条件数据
export function getMonitorAnalysisStatisticsCondition(params) {
  return request({
    url: '/drain/api/v1/monitor/analysis/statistics/condition',
    method: 'post',
    data: params
  })
}

// 获取报警类型统计数据
export function getMonitorAnalysisLevelStatistics(dayIndex = 7) {
  return request({
    url: '/drain/api/v1/monitor/analysis/level/statistics',
    method: 'post',
    params: {
      dayIndex
    }
  })
}

// 获取报警趋势分析数据
export function getMonitorAnalysisTrendStatistics(dayIndex = 7) {
  return request({
    url: '/drain/api/v1/monitor/analysis/trend/statistics',
    method: 'post',
    params: {
      dayIndex
    }
  })
}

// 获取高发报警设备数据
export function getMonitorAnalysisDeviceHighFrequency(pageNum = 1, pageSize = 10, dayIndex = 7) {
  return request({
    url: '/drain/api/v1/monitor/analysis/device/high-frequency',
    method: 'post',
    params: {
      pageNum,
      pageSize,
      dayIndex
    }
  })
}