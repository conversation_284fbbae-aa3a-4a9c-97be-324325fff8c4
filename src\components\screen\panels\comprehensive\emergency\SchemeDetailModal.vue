<template>
  <teleport to="body">
    <transition name="fade">
      <div class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">预案详情</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          
          <!-- 预案详情内容区域 -->
          <div class="modal-content">
            <!-- 预案详情表单 -->
            <div class="scheme-detail-form">
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">预案标题:</label>
                  <div class="form-value">{{ schemeDetail.schemeTitle || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">所属专项:</label>
                  <div class="form-value">{{ schemeDetail.relatedBusinessName || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">预案级别:</label>
                  <div class="form-value">{{ schemeDetail.schemeLevelName || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">预案类型:</label>
                  <div class="form-value">{{ schemeDetail.schemeTypeName || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">主题类型:</label>
                  <div class="form-value">{{ schemeDetail.themeTypeName || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">预案状态:</label>
                  <div class="form-value">{{ schemeDetail.schemeStatusName || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">索引编码:</label>
                  <div class="form-value">{{ schemeDetail.indexCode || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">发布单位:</label>
                  <div class="form-value">{{ schemeDetail.issuedUnit || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">发布时间:</label>
                  <div class="form-value">{{ formatTime(schemeDetail.issuedTime) }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">主题词:</label>
                  <div class="form-value">{{ schemeDetail.themeWord || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item full-width">
                  <label class="form-label">适用对象:</label>
                  <div class="form-value">{{ schemeDetail.applyObject || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item full-width">
                  <label class="form-label">备注信息:</label>
                  <div class="form-value remarks">{{ schemeDetail.remarks || '-' }}</div>
                </div>
              </div>
              
              <div class="form-row" v-if="schemeDetail.fileUrls">
                <div class="form-item full-width">
                  <label class="form-label">附件文件:</label>
                  <div class="form-value file-links">
                    <a 
                      v-for="(url, index) in getFileUrls(schemeDetail.fileUrls)" 
                      :key="index"
                      :href="url.trim()" 
                      target="_blank" 
                      class="file-link"
                    >
                      查看文件{{ index + 1 }}
                    </a>
                  </div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">创建时间:</label>
                  <div class="form-value">{{ formatTime(schemeDetail.createTime) }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">更新时间:</label>
                  <div class="form-value">{{ formatTime(schemeDetail.updateTime) }}</div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-item">
                  <label class="form-label">创建人:</label>
                  <div class="form-value">{{ schemeDetail.createBy || '-' }}</div>
                </div>
                <div class="form-item">
                  <label class="form-label">更新人:</label>
                  <div class="form-value">{{ schemeDetail.updateBy || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮区域 -->
          <div class="modal-footer">
            <button @click="closeModal" class="close-btn">关闭</button>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref } from 'vue'
import moment from 'moment'

// 定义组件属性
const props = defineProps({
  schemeData: {
    type: Object,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['close'])

// 响应式数据
const schemeDetail = ref(props.schemeData || {})

/**
 * 关闭弹窗
 */
const closeModal = () => {
  emit('close')
}

/**
 * 格式化时间
 * @param {string} time 时间字符串
 * @returns {string} 格式化后的时间
 */
const formatTime = (time) => {
  if (!time) return '-'
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 处理文件URL字符串，返回URL数组
 * @param {string} fileUrls 文件URL字符串
 * @returns {Array} URL数组
 */
const getFileUrls = (fileUrls) => {
  if (!fileUrls) return []
  // 去除首尾空格，按逗号分割，过滤空字符串
  return fileUrls.trim().split(',').filter(url => url.trim())
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 700px;
  max-height: 80vh;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

/* 内容区域样式 */
.modal-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 表单样式 */
.scheme-detail-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item.full-width {
  flex: 1;
}

.form-label {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.form-value {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: #FFFFFF;
  background-color: rgba(59, 141, 242, 0.1);
  border: 1px solid rgba(59, 141, 242, 0.3);
  border-radius: 4px;
  padding: 10px 12px;
  min-height: 20px;
  line-height: 1.4;
}

.form-value.remarks {
  min-height: 60px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.form-value.file-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-link {
  color: #3AA1FF;
  text-decoration: none;
  padding: 4px 8px;
  background-color: rgba(58, 161, 255, 0.1);
  border-radius: 4px;
  transition: all 0.3s;
  display: inline-block;
  width: fit-content;
}

.file-link:hover {
  background-color: rgba(58, 161, 255, 0.2);
  color: #66B8FF;
}

/* 底部操作区域 */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 15px 20px;
  border-top: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.close-btn {
  padding: 8px 24px;
  background-color: #3AA1FF;
  border: none;
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: #66B8FF;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .modal-container {
    width: 90%;
    max-width: 600px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 15px;
  }
}
</style>