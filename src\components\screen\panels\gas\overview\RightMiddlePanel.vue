<template>
  <PanelBox title="风险监测" class="right-middle-panel">
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备总数</span>
          <span class="stat-value-blue">{{ statsData.totalDevices }}</span>
          <span class="stat-unit">台</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">在线/离线</span>
          <span class="stat-value-highlight">{{ statsData.onlineDevices }}</span>
          <span class="stat-value-warning">/{{ statsData.offlineDevices }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备在线率</span>
          <span class="stat-value-gradient">{{ statsData.onlineRate }}</span>
        </div>
      </div>
      
      <div class="chart-container">
        <div class="chart-header">
          <div class="unit-label">单位（台）</div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-icon access"></span>
              <span class="legend-text">接入总数</span>
            </div>
            <div class="legend-item">
              <span class="legend-icon online"></span>
              <span class="legend-text">在线数</span>
            </div>
            <div class="legend-item">
              <span class="legend-icon rate"></span>
              <span class="legend-text">在线率</span>
            </div>
          </div>
        </div>
        
        <div class="chart-wrapper" ref="chartRef"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, reactive, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getRiskMonitorStatistics } from '@/api/gas'

const chartRef = ref(null)
let chartInstance = null

// 统计数据
const statsData = reactive({
  totalDevices: 0,
  onlineDevices: 0,
  offlineDevices: 0,
  onlineRate: '0%'
})

// 图表数据
const chartData = ref([])

// 从后端获取数据
const fetchData = async () => {
  try {
    const res = await getRiskMonitorStatistics()
    if (res.code === 200 && res.data) {
      // 处理统计数据
      statsData.totalDevices = res.data.totalDevices || 0
      statsData.onlineDevices = res.data.onlineDevices || 0
      statsData.offlineDevices = (res.data.totalDevices || 0) - (res.data.onlineDevices || 0)
      statsData.onlineRate = (typeof res.data.onlineRate === 'number' ? res.data.onlineRate + '%' : (res.data.onlineRate || '0%'))
      // 处理图表数据
      chartData.value = (res.data.deviceTypeStatistics || []).map(item => ({
        category: item.deviceTypeName,
        access: item.count,
        online: item.onlineCount,
        rate: typeof item.onlineRate === 'number' ? item.onlineRate : 0
      }))
      if (chartInstance) {
        updateChart()
      }
    } else {
      // 数据异常时清空
      statsData.totalDevices = 0
      statsData.onlineDevices = 0
      statsData.offlineDevices = 0
      statsData.onlineRate = '0%'
      chartData.value = []
    }
  } catch (error) {
    console.error('获取风险监测数据失败:', error)
    statsData.totalDevices = 0
    statsData.onlineDevices = 0
    statsData.offlineDevices = 0
    statsData.onlineRate = '0%'
    chartData.value = []
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  // 根据屏幕高度调整图表bottom值
  let bottomPadding = '22%';
  let axisLabelMargin = 16;
  let axisLabelFontSize = 12;
  let axisRotation = 0;
  
  // 根据窗口高度动态调整参数
  if (window.innerHeight <= 940 && window.innerHeight >= 900) {
    bottomPadding = '32%'; // 为910px高度增加更多底部空间
    axisLabelMargin = 12; // 减小X轴标签的外边距
    axisLabelFontSize = 10; // 减小X轴标签的字体大小
    axisRotation = 45; // 标签倾斜角度
  }
  
  // 获取最长的类别名称长度
  const maxCategoryLength = Math.max(...chartData.value.map(item => item.category.length));
  
  // 如果有特别长的类别名称，增加底部间距
  if (maxCategoryLength > 8) {
    bottomPadding = window.innerHeight <= 940 ? '1%' : '25%';
  }
  
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '12%',
      left: '3%',
      right: '4%',
      bottom: bottomPadding,
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true, // 确保tooltip不会超出容器边界
      enterable: true, // 鼠标可以进入tooltip
      formatter: function(params) {
        const categoryName = params[0].name;
        let html = `<div style="font-weight:bold;margin-bottom:8px;color:#3CF3FF;">${categoryName}</div>`;
        
        params.forEach(item => {
          let color = '';
          let unit = '';
          
          if (item.seriesName === '接入总数') {
            color = '#0576FF';
            unit = '台';
          } else if (item.seriesName === '在线数') {
            color = '#23CAFF';
            unit = '台';
          } else if (item.seriesName === '在线率') {
            color = '#3FD87C';
            unit = '%';
          }
          
          html += `<div style="display:flex;justify-content:space-between;margin:5px 0;align-items:center;">
            <span style="margin-right:15px;display:inline-block;">
              <span style="display:inline-block;width:8px;height:8px;border-radius:4px;background-color:${color};margin-right:5px;"></span>
              ${item.seriesName}:
            </span>
            <span style="font-weight:bold;color:${color};">${item.value}${unit}</span>
          </div>`;
        });
        
        return html;
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(5, 122, 255, 0.1)'
        }
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.category),
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: axisLabelFontSize,
        margin: axisLabelMargin,
        rotate: 0, // 横向显示
        interval: 1, // 间隔一个显示一个
        hideOverlap: true, // 不自动隐藏重叠标签
        formatter: function(value) {
          // 显示完整文本，不添加省略号
          return value;
        },
        rich: {
          // 自定义富文本样式
          title: {
            color: '#FFFFFF',
            lineHeight: 16,
            align: 'center'
          }
        },
        // 添加文字阴影，增强可读性
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowBlur: 2,
        textShadowOffsetX: 1,
        textShadowOffsetY: 1
      }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '接入总数',
        type: 'bar',
        data: chartData.value.map(item => item.access),
        barWidth: 12,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#0576FF' },
              { offset: 0.5, color: '#0567E8' },
              { offset: 1, color: '#055ADB' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#0A93FF',
          borderWidth: 1,
          shadowColor: 'rgba(5, 122, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(5, 90, 219, 0.7)'
          },
          focus: 'series' // 高亮当前系列
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(5, 90, 219, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '在线数',
        type: 'bar',
        data: chartData.value.map(item => item.online),
        barWidth: 12,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#23CAFF' },
              { offset: 0.5, color: '#1FB4E9' },
              { offset: 1, color: '#1A9AD6' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#34D6FF',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          },
          focus: 'series' // 高亮当前系列
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '在线率',
        type: 'line',
        data: chartData.value.map(item => item.rate),
        symbolSize: 6,
        symbol: 'circle',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#3FD87C'
        },
        itemStyle: {
          color: '#3FD87C'
        },
        emphasis: {
          scale: true, // 鼠标移入放大点
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(63, 216, 124, 0.5)'
          },
          focus: 'series' // 高亮当前系列
        }
      }
    ]
  };
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  // 添加点击事件
  chartInstance.on('click', function(params) {
    console.log('点击了图表项:', params);
    // 这里可以根据需要处理点击事件，例如显示更详细的数据或跳转到其他页面
  });
  
  // 添加resize监听，并传递完整的配置更新
  window.addEventListener('resize', () => {
    if (chartInstance) {
      // 确保图表尺寸正确
      chartInstance.resize();
      // 重新应用配置以更新grid底部间距
      const newOption = createChartOption();
      chartInstance.setOption(newOption);
      // 特别为910px高度刷新图表
      checkWindowSize();
    }
  });
}

// 定时刷新数据
const setupDataRefresh = () => {
  // 定期刷新数据，例如每30秒
  const refreshInterval = 30000;
  setInterval(() => {
    fetchData();
  }, refreshInterval);
}

// 检查窗口大小并在需要时调整图表
const checkWindowSize = () => {
  if (chartInstance) {
    // 当窗口高度在900-940px之间时，更新图表
    if (window.innerHeight <= 940 && window.innerHeight >= 900) {
      const newOption = createChartOption()
      chartInstance.setOption(newOption)
      chartInstance.resize()
    }
  }
}

onMounted(async () => {
  await nextTick()
  // 获取初始数据
  await fetchData()
  // 初始化图表
  initChart()
  // 首次加载检查窗口大小
  checkWindowSize()
  // 设置数据刷新
  // setupDataRefresh()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-warning {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FFD11C;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  padding: 0 10px;
}

.unit-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 8px;
  height: 8px;
  display: block;
}

.legend-icon.access {
  background-color: #0576FF;
}

.legend-icon.online {
  background-color: #23CAFF;
}

.legend-icon.rate {
  background-color: #3FD87C;
}

.legend-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.chart-wrapper {
  flex: 1;
  min-height: 180px;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px){
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stats-row {
    margin-bottom: 5px;
  }
  
  .stat-item {
    gap: 3px;
  }
  
  .stat-dot {
    width: 7px;
    height: 7px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 12px;
  }
  
  .stat-value-blue,
  .stat-value-highlight,
  .stat-value-warning,
  .stat-value-gradient {
    font-size: 18px;
  }
  
  .stat-unit {
    font-size: 10px;
  }
  
  .chart-header {
    height: 24px;
    padding: 0 8px;
  }
  
  .unit-label {
    font-size: 10px;
  }
  
  .chart-legend {
    gap: 10px;
  }
  
  .legend-icon {
    width: 6px;
    height: 6px;
  }
  
  .legend-text {
    font-size: 10px;
  }
  
  .chart-wrapper {
    min-height: 160px;
  }
}

/* 910px左右高度的屏幕特别优化 */
@media (min-height: 900px) and (max-height: 939px){
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .stats-row {
    margin-bottom: 4px;
  }
  
  .stat-item {
    gap: 2px;
  }
  
  .stat-dot {
    width: 6px;
    height: 6px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-value-blue,
  .stat-value-highlight,
  .stat-value-warning,
  .stat-value-gradient {
    font-size: 16px;
    line-height: 20px;
  }
  
  .stat-unit {
    font-size: 9px;
  }
  
  .chart-header {
    height: 20px;
    padding: 0 6px;
  }
  
  .unit-label {
    font-size: 9px;
  }
  
  .chart-legend {
    gap: 8px;
  }
  
  .legend-icon {
    width: 5px;
    height: 5px;
  }
  
  .legend-text {
    font-size: 9px;
  }
  
  .chart-wrapper {
    min-height: 140px;
  }
}

</style>