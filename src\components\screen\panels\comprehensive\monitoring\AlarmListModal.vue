<template>
  <teleport to="body">
    <transition name="fade">
      <div v-if="dialogVisible" class="modal-overlay" @click.self="handleClose">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">报警信息列表</div>
            <div class="close-icon" @click="handleClose">×</div>
          </div>
          <div class="modal-content">
            <!-- 搜索条件 -->
            <div class="search-container">
              <el-form :model="queryParams" layout="inline">
                <el-form-item label="所属专项">
                  <CommonSelect v-model="queryParams.relatedBusiness" :options="typeOptions" placeholder="全部" popper-class="screen-dialog-popper" />
                </el-form-item>
                <el-form-item label="报警时间">
                  <el-date-picker
                    v-model="queryParams.timeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :clearable="true"
                    popper-class="screen-dialog-popper"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                  <el-button @click="handleReset">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 表格区域 -->
            <div class="table-container" v-loading="loading" element-loading-background="rgba(0, 22, 72, 0.5)">
              <el-table :data="listData" style="width: 100%" height="100%">
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="alarmCode" label="报警编码" width="160" />
                <el-table-column prop="deviceName" label="报警设备名称" width="250" />
                <el-table-column prop="monitorIndexName" label="监测指标" width="120" />
                <el-table-column prop="alarmLevelName" label="报警等级" width="120" />
                <el-table-column label="定位" width="60" >
                  <template #default="scope">
                    <div class="location-icon" @click="handleLocationClick(scope.row)"></div>
                  </template>
                </el-table-column>
              </el-table>
              <Nodata v-if="!loading && listData.length === 0" />
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-if="total > 0"
                v-model:current-page="queryParams.pageNum"
                v-model:page-size="queryParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import moment from 'moment'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import Nodata from '@/components/common/Nodata.vue'
import { getAlarmStatisticsScreen } from '@/api/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  initialType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible'])

const dialogVisible = ref(props.visible)
const loading = ref(false)
const listData = ref([])
const total = ref(0)

const typeOptions = [
  { label: '全部', value: '' },
  { label: '燃气', value: '7000501' },
  { label: '排水', value: '7000502' },
  { label: '供热', value: '7000503' },
  { label: '桥梁', value: '7000504' }
]

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  relatedBusiness: props.initialType,
  startTime: '',
  endTime: '',
  timeRange: []
})

const fetchData = async () => {
  loading.value = true
  try {
    const params = { ...queryParams }
    if (params.timeRange && params.timeRange.length === 2) {
      params.startTime = moment(params.timeRange[0]).format('YYYY-MM-DD HH:mm:ss')
      params.endTime = moment(params.timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
    }
    delete params.timeRange

    const response = await getAlarmStatisticsScreen(params)
    if (response && response.data && response.data.alarmPage) {
      listData.value = response.data.alarmPage.records || []
      total.value = response.data.alarmPage.total || 0
    } else {
      listData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取报警列表失败:', error)
    listData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.pageNum = 1
  fetchData()
}

const handleReset = () => {
  queryParams.pageNum = 1
  queryParams.relatedBusiness = ''
  queryParams.timeRange = []
  queryParams.startTime = ''
  queryParams.endTime = ''
  fetchData()
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  fetchData()
}

const handleLocationClick = (row) => {
  console.log('定位:', row)
  // TODO: 实现地图定位逻辑
}

const handleClose = () => {
  emit('update:visible', false)
}

watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    queryParams.relatedBusiness = props.initialType
    fetchData()
  }
})
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 900px;
  height: 650px;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

.modal-content {
  padding: 15px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden;
}

.search-container {
  background: rgba(3, 24, 55, 0.5);
  border-radius: 4px;
  padding: 15px;
}

.table-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.location-icon {
  width: 20px;
  height: 20px;
  background-image: url('@/assets/images/screen/common/location.svg');
  background-size: contain;
  background-repeat: no-repeat;
  cursor: pointer;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.el-form) {
  .el-form-item__label {
    color: #D3E5FF;
  }

  .el-button--primary {
    background: #1890FF;
    border-color: #1890FF;
    color: #FFFFFF;
  }
  .el-button {
    background: rgba(24, 144, 255, 0.1);
    border: 1px solid #1890FF;
    color: #1890FF;
  }
}

:deep(.el-date-editor.el-range-editor) {
  background: rgba(0, 19, 47, 0.35);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: none;

  .el-range-input {
    color: #fff;
    background: transparent;
     &::placeholder {
        color: rgba(255, 255, 255, 0.3);
    }
  }

  .el-range-separator {
    color: #fff;
  }
}


:deep(.el-table) {
  background: transparent;
  color: #fff;
  --el-table-border-color: rgba(59, 141, 242, 0.2);
  --el-table-header-bg-color: rgba(0, 35, 91, 0.8);
  --el-table-tr-bg-color: transparent;
  --el-table-row-hover-bg-color: rgba(0, 163, 255, 0.2);
}

:deep(.el-table th.el-table__cell) {
    background-color: rgba(0, 35, 91, 0.8);
}

:deep(.el-pagination) {
  --el-pagination-text-color: rgba(255, 255, 255, 0.9);
  --el-pagination-button-disabled-bg-color: transparent;
  --el-pagination-button-bg-color: transparent;
  --el-pagination-bg-color: transparent;
  --el-pagination-border-radius: 2px;
  --el-pagination-input-height: 28px;

  .el-pagination__jump .el-input__wrapper {
    background-color: rgba(0, 19, 47, 0.35) !important;
    box-shadow: none !important;
    border: 1px solid rgba(59, 141, 242, 0.5);
  }
   .el-pagination__jump .el-input__inner {
    color: #fff;
  }
}
</style>

<style lang="scss">
.screen-dialog-popper {
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.95) 0%, rgba(0, 35, 91, 0.95) 100%) !important;
  border: 1px solid rgba(59, 141, 242, 0.5) !important;
  z-index: 10000 !important;
  
  .el-select-dropdown__item {
    background-color: transparent;
    color: #fff;
  }
  .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
    background-color: rgba(0, 163, 255, 0.2);
  }
  .el-select-dropdown__item.selected {
    color: #409eff;
    font-weight: bold;
    background-color: rgba(0, 163, 255, 0.2);
  }

  .el-picker-panel {
    background: transparent !important;
    color: #fff;
    border: none !important;

    .el-picker-panel__header, .el-picker-panel__body {
      color: #fff;
    }

    .el-picker-panel__footer {
      background: transparent !important;
      border-top: 1px solid rgba(59, 141, 242, 0.3);
    }
    
    .el-date-table th {
      color: #fff;
      border-bottom: 1px solid rgba(59, 141, 242, 0.3);
    }

    .el-date-table td.available:hover {
      color: #409eff !important;
    }
    
    .el-date-table td.in-range .el-date-table-cell {
        background-color: rgba(0, 163, 255, 0.2);
    }

    .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
      background: #1890FF;
      color: #fff;
    }

    .el-picker-panel__icon-btn {
      color: #fff;
    }
    .el-picker-panel__icon-btn:hover {
      color: #409eff;
    }
  }
  
  .el-popper__arrow::before {
    background: rgba(0, 35, 91, 0.95) !important;
    border-color: rgba(59, 141, 242, 0.5) !important;
  }
}
</style>