<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-network-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线编码" prop="pipelineCode">
            <el-input v-model="formData.pipelineCode" placeholder="请输入管线编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管线类型" prop="pipelineType">
            <el-select v-model="formData.pipelineType" placeholder="请选择" class="w-full">
              <el-option v-for="item in pipelineTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="埋设类型" prop="buriedType">
            <el-select v-model="formData.buriedType" placeholder="请选择" class="w-full">
              <el-option v-for="item in buriedTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="材质" prop="material">
            <el-select v-model="formData.material" placeholder="请选择" class="w-full">
              <el-option v-for="item in materialOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="压力类型" prop="pressureType">
            <el-input v-model="formData.pressureType" placeholder="请输入压力类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="压力 (Mpa)" prop="designPressure">
            <el-input-number v-model="formData.designPressure" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管径 (mm)" prop="pipeDiameter">
            <el-input v-model="formData.pipeDiameter" placeholder="请输入管径" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="长度 (m)" prop="pipeLength">
            <el-input-number v-model="formData.pipeLength" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="流向" prop="flowDirection">
            <el-input v-model="formData.flowDirection" placeholder="请输入流向" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起点埋深 (m)" prop="startPointDepth">
            <el-input-number v-model="formData.startPointDepth" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点埋深 (m)" prop="endPointDepth">
            <el-input-number v-model="formData.endPointDepth" :min="0" :precision="2" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起点经纬度">
            <div class="flex items-center">
              <el-input v-model="formData.startPointLongitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.startPointLatitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker('start')"></el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点经纬度">
            <div class="flex items-center">
              <el-input v-model="formData.endPointLongitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.endPointLatitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker('end')"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设时间" prop="constructionTime">
            <el-date-picker
              v-model="formData.constructionTime"
              type="date"
              placeholder="请选择建设时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnit">
            <el-select v-model="formData.managementUnit" placeholder="请选择" class="w-full">
              <el-option v-for="unit in managementUnits" :key="unit.id" :label="unit.enterpriseName" :value="unit.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus">
            <el-select v-model="formData.usageStatus" placeholder="请选择" class="w-full">
              <el-option v-for="item in useTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="断面类型" prop="sectionType">
            <el-select v-model="formData.sectionType" placeholder="请选择" class="w-full">
              <el-option v-for="item in sectionTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getEnterpriseList, savePipeline, updatePipeline } from '@/api/drainage';
import { PIPELINE_TYPE_OPTIONS, BURIED_TYPE_OPTIONS, MATERIAL_OPTIONS, USE_TYPE,SECTION_TYPE } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 过滤掉选项中的"全部"选项
const pipelineTypeOptions = PIPELINE_TYPE_OPTIONS.filter(item => item.value !== '');
const buriedTypeOptions = BURIED_TYPE_OPTIONS.filter(item => item.value !== '');
const materialOptions = MATERIAL_OPTIONS.filter(item => item.value !== '');
const useTypeOptions = USE_TYPE;

// 断面类型选项
const sectionTypeOptions =SECTION_TYPE

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增管线',
    edit: '编辑管线',
    view: '管线详情'
  };
  return titles[props.mode] || '管线信息';
});

// 权属单位列表
const managementUnits = ref([]);
// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  pipelineCode: '',
  pipelineType: '',
  pipelineTypeName: '',
  buriedType: '',
  buriedTypeName: '',
  material: '',
  materialName: '',
  pressureType: '',
  pressureTypeName: '',
  designPressure: 0,
  pipeDiameter: '',
  pipeLength: 0,
  roadName: '',
  flowDirection: '',
  startPointDepth: 0,
  endPointDepth: 0,
  startPointLongitude: '',
  startPointLatitude: '',
  endPointLongitude: '',
  endPointLatitude: '',
  startPointDistance: 0,
  endPointDistance: 0,
  constructionTime: null,
  managementUnit: '',
  managementUnitName: '',
  usageStatus: '',
  usageStatusName: '',
  sectionType: '',
  sectionTypeName: '',
  city: '',
  county: '371728',
  countyName: '东明县',
  town: '',
  townName: '',
  address: '',
  geomText: ''
});

// 表单验证规则
const formRules = {
  pipelineCode: [{ required: true, message: '请输入管线编码', trigger: 'blur' }],
  pipelineType: [{ required: true, message: '请选择管线类型', trigger: 'change' }],
  buriedType: [{ required: true, message: '请选择埋设类型', trigger: 'change' }],
  material: [{ required: true, message: '请选择材质', trigger: 'change' }],
  pipeDiameter: [{ required: true, message: '请输入管径', trigger: 'blur' }],
  pipeLength: [{ required: true, message: '请输入长度', trigger: 'blur' }],
  roadName: [{ required: true, message: '请输入所在道路', trigger: 'blur' }],
  startPointDepth: [{ required: true, message: '请输入起点埋深', trigger: 'blur' }],
  endPointDepth: [{ required: true, message: '请输入终点埋深', trigger: 'blur' }],
  constructionTime: [{ required: true, message: '请选择建设时间', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  usageStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else if (key === 'constructionTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
  
  // 保留默认值
  formData.county = '371728';
  formData.countyName = '东明县';
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 管线类型
  const selectedPipelineType = pipelineTypeOptions.find(item => item.value === formData.pipelineType);
  if (selectedPipelineType) {
    formData.pipelineTypeName = selectedPipelineType.label;
  }
  
  // 埋设类型
  const selectedBuriedType = buriedTypeOptions.find(item => item.value === formData.buriedType);
  if (selectedBuriedType) {
    formData.buriedTypeName = selectedBuriedType.label;
  }
  
  // 材质
  const selectedMaterial = materialOptions.find(item => item.value === formData.material);
  if (selectedMaterial) {
    formData.materialName = selectedMaterial.label;
  }
  
  // 使用状态
  const selectedUsageStatus = useTypeOptions.find(item => item.value === formData.usageStatus);
  if (selectedUsageStatus) {
    formData.usageStatusName = selectedUsageStatus.label;
  }
  
  // 断面类型
  const selectedSectionType = sectionTypeOptions.find(item => item.value === formData.sectionType);
  if (selectedSectionType) {
    formData.sectionTypeName = selectedSectionType.label;
  }

  // 权属单位 - 现在使用名称作为值，直接赋值
  formData.managementUnitName = formData.managementUnit;
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理日期格式
    if (newVal.constructionTime && typeof newVal.constructionTime === 'object') {
      // 如果后端返回的是包含time字段的对象，转换为Date对象
      if (newVal.constructionTime.time) {
        formData.constructionTime = new Date(newVal.constructionTime.time);
      }
    }
    
    // 确保各字段的选项值和名称一致，处理回显问题
    if (newVal.pipelineType) {
      const selectedPipelineType = pipelineTypeOptions.find(item => item.value === newVal.pipelineType);
      if (selectedPipelineType) {
        formData.pipelineType = selectedPipelineType.value;
        formData.pipelineTypeName = selectedPipelineType.label;
      }
    }
    
    if (newVal.buriedType) {
      const selectedBuriedType = buriedTypeOptions.find(item => item.value === newVal.buriedType);
      if (selectedBuriedType) {
        formData.buriedType = selectedBuriedType.value;
        formData.buriedTypeName = selectedBuriedType.label;
      }
    }
    
    if (newVal.material) {
      const selectedMaterial = materialOptions.find(item => item.value === newVal.material);
      if (selectedMaterial) {
        formData.material = selectedMaterial.value;
        formData.materialName = selectedMaterial.label;
      }
    }
    
    if (newVal.usageStatus) {
      const selectedUsageStatus = useTypeOptions.find(item => item.value === newVal.usageStatus);
      if (selectedUsageStatus) {
        formData.usageStatus = selectedUsageStatus.value;
        formData.usageStatusName = selectedUsageStatus.label;
      }
    }
    
    if (newVal.sectionType) {
      const selectedSectionType = sectionTypeOptions.find(item => item.value === newVal.sectionType);
      if (selectedSectionType) {
        formData.sectionType = selectedSectionType.value;
        formData.sectionTypeName = selectedSectionType.label;
      }
    }
    
    if (newVal.managementUnit) {
      formData.managementUnit = newVal.managementUnit;
      formData.managementUnitName = newVal.managementUnit;
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.pipelineType, (val) => {
  if (val) {
    const selected = pipelineTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.pipelineTypeName = selected.label;
    }
  }
});

watch(() => formData.buriedType, (val) => {
  if (val) {
    const selected = buriedTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.buriedTypeName = selected.label;
    }
  }
});

watch(() => formData.material, (val) => {
  if (val) {
    const selected = materialOptions.find(item => item.value === val);
    if (selected) {
      formData.materialName = selected.label;
    }
  }
});

watch(() => formData.usageStatus, (val) => {
  if (val) {
    const selected = useTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.usageStatusName = selected.label;
    }
  }
});

watch(() => formData.sectionType, (val) => {
  if (val) {
    const selected = sectionTypeOptions.find(item => item.value === val);
    if (selected) {
      formData.sectionTypeName = selected.label;
    }
  }
});

watch(() => formData.managementUnit, (val) => {
  if (val) {
    formData.managementUnitName = val;
  }
});

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.data) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 当前选点类型
const currentPointType = ref('');

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    if (currentPointType.value === 'start') {
      formData.startPointLongitude = params.longitude;
      formData.startPointLatitude = params.latitude;
    } else if (currentPointType.value === 'end') {
      formData.endPointLongitude = params.longitude;
      formData.endPointLatitude = params.latitude;
    }
  });
};

// 打开地图选点
const openMapPicker = (type) => {
  currentPointType.value = type;
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 设置枚举值对应的名称
    submitData.pipelineTypeName = pipelineTypeOptions.find(item => item.value === formData.pipelineType)?.label || '';
    submitData.buriedTypeName = buriedTypeOptions.find(item => item.value === formData.buriedType)?.label || '';
    submitData.materialName = materialOptions.find(item => item.value === formData.material)?.label || '';
    submitData.usageStatusName = useTypeOptions.find(item => item.value === formData.usageStatus)?.label || '';
    submitData.sectionTypeName = sectionTypeOptions.find(item => item.value === formData.sectionType)?.label || '';
    
    // 权属单位使用名称作为值，直接赋值
    submitData.managementUnitName = formData.managementUnit;
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await savePipeline(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePipeline(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
});
</script>

<style scoped>
.drainage-network-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

/* 确保下拉框显示正确 */
/* :deep(.el-select .el-select__wrapper) {
  box-shadow: none !important;
}

:deep(.el-select .el-input__inner) {
  color: #606266;
} */

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 