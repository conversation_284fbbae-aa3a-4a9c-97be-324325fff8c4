<template>
  <el-dialog
    v-model="dialogVisible"
    title="自动排班"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="auto-schedule-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="值班日期" prop="shiftDates">
        <el-date-picker
          v-model="formData.shiftDates"
          type="daterange"
          value-format="YYYY-MM-DD"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="休息日期">
        <el-date-picker
          v-model="formData.restDates"
          type="dates"
          value-format="YYYY-MM-DD"
          placeholder="选择休息日期"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="排班人员" prop="userIds">
        <el-select
          v-model="formData.userIds"
          multiple
          placeholder="请选择排班人员"
          class="w-full"
        >
          <el-option
            v-for="item in userOptions"
            :key="item.id"
            :label="item.userName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  autoSchedule,
  getDutyUserPage
} from '@/api/comprehensive';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);
const loading = ref(false);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 下拉选项数据
const userOptions = ref([]);

// 表单数据
const formData = reactive({
  shiftDates: [],
  restDates: [],
  userIds: []
});

// 表单验证规则
const formRules = {
  shiftDates: [{ required: true, message: '请选择值班日期', trigger: 'change' }],
  userIds: [{ required: true, message: '请选择排班人员', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  formData.shiftDates = [];
  formData.restDates = [];
  formData.userIds = [];
};

// 获取值班人员列表
const fetchUserList = async () => {
  try {
    const res = await getDutyUserPage(1, 999, {});
    if (res && res.data && res.data.records) {
      userOptions.value = res.data.records;
    }
  } catch (error) {
    console.error('获取值班人员列表失败', error);
    userOptions.value = [];
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // 处理日期范围
    const submitData = {
      restDates: formData.restDates || [],
      userIds: formData.userIds
    };

    // 将日期范围转换为日期数组
    if (formData.shiftDates && formData.shiftDates.length === 2) {
      const startDate = new Date(formData.shiftDates[0]);
      const endDate = new Date(formData.shiftDates[1]);
      const shiftDates = [];
      
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        // 排除休息日
        if (!submitData.restDates.includes(dateStr)) {
          shiftDates.push(dateStr);
        }
      }
      
      submitData.shiftDates = shiftDates;
    }

    const res = await autoSchedule(submitData);

    if (res && res.code === 200) {
      ElMessage.success('自动排班成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || '自动排班失败');
    }
  } catch (error) {
    console.error('表单验证失败', error);
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchUserList();
});
</script>

<style scoped>
.auto-schedule-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 