<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" :close-on-click-modal="false"
    :before-close="handleClose" class="threshold-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="mode === 'view'">
      <!-- 规则信息 -->
      <div class="section-title">规则信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则名称:" prop="ruleName">
            <el-input v-model="formData.ruleName" placeholder="请输入规则名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规则描述:" prop="ruleDesc">
            <el-input v-model="formData.ruleDesc" placeholder="请输入规则描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否生效:" prop="isEnabled">
            <el-select v-model="formData.isEnabled" placeholder="是" class="w-full">
              <el-option 
                v-for="item in enabledStatusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 设置内容 -->
      <div class="section-title">设置内容</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型:" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择" class="w-full">
              <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监测指标:" prop="monitorIndex">
            <el-select v-model="formData.monitorIndex" placeholder="请选择" class="w-full" :disabled="!formData.deviceType" @change="handleMonitorIndexChange">
              <el-option v-for="item in monitorIndexOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
      <el-col :span="24">
          <el-form-item label="设备选择:" prop="deviceIds">
            <div class="device-select-wrapper">
              <el-button 
                @click="openDeviceSelectDialog" 
                :disabled="mode !== 'view' && (!formData.deviceType || !formData.monitorIndex)"
                class="device-select-btn"
              >
                <span v-if="selectedDeviceNames.length === 0">请选择设备</span>
                <span v-else>已选择 {{ formData.deviceIds.length }} 个设备</span>
              </el-button>
              <div v-if="selectedDeviceNames.length > 0" class="selected-devices-preview">
                <el-tag 
                  v-for="(name, index) in selectedDeviceNames.slice(0, 3)" 
                  :key="index" 
                  size="small" 
                  class="device-tag"
                >
                  {{ name }}
                </el-tag>
                <el-tag v-if="selectedDeviceNames.length > 3" size="small" type="info">
                  +{{ selectedDeviceNames.length - 3 }}个
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 一级报警 -->
      <div class="alert-level">一级报警:</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阈值下限:" prop="thresholdLevel1Min">
            <el-input v-model.number="formData.thresholdLevel1Min" type="number" placeholder="请输入阈值下限">
              <template #append v-if="currentMeasureUnit">{{ currentMeasureUnit }}</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阈值上限:" prop="thresholdLevel1Max">
            <el-input v-model.number="formData.thresholdLevel1Max" type="number" placeholder="请输入阈值上限">
              <template #append v-if="currentMeasureUnit">{{ currentMeasureUnit }}</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知人:" class="notification-item">
            <div class="checkbox-group">
              <el-checkbox v-model="formData.notifyRightsDept1" label="权属单位" />
              <el-checkbox v-model="formData.notifySuperviseDept1" label="监管部门" />
              <el-select v-if="formData.notifySuperviseDept1" v-model="formData.notifySuperviseDeptIds1" multiple class="select-unit" placeholder="请选择监管部门">
                <el-option v-for="dept in superviseDepartments" :key="dept.value" :label="dept.label" :value="dept.value" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 二级报警 -->
      <div class="alert-level">二级报警:</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阈值下限:" prop="thresholdLevel2Min">
            <el-input v-model.number="formData.thresholdLevel2Min" type="number" placeholder="请输入阈值下限">
              <template #append v-if="currentMeasureUnit">{{ currentMeasureUnit }}</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阈值上限:" prop="thresholdLevel2Max">
            <el-input v-model.number="formData.thresholdLevel2Max" type="number" placeholder="请输入阈值上限">
              <template #append v-if="currentMeasureUnit">{{ currentMeasureUnit }}</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知人:" class="notification-item">
            <div class="checkbox-group">
              <el-checkbox v-model="formData.notifyRightsDept2" label="权属单位" />
              <el-checkbox v-model="formData.notifySuperviseDept2" label="监管部门" />
              <el-select v-if="formData.notifySuperviseDept2" v-model="formData.notifySuperviseDeptIds2" multiple class="select-unit" placeholder="请选择监管部门">
                <el-option v-for="dept in superviseDepartments" :key="dept.value" :label="dept.label" :value="dept.value" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 三级报警 -->
      <div class="alert-level">三级报警:</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阈值下限:" prop="thresholdLevel3Min">
            <el-input v-model.number="formData.thresholdLevel3Min" type="number" placeholder="请输入阈值下限">
              <template #append v-if="currentMeasureUnit">{{ currentMeasureUnit }}</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阈值上限:" prop="thresholdLevel3Max">
            <el-input v-model.number="formData.thresholdLevel3Max" type="number" placeholder="请输入阈值上限">
              <template #append v-if="currentMeasureUnit">{{ currentMeasureUnit }}</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知人:" class="notification-item">
            <div class="checkbox-group">
              <el-checkbox v-model="formData.notifyRightsDept3" label="权属单位" />
              <el-checkbox v-model="formData.notifySuperviseDept3" label="监管部门" />
              <el-select v-if="formData.notifySuperviseDept3" v-model="formData.notifySuperviseDeptIds3" multiple class="select-unit" placeholder="请选择监管部门">
                <el-option v-for="dept in superviseDepartments" :key="dept.value" :label="dept.label" :value="dept.value" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 通知方式 -->
      <div class="section-title">通知方式:</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通知方式:">
            <el-checkbox v-model="formData.notifySystem" label="系统通知" />
            <el-checkbox v-model="formData.notifySms" label="短信通知" />
            <el-checkbox v-model="formData.notifyEmail" label="邮件通知" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 设备选择弹窗 -->
  <DeviceSelectDialog
    v-model:visible="deviceSelectDialogVisible"
    :device-list="deviceOptions"
    :selected-device-ids="formData.deviceIds"
    :readonly="mode === 'view'"
    @confirm="handleDeviceSelectConfirm"
  />
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  getEnterpriseList, 
  saveAlarmThreshold, 
  updateAlarmThreshold, 
  getAlarmThresholdDetail, 
  getMonitorDeviceList, 
  getMonitorIndicatorsList 
} from '@/api/drainage';
import { ENABLED_STATUS_OPTIONS, SUPERVISE_DEPARTMENTS } from '@/constants/drainage';
import DeviceSelectDialog from './DeviceSelectDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增',
    edit: '编辑',
    view: '详情'
  };
  return titles[props.mode] || '阈值配置';
});

// 表单数据
const formData = reactive({
  id: '',
  ruleName: '',
  ruleDesc: '',
  isEnabled: true,
  deviceType: '',
  monitorIndex: '',
  monitorIndexName: '',
  deviceIds: [],
  thresholdLevel1Min: '',
  thresholdLevel1Max: '',
  thresholdLevel2Min: '',
  thresholdLevel2Max: '',
  thresholdLevel3Min: '',
  thresholdLevel3Max: '',
  notifyRightsDept1: false,
  notifySuperviseDept1: false,
  notifySuperviseDeptIds1: [],
  notifyRightsDept2: false,
  notifySuperviseDept2: false,
  notifySuperviseDeptIds2: [],
  notifyRightsDept3: false,
  notifySuperviseDept3: false,
  notifySuperviseDeptIds3: [],
  notifySystem: true,
  notifySms: false,
  notifyEmail: false
});

// 表单验证规则
const formRules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  monitorIndex: [{ required: true, message: '请选择监测指标', trigger: 'change' }],
  thresholdLevel1Min: [{ required: true, message: '请输入一级阈值下限', trigger: 'blur' }],
  thresholdLevel1Max: [{ required: true, message: '请输入一级阈值上限', trigger: 'blur' }],
  thresholdLevel2Min: [{ required: true, message: '请输入二级阈值下限', trigger: 'blur' }],
  thresholdLevel2Max: [{ required: true, message: '请输入二级阈值上限', trigger: 'blur' }],
  thresholdLevel3Min: [{ required: true, message: '请输入三级阈值下限', trigger: 'blur' }],
  thresholdLevel3Max: [{ required: true, message: '请输入三级阈值上限', trigger: 'blur' }]
};

// 生效状态选项
const enabledStatusOptions = ENABLED_STATUS_OPTIONS;

// 设备类型选项
const deviceTypeOptions = ref([]);

// 监测指标选项
const monitorIndexOptions = ref([]);

// 监管部门选项
const superviseDepartments = SUPERVISE_DEPARTMENTS;

// 权属单位选项
const managementUnits = ref([]);

// 设备选项
const deviceOptions = ref([]);

// 设备选择弹窗相关
const deviceSelectDialogVisible = ref(false);

// 当前监测指标的单位
const currentMeasureUnit = ref('');

// 已选择设备名称列表（用于显示）
const selectedDeviceNames = computed(() => {
  if (!formData.deviceIds || formData.deviceIds.length === 0) {
    return [];
  }
  return formData.deviceIds.map(id => {
    const device = deviceOptions.value.find(item => item.id === id);
    return device ? device.deviceName : '';
  }).filter(name => name);
});

// 获取权属单位列表
const fetchManagementUnits = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.code === 200) {
      managementUnits.value = res.data || [];
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error);
  }
};

// 获取设备类型列表
const fetchDeviceTypes = async () => {
  try {
    const res = await getMonitorIndicatorsList({
      deviceType: ""
    });
    if (res && res.code === 200) {
      const data = res.data || [];
      // 去重处理，确保设备类型唯一
      const uniqueDeviceTypes = [];
      const deviceTypeSet = new Set();
      data.forEach(item => {
        if (item.deviceType && item.deviceTypeName && !deviceTypeSet.has(item.deviceType)) {
          deviceTypeSet.add(item.deviceType);
          uniqueDeviceTypes.push({
            label: item.deviceTypeName,
            value: item.deviceType
          });
        }
      });
      deviceTypeOptions.value = uniqueDeviceTypes;
    }
  } catch (error) {
    console.error('获取设备类型列表失败', error);
  }
};

// 获取监测指标列表
const fetchMonitorIndexes = async (deviceType) => {
  if (!deviceType) {
    monitorIndexOptions.value = [];
    return;
  }
  
  try {
    console.log('正在获取监测指标，设备类型:', deviceType);
    const res = await getMonitorIndicatorsList({
      deviceType: deviceType
    });
    if (res && res.code === 200) {
      const filteredData = (res.data || []).filter(item => item.deviceType === deviceType);
      monitorIndexOptions.value = filteredData.map(item => ({
        label: item.monitorIndexName,
        value: item.monitorIndex,
        measureUnit: item.measureUnit || ''
      }));
      console.log('监测指标加载完成:', monitorIndexOptions.value);
    }
  } catch (error) {
    console.error('获取监测指标列表失败', error);
  }
};

// 获取设备列表
const fetchDeviceList = async (deviceType, monitorIndex) => {
  const targetDeviceType = deviceType || formData.deviceType;
  const targetMonitorIndex = monitorIndex || formData.monitorIndex;
  
  if (!targetDeviceType || !targetMonitorIndex) {
    deviceOptions.value = [];
    return;
  }
  
  try {
    console.log('正在获取设备列表，设备类型:', targetDeviceType, '监测指标:', targetMonitorIndex);
    const res = await getMonitorDeviceList({
      deviceType: targetDeviceType,
      monitorIndex: targetMonitorIndex,
      thresholdId: props.data.id
    });
    if (res && res.code === 200) {
      deviceOptions.value = res.data || [];
      console.log('设备列表加载完成:', deviceOptions.value);
    }
  } catch (error) {
    console.error('获取设备列表失败', error);
  }
};

// 获取阈值详情
const fetchThresholdDetail = async (id) => {
  try {
    const res = await getAlarmThresholdDetail(id);
    if (res && res.code === 200) {
      const data = res.data;
      
      // 先加载设备类型选项（如果还没有加载）
      if (deviceTypeOptions.value.length === 0) {
        await fetchDeviceTypes();
      }
      
      // 如果有设备类型，先加载对应的监测指标
      if (data.deviceType) {
        await fetchMonitorIndexes(data.deviceType);
        
        // 如果同时有监测指标，则加载设备列表
        if (data.monitorIndex) {
          await fetchDeviceList(data.deviceType, data.monitorIndex);
        }
      }
      
      // 等待下一个事件循环，确保选项已经更新
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 复制数据到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined) {
          formData[key] = data[key];
        }
      });
      
      // 处理字符串形式的数组数据
      if (typeof data.deviceIds === 'string' && data.deviceIds) {
        formData.deviceIds = data.deviceIds.split(',').map(id => id.trim()).filter(id => id);
      } else if (Array.isArray(data.deviceIds)) {
        formData.deviceIds = data.deviceIds;
      }
      
      // 处理监管部门ID字段
      ['notifySuperviseDeptIds1', 'notifySuperviseDeptIds2', 'notifySuperviseDeptIds3'].forEach(field => {
        if (typeof data[field] === 'string' && data[field]) {
          formData[field] = data[field].split(',').map(id => id.trim()).filter(id => id);
        } else if (Array.isArray(data[field])) {
          formData[field] = data[field];
        }
      });
      
      // 设置监测指标名称和单位
      if (formData.monitorIndex && monitorIndexOptions.value.length > 0) {
        const selectedOption = monitorIndexOptions.value.find(item => item.value === formData.monitorIndex);
        formData.monitorIndexName = selectedOption ? selectedOption.label : '';
        currentMeasureUnit.value = selectedOption ? selectedOption.measureUnit || '' : '';
      }
      
      console.log('回显数据:', {
        原始数据: data,
        处理后: formData,
        设备类型选项: deviceTypeOptions.value,
        监测指标选项: monitorIndexOptions.value,
        设备选项: deviceOptions.value
      });
    }
  } catch (error) {
    console.error('获取阈值详情失败', error);
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      if (newVal.id) {
        fetchThresholdDetail(newVal.id);
      }
    } else if (props.mode === 'add') {
      // 新增模式：重置表单并复制传入的数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key];
        }
      });
    }
  }
}, { immediate: true, deep: true });

// 监听设备类型变化
watch(() => formData.deviceType, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    fetchMonitorIndexes(newVal);
    
    // 只在新增模式或者确实是用户手动改变设备类型时才清空
    if (props.mode === 'add' || (oldVal && oldVal !== newVal)) {
      formData.monitorIndex = '';
      formData.deviceIds = [];
      currentMeasureUnit.value = '';
    }
    
    // 如果监测指标已选择，则获取设备列表
    if (formData.monitorIndex) {
      fetchDeviceList(newVal, formData.monitorIndex);
    }
  } else if (!newVal) {
    monitorIndexOptions.value = [];
    deviceOptions.value = [];
    formData.monitorIndex = '';
    formData.deviceIds = [];
    currentMeasureUnit.value = '';
  }
});

// 处理监测指标变化
const handleMonitorIndexChange = (value) => {
  // 更新监测指标名称和单位
  const selectedOption = monitorIndexOptions.value.find(item => item.value === value);
  formData.monitorIndexName = selectedOption ? selectedOption.label : '';
  currentMeasureUnit.value = selectedOption ? selectedOption.measureUnit || '' : '';
  
  // 清空设备选择
  formData.deviceIds = [];
  
  // 如果设备类型已选择，则获取设备列表
  if (formData.deviceType) {
    fetchDeviceList(formData.deviceType, value);
  }
};

// 监听监测指标变化
watch(() => formData.monitorIndex, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    // 只在新增模式或者确实是用户手动改变监测指标时才清空设备选择
    if (props.mode === 'add' || (oldVal && oldVal !== newVal)) {
      formData.deviceIds = [];
    }
    
    // 如果设备类型已选择，则获取设备列表
    if (formData.deviceType) {
      fetchDeviceList(formData.deviceType, newVal);
    }
  } else if (!newVal) {
    deviceOptions.value = [];
    formData.deviceIds = [];
  }
});

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'isEnabled') {
      formData[key] = true;
    } else if (key === 'notifySystem') {
      formData[key] = true;
    } else if (key === 'notifySms' || key === 'notifyEmail' || 
               key === 'notifyRightsDept1' || key === 'notifySuperviseDept1' || 
               key === 'notifyRightsDept2' || key === 'notifySuperviseDept2' || 
               key === 'notifyRightsDept3' || key === 'notifySuperviseDept3') {
      formData[key] = false;
    } else if (Array.isArray(formData[key])) {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });
  // 重置单位
  currentMeasureUnit.value = '';
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交数据
    const submitData = { ...formData };

    // 处理监管部门IDs字段
    if (Array.isArray(submitData.notifySuperviseDeptIds1)) {
      submitData.notifySuperviseDeptIds1 = submitData.notifySuperviseDeptIds1.join(',');
    }
    if (Array.isArray(submitData.notifySuperviseDeptIds2)) {
      submitData.notifySuperviseDeptIds2 = submitData.notifySuperviseDeptIds2.join(',');
    }
    if (Array.isArray(submitData.notifySuperviseDeptIds3)) {
      submitData.notifySuperviseDeptIds3 = submitData.notifySuperviseDeptIds3.join(',');
    }

    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveAlarmThreshold(submitData);
    } else if (props.mode === 'edit') {
      res = await updateAlarmThreshold(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 打开设备选择弹窗
const openDeviceSelectDialog = () => {
  if (props.mode !== 'view' && (!formData.deviceType || !formData.monitorIndex)) {
    ElMessage.warning('请先选择设备类型和监测指标');
    return;
  }
  deviceSelectDialogVisible.value = true;
};

// 确认设备选择
const handleDeviceSelectConfirm = (selectedIds) => {
  formData.deviceIds = selectedIds;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchManagementUnits();
  fetchDeviceTypes();
});
</script>

<style scoped>
.threshold-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}



.w-full {
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 20px 0 10px;
  padding-left: 10px;
  border-left: 3px solid #0277FD;
}

.alert-level {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin: 10px 0;
  padding: 5px 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.notification-item {
  margin-bottom: 20px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.select-unit {
  width: 300px;
  margin-left: 10px;
}

:deep(.el-checkbox) {
  margin-right: 15px;
}

/* 设备选择相关样式 */
.device-select-wrapper {
  width: 100%;
}

.device-select-btn {
  width: 100%;
  height: 36px;
  text-align: left;
  justify-content: flex-start;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  color: #606266;
  border-radius: 6px;
}

.device-select-btn:hover {
  border-color: #c0c4cc;
}

.device-select-btn:disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.selected-devices-preview {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.device-tag {
  margin: 0;
}
</style>