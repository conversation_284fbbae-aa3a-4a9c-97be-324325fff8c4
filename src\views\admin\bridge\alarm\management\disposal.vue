<template>
  <div class="disposal-container">
    <!-- 查询表单区域 -->
    <div class="search-section">
      <BridgeAlarmDisposalSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-cards">
      <div 
        class="stat-card" 
        :class="{ 'active': activeStatus === 'all' }" 
        @click="handleStatusFilter('all')"
      >
        <div class="stat-title">全部</div>
        <div class="stat-value">{{ statistics.total }}</div>
      </div>
      <div 
        class="stat-card" 
        :class="{ 'active': activeStatus === '待确认' }" 
        @click="handleStatusFilter('待确认')"
      >
        <div class="stat-title">待确认</div>
        <div class="stat-value">{{ statistics.pending }}</div>
      </div>
      <div 
        class="stat-card" 
        :class="{ 'active': activeStatus === '待处置' }" 
        @click="handleStatusFilter('待处置')"
      >
        <div class="stat-title">待处置</div>
        <div class="stat-value">{{ statistics.toProcess }}</div>
      </div>
      <div 
        class="stat-card" 
        :class="{ 'active': activeStatus === '处置中' }" 
        @click="handleStatusFilter('处置中')"
      >
        <div class="stat-title">处置中</div>
        <div class="stat-value">{{ statistics.processing }}</div>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :max-height="tableMaxHeight"
      v-loading="loading"
    >
      <el-table-column label="序号" width="60" align="center">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="报警来源" width="120" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ BRIDGE_ALARM_SOURCE_MAP[scope.row.alarmSource] || scope.row.alarmSource }}
        </template>
      </el-table-column>
      <el-table-column prop="alarmCode" label="报警编号" width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="alarmTime" label="报警时间" width="170" align="center">
        <template #default="scope">
          {{ formatAlarmTime(scope.row.alarmTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceId" label="报警设备编码" width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="deviceName" label="报警设备名称" width="140" align="center" show-overflow-tooltip />
      <el-table-column prop="alarmTypeName" label="报警类型" width="120" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="monitorObjectName" label="监测对象" width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="alarmValue" label="报警值" width="100" align="center">
        <template #default="scope">
          {{ scope.row.alarmValue }} {{ scope.row.alarmValueUnit || '' }}
        </template>
      </el-table-column>
      <el-table-column prop="alarmLocation" label="报警位置" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column prop="alarmLevelName" label="报警级别" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="报警状态" width="120" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ BRIDGE_ALARM_STATUS_MAP[scope.row.alarmStatus] || scope.row.alarmStatusName }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <template v-if="scope.row.alarmStatus === 4003701">
              <el-button type="primary" link @click.stop="handleConfirm(scope.row)">确认</el-button>
            </template>
            <template v-if="scope.row.alarmStatus === 4003703 || scope.row.alarmStatus === 4003704">
              <el-button type="primary" link @click.stop="handleDisposal(scope.row)">处置</el-button>
            </template>
            <el-button type="primary" link @click.stop="handleDetail(scope.row)">详情</el-button>
            <el-button type="primary" link @click.stop="handleLocation(scope.row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 报警详情弹窗 -->
    <BridgeAlarmDialog
      v-model:visible="dialogVisible"
      :alarm-id="currentAlarmId"
    />

    <!-- 报警确认弹窗 -->
    <BridgeAlarmConfirmDialog
      v-model:visible="confirmDialogVisible"
      :alarm-data="currentAlarmData"
      @success="handleConfirmSuccess"
    />

    <!-- 报警处置弹窗 -->
    <BridgeAlarmDisposalDialog
      v-model:visible="disposalDialogVisible"
      :alarm-data="currentAlarmData"
      @success="handleDisposalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElMessage, ElButton } from 'element-plus';
import BridgeAlarmDisposalSearch from './components/BridgeAlarmDisposalSearch.vue';
import BridgeAlarmDialog from './components/BridgeAlarmDialog.vue';
import BridgeAlarmConfirmDialog from './components/BridgeAlarmConfirmDialog.vue';
import BridgeAlarmDisposalDialog from './components/BridgeAlarmDisposalDialog.vue';
import { getBridgeAlarmList, getBridgeAlarmStatistics } from '@/api/bridge';
import { 
  BRIDGE_ALARM_LEVEL_MAP, 
  BRIDGE_ALARM_STATUS_MAP, 
  BRIDGE_ALARM_SOURCE_OPTIONS, 
  BRIDGE_ALARM_SOURCE_MAP, 
  BRIDGE_ALARM_TYPE_OPTIONS, 
  BRIDGE_ALARM_TYPE_MAP 
} from '@/constants/bridge';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 查询参数
const queryParams = ref({});

// 当前选中的状态筛选
const activeStatus = ref('all');

// 统计数据
const statistics = ref({
  total: 0,
  pending: 0,
  toProcess: 0,
  processing: 0,
  handled: 0
});

// 详情弹窗相关
const dialogVisible = ref(false);
const currentAlarmId = ref('');

// 报警确认弹窗相关
const confirmDialogVisible = ref(false);
const currentAlarmData = ref({});

// 报警处置弹窗相关
const disposalDialogVisible = ref(false);

// 格式化报警时间
const formatAlarmTime = (timeObj) => {
  if (!timeObj) return '';
  // 如果是时间戳对象格式
  if (typeof timeObj === 'object' && timeObj.time) {
    return new Date(timeObj.time).toLocaleString('zh-CN');
  }
  // 如果是字符串格式
  if (typeof timeObj === 'string') {
    return timeObj;
  }
  return '';
};

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return BRIDGE_ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    4003601: 'alarm-level-first',
    4003602: 'alarm-level-second',
    4003603: 'alarm-level-third',
    4003604: 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 处理状态筛选
const handleStatusFilter = (status) => {
  activeStatus.value = status;
  // 根据状态更新查询参数
  if (status === 'all') {
    delete queryParams.value.alarmStatus;
  } else {
    const statusMap = {
      '待确认': 4003701,
      '待处置': 4003703,
      '处置中': 4003704
    };
    queryParams.value.alarmStatus = statusMap[status];
  }
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理搜索
const handleSearch = (formData) => {
  // 转换查询参数
  const params = {};
  
  if (formData.alarmSource && formData.alarmSource !== '') {
    // 查找对应的数值
    const source = BRIDGE_ALARM_SOURCE_OPTIONS.find(item => item.label === formData.alarmSource);
    if (source) {
      params.alarmSource = source.value;
    }
  }
  
  if (formData.alarmLevel && formData.alarmLevel !== '') {
    params.alarmLevel = parseInt(formData.alarmLevel);
  }
  
  if (formData.alarmType && formData.alarmType !== '') {
    // 查找对应的数值
    params.alarmType = formData.alarmType;
  }
  
  if (formData.timeRange && formData.timeRange.length === 2) {
    params.startTime = formData.timeRange[0];
    params.endTime = formData.timeRange[1];
  }
  
  if (formData.deviceId) {
    params.deviceId = formData.deviceId;
  }
  
  queryParams.value = params;
  if (activeStatus.value !== 'all') {
    const statusMap = {
      '待确认': 4003701,
      '待处置': 4003703,
      '处置中': 4003704
    };
    queryParams.value.alarmStatus = statusMap[activeStatus.value];
  }

  currentPage.value = 1;
  fetchAlarmData();
  fetchAlarmStatistics();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  activeStatus.value = 'all';
  currentPage.value = 1;
  fetchAlarmData();
  fetchAlarmStatistics();
};

// 获取统计数据
const fetchAlarmStatistics = async () => {
  try {
    const res = await getBridgeAlarmStatistics({
      startDate: queryParams.value.startTime || "",
      endDate: queryParams.value.endTime || ""
    });
    if (res.code === 200 && res.data) {
      statistics.value = {
        total: res.data.totalAlarms || 0,
        pending: res.data.pendingConfirm || 0,
        toProcess: res.data.pendingHandle || 0,
        processing: res.data.handling || 0,
        handled: res.data.handled || 0
      };
    }
  } catch (error) {
    console.error('获取报警统计数据失败:', error);
    // 使用模拟数据
    statistics.value = {
      total: 10,
      pending: 2,
      toProcess: 2,
      processing: 6,
      handled: 0
    };
  }
};

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    loading.value = true;
    const res = await getBridgeAlarmList(currentPage.value, pageSize.value, queryParams.value);
    if (res.code === 200 && res.data) {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取报警数据失败:', error);
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchAlarmData();
};

// 表头样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleConfirm = (row) => {
  console.log('确认报警:', row);
  currentAlarmData.value = row;
  confirmDialogVisible.value = true;
};

const handleDisposal = (row) => {
  console.log('处置报警:', row);
  currentAlarmData.value = row;
  disposalDialogVisible.value = true;
};

const handleDetail = (row) => {
  currentAlarmId.value = row.id + "";
  dialogVisible.value = true;
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 确认成功回调
const handleConfirmSuccess = () => {
  fetchAlarmData(); // 重新获取数据
  fetchAlarmStatistics(); // 重新获取统计数据
};

// 处置成功回调
const handleDisposalSuccess = () => {
  fetchAlarmData(); // 重新获取数据
  fetchAlarmStatistics(); // 重新获取统计数据
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.disposal-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const statsSection = container.querySelector('.statistics-cards');
    const statsHeight = statsSection ? statsSection.offsetHeight : 60;
    const paginationReservedHeight = 180;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - statsHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchAlarmData();
  fetchAlarmStatistics();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
});

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.disposal-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索表单区域 */
.search-section {
  margin-bottom: 16px;
}

/* 统计卡片区域 */
.statistics-cards {
  display: flex;
  margin-bottom: 16px;
  gap: 16px;
}

.stat-card {
  flex: 1;
  height: 80px;
  background: #F8FAFD;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.stat-card.active {
  background: #EEF5FF;
  border: 1px solid #0086FF;
}

.stat-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  margin-bottom: 8px;
}

.stat-value {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 24px;
  color: #282828;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>