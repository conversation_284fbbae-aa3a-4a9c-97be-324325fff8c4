<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-cctv-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线编码" prop="pipelineCode">
            <el-select v-model="formData.pipelineCode" placeholder="请选择" class="w-full" @change="handlePipelineChange">
              <el-option v-for="item in pipelineList" :key="item.id" :label="item.pipelineCode" :value="item.pipelineCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管线类型" prop="pipelineTypeName">
            <el-input v-model="formData.pipelineTypeName" placeholder="根据管线自动获取" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="根据管线自动获取" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="managementUnitName">
            <el-input v-model="formData.managementUnitName" placeholder="根据管线自动获取" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管道结构性缺陷类型" prop="defectType">
            <el-select v-model="formData.defectType" placeholder="请选择" class="w-full" @change="handleDefectTypeChange">
              <el-option v-for="item in defectTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="缺陷等级" prop="defectLevel">
            <el-select v-model="formData.defectLevel" placeholder="请选择" class="w-full" @change="handleDefectLevelChange">
              <el-option v-for="item in defectLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检测日期" prop="detectTime">
            <el-date-picker
              v-model="formData.detectTime"
              type="datetime"
              placeholder="请选择检测日期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="formData.remarks" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider>检测资料</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管道检测记录表">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handlePipeRecordUpload"
                :file-list="pipeRecordFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="pipeRecordFileList.length > 0">
                <el-link
                  v-for="(file, index) in pipeRecordFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="窨井检测记录表">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleWellRecordUpload"
                :file-list="wellRecordFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="wellRecordFileList.length > 0">
                <el-link
                  v-for="(file, index) in wellRecordFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排水窨井总表">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleWellSummaryUpload"
                :file-list="wellSummaryFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="wellSummaryFileList.length > 0">
                <el-link
                  v-for="(file, index) in wellSummaryFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排水管道成果表">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handlePipeResultUpload"
                :file-list="pipeResultFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="pipeResultFileList.length > 0">
                <el-link
                  v-for="(file, index) in pipeResultFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管道评估表">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handlePipeAssessUpload"
                :file-list="pipeAssessFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="pipeAssessFileList.length > 0">
                <el-link
                  v-for="(file, index) in pipeAssessFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="沉积物剖面图">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleSedimentProfileUpload"
                :file-list="sedimentProfileFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="sedimentProfileFileList.length > 0">
                <el-link
                  v-for="(file, index) in sedimentProfileFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="评分表">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleScoreUpload"
                :file-list="scoreFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.doc、.docx、.pdf、.excel、.jpg、.bmp、.png、.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="scoreFileList.length > 0">
                <el-link
                  v-for="(file, index) in scoreFileList"
                  :key="index"
                  :href="file.url"
                  target="_blank"
                  type="primary"
                  class="mr-2"
                >
                  {{ file.name }}
                </el-link>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider>CCTV资料</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="CCTV检测图片">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleCCTVPicUpload"
                :file-list="cctvPicFileList"
                :on-error="handleUploadError"
                list-type="picture-card"
                :on-preview="handlePicturePreview"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">支持.jpg、.bmp、.png等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <el-image
                v-for="(file, index) in cctvPicFileList"
                :key="index"
                :src="file.url"
                :preview-src-list="cctvPicFileList.map(item => item.url)"
                fit="cover"
                style="width: 100px; height: 100px; margin-right: 10px;"
              />
              <span v-if="cctvPicFileList.length === 0">无</span>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="CCTV检测视频">
            <template v-if="mode !== 'view'">
              <el-upload
                class="upload-demo"
                :http-request="handleCCTVVideoUpload"
                :file-list="cctvVideoFileList"
                :on-error="handleUploadError"
              >
                <el-button type="primary">上传视频</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持.mp4等格式</div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div v-if="cctvVideoFileList.length > 0">
                <div v-for="(file, index) in cctvVideoFileList" :key="index" class="video-preview">
                  <video :src="file.url" controls style="max-width: 300px; max-height: 200px;"></video>
                  <div>{{ file.name }}</div>
                </div>
              </div>
              <span v-else>无</span>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>

    <el-dialog v-model="previewVisible" append-to-body>
      <img w-full :src="previewUrl" alt="Preview Image" />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { saveCCTV, updateCCTV, getCCTVDetail, getPipelineList } from '@/api/drainage';
import { DEFECT_TYPE_OPTIONS, DEFECT_LEVEL_OPTIONS } from '@/constants/drainage';
import { uploadFile } from '@/api/upload';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增CCTV检测信息',
    edit: '编辑CCTV检测信息',
    view: 'CCTV检测信息详情'
  };
  return titles[props.mode] || 'CCTV检测信息';
});

// 表单数据
const formData = reactive({
  id: '',
  pipelineCode: '',
  pipelineId: '',
  pipelineType: '',
  pipelineTypeName: '',
  roadName: '',
  managementUnit: '',
  managementUnitName: '',
  defectType: '',
  defectTypeName: '',
  defectLevel: '',
  defectLevelName: '',
  detectTime: '',
  remarks: '',
  pipeRecordUrls: '',
  wellRecordUrls: '',
  drainWellSummaryUrls: '',
  drainPipeResultUrls: '',
  pipeAssessUrls: '',
  sedimentProfileUrls: '',
  scoreUrls: '',
  cctvPicUrls: '',
  cctvVideoUrls: ''
});

// 表单验证规则
const formRules = {
  pipelineCode: [{ required: true, message: '请选择管线编码', trigger: 'change' }],
  defectType: [{ required: true, message: '请选择管道结构性缺陷类型', trigger: 'change' }],
  defectLevel: [{ required: true, message: '请选择缺陷等级', trigger: 'change' }],
  detectTime: [{ required: true, message: '请选择检测日期', trigger: 'change' }]
};

// 管线列表
const pipelineList = ref([]);

// 缺陷类型和等级选项，过滤掉"全部"选项
const defectTypeOptions = DEFECT_TYPE_OPTIONS.filter(item => item.value !== '');
const defectLevelOptions = DEFECT_LEVEL_OPTIONS.filter(item => item.value !== '');

// 文件列表
const pipeRecordFileList = ref([]);
const wellRecordFileList = ref([]);
const wellSummaryFileList = ref([]);
const pipeResultFileList = ref([]);
const pipeAssessFileList = ref([]);
const sedimentProfileFileList = ref([]);
const scoreFileList = ref([]);
const cctvPicFileList = ref([]);
const cctvVideoFileList = ref([]);

// 图片预览
const previewVisible = ref(false);
const previewUrl = ref('');

const handlePicturePreview = (file) => {
  previewUrl.value = file.url || URL.createObjectURL(file.raw);
  previewVisible.value = true;
};

// 获取管线列表
const fetchPipelineList = async () => {
  try {
    const res = await getPipelineList({});
    if (res && res.code === 200) {
      pipelineList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 处理管线选择变化
const handlePipelineChange = (value) => {
  const selectedPipeline = pipelineList.value.find(item => item.pipelineCode === value);
  if (selectedPipeline) {
    formData.pipelineId = selectedPipeline.id;
    formData.pipelineType = selectedPipeline.pipelineType;
    formData.pipelineTypeName = selectedPipeline.pipelineTypeName;
    formData.roadName = selectedPipeline.road || '';
    formData.managementUnit = selectedPipeline.managementUnit || '';
    formData.managementUnitName = selectedPipeline.managementUnitName || '';
  }
};

// 处理管道结构性缺陷类型变化
const handleDefectTypeChange = (value) => {
  const defectTypeOption = defectTypeOptions.find(item => item.value === value);
  formData.defectTypeName = defectTypeOption ? defectTypeOption.label : '';
};

// 处理缺陷等级变化
const handleDefectLevelChange = (value) => {
  const defectLevelOption = defectLevelOptions.find(item => item.value === value);
  formData.defectLevelName = defectLevelOption ? defectLevelOption.label : '';
};

// 文件上传处理
const handlePipeRecordUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.pipeRecordUrls = res.data.url;
      pipeRecordFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传管道检测记录表失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleWellRecordUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.wellRecordUrls = res.data.url;
      wellRecordFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传窨井检测记录表失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleWellSummaryUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.drainWellSummaryUrls = res.data.url;
      wellSummaryFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传排水窨井总表失败:', error);
    ElMessage.error('上传失败');
  }
};

const handlePipeResultUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.drainPipeResultUrls = res.data.url;
      pipeResultFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传排水管道成果表失败:', error);
    ElMessage.error('上传失败');
  }
};

const handlePipeAssessUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.pipeAssessUrls = res.data.url;
      pipeAssessFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传管道评估表失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleSedimentProfileUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.sedimentProfileUrls = res.data.url;
      sedimentProfileFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传沉积物剖面图失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleScoreUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.scoreUrls = res.data.url;
      scoreFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传评分表失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleCCTVPicUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.cctvPicUrls = res.data.url;
      cctvPicFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传CCTV检测图片失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleCCTVVideoUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.cctvVideoUrls = res.data.url;
      cctvVideoFileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传CCTV检测视频失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleUploadError = () => {
  ElMessage.error('上传失败');
};

// 获取CCTV检测详情
const fetchCCTVDetail = async (id) => {
  try {
    const res = await getCCTVDetail(id);
    if (res && res.code === 200) {
      const data = res.data;
      
      // 将详情数据映射到表单
      Object.keys(formData).forEach(key => {
        if (data[key] !== undefined) {
          formData[key] = data[key];
        }
      });
      
      // 设置文件列表
      if (data.pipeRecordUrls) {
        pipeRecordFileList.value = [{ name: '管道检测记录表', url: data.pipeRecordUrls }];
      }
      if (data.wellRecordUrls) {
        wellRecordFileList.value = [{ name: '窨井检测记录表', url: data.wellRecordUrls }];
      }
      if (data.drainWellSummaryUrls) {
        wellSummaryFileList.value = [{ name: '排水窨井总表', url: data.drainWellSummaryUrls }];
      }
      if (data.drainPipeResultUrls) {
        pipeResultFileList.value = [{ name: '排水管道成果表', url: data.drainPipeResultUrls }];
      }
      if (data.pipeAssessUrls) {
        pipeAssessFileList.value = [{ name: '管道评估表', url: data.pipeAssessUrls }];
      }
      if (data.sedimentProfileUrls) {
        sedimentProfileFileList.value = [{ name: '沉积物剖面图', url: data.sedimentProfileUrls }];
      }
      if (data.scoreUrls) {
        scoreFileList.value = [{ name: '评分表', url: data.scoreUrls }];
      }
      if (data.cctvPicUrls) {
        cctvPicFileList.value = [{ name: 'CCTV检测图片', url: data.cctvPicUrls }];
      }
      if (data.cctvVideoUrls) {
        cctvVideoFileList.value = [{ name: 'CCTV检测视频', url: data.cctvVideoUrls }];
      }
    } else {
      ElMessage.error(res?.msg || '获取CCTV检测详情失败');
    }
  } catch (error) {
    console.error('获取CCTV检测详情失败:', error);
    ElMessage.error('获取CCTV检测详情失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPipelineList();
});

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    if (props.mode === 'edit' || props.mode === 'view') {
      // 编辑模式或查看模式，需要获取详情
      if (newVal.id) {
        fetchCCTVDetail(newVal.id);
      }
    } else {
      // 新增模式，直接使用传入的默认数据
      Object.keys(formData).forEach(key => {
        if (newVal[key] !== undefined) {
          formData[key] = newVal[key];
        }
      });
    }
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  // 重置文件列表
  pipeRecordFileList.value = [];
  wellRecordFileList.value = [];
  wellSummaryFileList.value = [];
  pipeResultFileList.value = [];
  pipeAssessFileList.value = [];
  sedimentProfileFileList.value = [];
  scoreFileList.value = [];
  cctvPicFileList.value = [];
  cctvVideoFileList.value = [];
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 确保数值类型字段为数值
    if (submitData.pipelineType) submitData.pipelineType = Number(submitData.pipelineType);
    if (submitData.defectType) submitData.defectType = Number(submitData.defectType);
    if (submitData.defectLevel) submitData.defectLevel = Number(submitData.defectLevel);
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveCCTV(submitData);
    } else if (props.mode === 'edit') {
      res = await updateCCTV(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.drainage-cctv-dialog {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-select__input) {
  border-radius: 6px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.mr-2 {
  margin-right: 8px;
}

.video-preview {
  margin-bottom: 10px;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  --el-upload-list-picture-card-size: 100px;
}
</style> 