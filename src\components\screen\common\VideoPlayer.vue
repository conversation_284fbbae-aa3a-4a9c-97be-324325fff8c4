<template>
  <div class="video-player-container">
    <div ref="videoContainer" class="video-container"></div>
    <div v-if="showControls" class="video-controls">
      <div class="control-btn" @click="togglePlay">
        <i :class="isPlaying ? 'icon-pause' : 'icon-play'"></i>
      </div>
      <div class="control-btn" @click="toggleFullscreen">
        <i class="icon-fullscreen"></i>
      </div>
    </div>
    <div v-if="showStatus" class="video-status" :class="{'status-online': isOnline, 'status-offline': !isOnline}">
      {{ isOnline ? '在线' : '离线' }}
    </div>
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import Hls from 'hls.js';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  muted: {
    type: Boolean,
    default: true
  },
  loop: {
    type: Boolean,
    default: false
  },
  controls: {
    type: Boolean,
    default: false
  },
  fluid: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 225
  },
  type: {
    type: String,
    default: 'application/x-mpegURL'
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showStatus: {
    type: Boolean,
    default: false
  },
  isOnline: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['play', 'pause', 'fullscreen', 'error']);

const videoContainer = ref(null);
const player = ref(null);
const hls = ref(null);
const isPlaying = ref(false);
const errorMessage = ref('');

// 播放/暂停切换
const togglePlay = () => {
  if (player.value) {
    if (player.value.paused()) {
      player.value.play();
      isPlaying.value = true;
      emit('play');
    } else {
      player.value.pause();
      isPlaying.value = false;
      emit('pause');
    }
  }
};

// 切换全屏
const toggleFullscreen = () => {
  if (player.value) {
    if (player.value.isFullscreen()) {
      player.value.exitFullscreen();
    } else {
      player.value.requestFullscreen();
    }
    emit('fullscreen');
  }
};

// 销毁HLS实例
const destroyHls = () => {
  if (hls.value) {
    hls.value.destroy();
    hls.value = null;
  }
};

// 初始化播放器
const initPlayer = () => {
  try {
    errorMessage.value = '';
    
    // 清理之前的实例
    if (player.value) {
      player.value.dispose();
      player.value = null;
    }
    destroyHls();

    // 创建video元素
    const videoElement = document.createElement('video');
    videoElement.className = 'video-js vjs-default-skin';
    videoElement.crossOrigin = 'anonymous'; // 添加跨域支持
    videoElement.style.width = '100%';
    videoElement.style.height = '100%';
    videoElement.style.objectFit = 'cover';
    
    // 添加到DOM
    if (videoContainer.value) {
      videoContainer.value.innerHTML = '';
      videoContainer.value.appendChild(videoElement);
    }

    // 检查HLS支持
    const isHlsStream = props.src && props.src.includes('.m3u8');
    
    if (isHlsStream) {
      if (Hls.isSupported()) {
        // 使用hls.js处理HLS流
        initHlsPlayer(videoElement);
      } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持HLS
        initNativeHlsPlayer(videoElement);
      } else {
        errorMessage.value = '浏览器不支持HLS播放';
        console.error('HLS is not supported');
        return;
      }
    } else {
      // 普通视频流
      initNormalPlayer(videoElement);
    }
  } catch (error) {
    console.error('初始化播放器失败:', error);
    errorMessage.value = '播放器初始化失败';
    emit('error', error);
  }
};

// 初始化HLS播放器（使用hls.js）
const initHlsPlayer = (videoElement) => {
  // 初始化video.js播放器
  player.value = videojs(videoElement, {
    controls: props.controls,
    autoplay: props.autoplay,
    muted: props.muted,
    loop: props.loop,
    fill: true,
    techOrder: ['html5'],
    html5: {
      hls: {
        overrideNative: true
      }
    }
  });

  // 播放器准备就绪后初始化HLS
  player.value.ready(() => {
    try {
      hls.value = new Hls({
        enableWorker: false,
        lowLatencyMode: true,
        backBufferLength: 30
      });

      // HLS事件监听
      hls.value.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('HLS media attached');
        hls.value.loadSource(props.src);
      });

      hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed');
        if (props.autoplay) {
          videoElement.play().catch(e => {
            console.log('自动播放失败:', e);
          });
        }
      });

      hls.value.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS错误:', data);
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('网络错误，尝试重新加载');
              hls.value.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('媒体错误，尝试恢复');
              hls.value.recoverMediaError();
              break;
            default:
              console.log('无法恢复的错误');
              errorMessage.value = '视频加载失败，请检查网络连接';
              break;
          }
        }
        emit('error', data);
      });

      // 附加到video元素
      hls.value.attachMedia(videoElement);

    } catch (error) {
      console.error('HLS初始化失败:', error);
      errorMessage.value = 'HLS初始化失败';
      emit('error', error);
    }
  });

  // 添加播放器事件监听
  addPlayerEventListeners();
};

// 初始化原生HLS播放器（Safari）
const initNativeHlsPlayer = (videoElement) => {
  videoElement.src = props.src;

  player.value = videojs(videoElement, {
    controls: props.controls,
    autoplay: props.autoplay,
    muted: props.muted,
    loop: props.loop,
    fluid: false,
    responsive: false,
    fill: true
  });

  addPlayerEventListeners();
};

// 初始化普通播放器
const initNormalPlayer = (videoElement) => {
  player.value = videojs(videoElement, {
    controls: props.controls,
    autoplay: props.autoplay,
    muted: props.muted,
    loop: props.loop,
    fluid: false,
    responsive: false,
    fill: true,
    sources: [{
      src: props.src,
      type: props.type
    }]
  });

  addPlayerEventListeners();
};

// 添加播放器事件监听
const addPlayerEventListeners = () => {
  if (!player.value) return;

  player.value.on('play', () => {
    isPlaying.value = true;
    errorMessage.value = '';
    emit('play');
  });
  
  player.value.on('pause', () => {
    isPlaying.value = false;
    emit('pause');
  });
  
  player.value.on('error', (error) => {
    console.error('Video.js播放错误:', error);
    errorMessage.value = '视频播放出错，请稍后重试';
    emit('error', error);
  });

  player.value.on('loadstart', () => {
    console.log('开始加载视频');
  });

  player.value.on('canplay', () => {
    console.log('视频可以播放');
    errorMessage.value = '';
  });
};

// 暴露的方法
const play = () => {
  if (player.value) {
    player.value.play();
    isPlaying.value = true;
  }
};

const pause = () => {
  if (player.value) {
    player.value.pause();
    isPlaying.value = false;
  }
};

const maximize = () => {
  if (player.value && !player.value.isFullscreen()) {
    player.value.requestFullscreen();
  }
};

// 监听src变化，重新初始化播放器
watch(() => props.src, (newSrc) => {
  if (newSrc) {
    initPlayer();
  }
});

onMounted(() => {
  if (props.src) {
    initPlayer();
  }
});

onUnmounted(() => {
  if (player.value) {
    player.value.dispose();
  }
  destroyHls();
});

// 暴露组件方法
defineExpose({
  play,
  pause,
  maximize,
  player
});
</script>

<style scoped>
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  border-radius: 4px;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.video-controls {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
  opacity: 0;
  z-index: 20;
}

.video-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  z-index: 20;
}

.status-online {
  background-color: rgba(16, 185, 129, 0.8);
  color: white;
}

.status-offline {
  background-color: rgba(156, 163, 175, 0.8);
  color: white;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(220, 38, 38, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 30;
  max-width: 80%;
  text-align: center;
}

.video-player-container:hover .video-controls {
  opacity: 1;
}

.control-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.control-btn i {
  color: #fff;
  font-size: 16px;
}

/* 图标样式 */
.icon-play:before {
  content: "▶";
}

.icon-pause:before {
  content: "⏸";
}

.icon-fullscreen:before {
  content: "⤢";
}

:deep(.video-js) {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  position: relative !important;
}

:deep(.video-js .vjs-tech) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

:deep(.video-js video) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

:deep(.vjs-poster) {
  background-size: cover;
}

:deep(.vjs-control-bar) {
  display: none !important;
}

:deep(.vjs-big-play-button) {
  display: none !important;
}

:deep(.vjs-error-display) {
  display: none !important;
}
</style> 