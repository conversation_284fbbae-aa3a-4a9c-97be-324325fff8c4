<template>
  <div class="heating-plan-container">
    <!-- 搜索区域 -->
    <div class="heating-plan-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">事件分类:</span>
          <el-select v-model="formData.eventType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">预案级别:</span>
          <el-select v-model="formData.schemeLevel" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in schemeLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.schemeName" class="form-input" placeholder="输入预案名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :max-height="tableMaxHeight"
      empty-text="暂无数据" v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="schemeName" label="预案名称" min-width="120" show-overflow-tooltip />
      <el-table-column prop="eventTypeName" label="事件分类" min-width="100" show-overflow-tooltip />
      <el-table-column prop="responseLevelName" label="响应等级" min-width="120" show-overflow-tooltip />
      <el-table-column prop="schemeLevelName" label="预案级别" min-width="100" show-overflow-tooltip />
      <el-table-column prop="schemeClassification" label="预案分类" min-width="120" show-overflow-tooltip />
      <el-table-column prop="drawupUnit" label="制定单位" min-width="120" show-overflow-tooltip />
      <el-table-column prop="issuedUnitName" label="下发部门" min-width="120" show-overflow-tooltip />
      <el-table-column label="附件" min-width="80">
        <template #default="{ row }">
          <el-link v-if="row.fileUrls" type="primary" @click="handleViewFiles(row)">查看附件</el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="预案状态" min-width="80">
        <template #default="{ row }">
          <span class="status-tag" :class="getStatusClass(row.reviewStatus)">
            {{ getStatusText(row.reviewStatus) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="200">
        <template #default="{ row }">
          <div class="operation-btns">
            <el-button v-if="row.reviewStatus === '0'" type="primary" link @click.stop="handleReview(row)">审核</el-button>
            <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
            <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 预案弹窗 -->
    <PlanDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />

    <!-- 审核弹窗 -->
    <el-dialog
      v-model="reviewDialogVisible"
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      class="review-dialog"
    >
      <el-form
        ref="reviewFormRef"
        :model="reviewFormData"
        :rules="reviewRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="reviewStatus">
          <el-select v-model="reviewFormData.reviewStatus" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in reviewStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleReviewCancel">取消</el-button>
          <el-button type="primary" @click="handleReviewSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElLink } from 'element-plus';
import { 
  getEmergencyPlanPage, 
  deleteEmergencyPlan, 
  getEmergencyPlanDetail,
  updateEmergencyPlan
} from '@/api/heating';
import { 
  EVENT_TYPE_OPTIONS, 
  SCHEME_LEVEL_OPTIONS,
  PLAN_REVIEW_STATUS_OPTIONS,
  PLAN_REVIEW_STATUS_MAP
} from '@/constants/heating';
import PlanDialog from './components/PlanDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);

// 下拉选项数据
const eventTypeOptions = ref(EVENT_TYPE_OPTIONS);
const schemeLevelOptions = ref(SCHEME_LEVEL_OPTIONS);
const reviewStatusOptions = ref(PLAN_REVIEW_STATUS_OPTIONS);

// 表单数据
const formData = ref({
  eventType: '',
  schemeLevel: '',
  schemeName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 审核弹窗相关
const reviewDialogVisible = ref(false);
const reviewFormRef = ref(null);
const reviewFormData = ref({
  id: '',
  reviewStatus: '',
});

// 审核表单验证规则
const reviewRules = {
  reviewStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
};

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 获取状态文本
const getStatusText = (status) => {
  return PLAN_REVIEW_STATUS_MAP[status] || '未审核';
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    "0": 'status-pending',   // 未审核 - 黄色
    "1": 'status-approved',  // 通过 - 绿色
    "2": 'status-rejected'   // 驳回 - 红色
  };
  return classMap[status] || 'status-pending';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchPlanData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    eventType: '',
    schemeLevel: '',
    schemeName: ''
  };
  currentPage.value = 1;
  fetchPlanData();
};

// 获取预案分页数据
const fetchPlanData = async () => {
  try {
    loading.value = true;
    const params = {
      eventType: formData.value.eventType,
      schemeLevel: formData.value.schemeLevel,
      schemeName: formData.value.schemeName
    };
    
    const res = await getEmergencyPlanPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取预案数据失败:', error);
    ElMessage.error('获取预案数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchPlanData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchPlanData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getEmergencyPlanDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取预案详情失败');
    }
  } catch (error) {
    console.error('获取预案详情失败:', error);
    ElMessage.error('获取预案详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getEmergencyPlanDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取预案详情失败');
    }
  } catch (error) {
    console.error('获取预案详情失败:', error);
    ElMessage.error('获取预案详情失败');
  }
};

// 处理审核
const handleReview = (row) => {
  reviewFormData.value = {
    id: row.id,
    reviewStatus: '',
  };
  reviewDialogVisible.value = true;
};

// 处理审核取消
const handleReviewCancel = () => {
  reviewDialogVisible.value = false;
  reviewFormData.value = {
    id: '',
    reviewStatus: '',
  };
};

// 处理审核提交
const handleReviewSubmit = async () => {
  if (!reviewFormRef.value) return;

  try {
    await reviewFormRef.value.validate();

    const submitData = {
      id: reviewFormData.value.id,
      reviewStatus: reviewFormData.value.reviewStatus,
    };

    const res = await updateEmergencyPlan(submitData);
    
    if (res && res.code === 200) {
      ElMessage.success('审核成功');
      reviewDialogVisible.value = false;
      handleReviewCancel();
      fetchPlanData(); // 刷新列表
    } else {
      ElMessage.error(res?.msg || '审核失败');
    }
  } catch (error) {
    console.error('审核失败:', error);
    ElMessage.error('审核失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该预案信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEmergencyPlan(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPlanData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除预案失败:', error);
      ElMessage.error('删除预案失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理查看附件
const handleViewFiles = (row) => {
  if (row.fileUrls) {
    const urls = row.fileUrls.split(',');
    urls.forEach((url, index) => {
      window.open(url, `_blank_${index}`);
    });
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchPlanData();
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.heating-plan-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.heating-plan-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;
    const paginationReservedHeight = 80;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(() => {
  fetchPlanData();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.heating-plan-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.heating-plan-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 审核弹窗样式 */
.review-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.review-dialog .el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.review-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.review-dialog .el-dialog__body) {
  padding: 20px;
}

:deep(.review-dialog .el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}
</style>