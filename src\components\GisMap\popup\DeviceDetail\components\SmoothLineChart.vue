<template>
  <div
    class="echarts"
    :id="id"
    :style="{
      width: width,
      height: height
    }"
    v-echart-resize
  ></div>
</template>

<script setup>
import { defineProps, onMounted, onUnmounted, ref, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import moment from "moment";

const aa = '1927'
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  value: {
    type: Object,
    default: () => {}
  },
  redThreshold: {
    type: Number,
    default: null
  },
  orangeThreshold: {
    type: Number,
    default: null
  },
  yellowThreshold: {
    type: Number,
    default: null
  },
  latestAlarm: {
    type: Object,
    default: null
  }
})
const chart = ref(null)

const unwarp = (obj) => obj && (obj.__v_raw || obj.valueOf() || obj)

const setOptions = (item) => {
  let chartDta = []
  let datetime = []
  for (let index in item) {
    chartDta.push({
      name: item[index].name,
      value: item[index].value,
      unit: item[index].unit,
      jczb: item[index].jczb,
      type: item[index].type,
    })
    datetime.push(item[index].name)
  }

  // 计算数据中的最大值和最小值
  const dataValues = chartDta.map(item => item.value).filter(val => typeof val === 'number' && !isNaN(val))
  const dataMax = dataValues.length > 0 ? Math.max(...dataValues) : 0
  const dataMin = dataValues.length > 0 ? Math.min(...dataValues) : 0

  // 收集所有需要显示的阈值
  const thresholds = []
  if (props.redThreshold !== null) thresholds.push(props.redThreshold)
  if (props.orangeThreshold !== null) thresholds.push(props.orangeThreshold)
  if (props.yellowThreshold !== null) thresholds.push(props.yellowThreshold)

  // 计算y轴的范围，确保包含所有数据和阈值
  let yAxisMin = dataMin
  let yAxisMax = dataMax

  if (thresholds.length > 0) {
    const thresholdMax = Math.max(...thresholds)
    const thresholdMin = Math.min(...thresholds)

    // 扩展y轴范围以包含所有阈值
    yAxisMin = Math.min(yAxisMin, thresholdMin)
    yAxisMax = Math.max(yAxisMax, thresholdMax)
  }

  // 添加一些边距，确保警戒线不会贴边
  const range = yAxisMax - yAxisMin
  let padding = range * 0.1 // 10%的边距

  if (range === 0) {
    padding = 1
  }

  yAxisMin = Math.floor(yAxisMin - padding)
  yAxisMax = Math.ceil(yAxisMax + padding)

  // 确保最小值不小于0（如果数据都是正数）
  if (dataMin >= 0 && yAxisMin < 0) {
    yAxisMin = 0
  }

  const option = {
    width: '90%',
    height: '85%',
    grid: {
      top: '10%',
      left: '5%',
      right: '5%',
      bottom: '5%',
      containLabel: true
    },
    // legend: {
    //   x: '50%',
    //   y: '5%',
    //   itemWidth: 5,
    //   itemHeight: 5,
    //   itemGap: 20,
    //   textStyle: {
    //     color: 'rgba(255, 255, 255, 1)',
    //     fontSize: '14'
    //   },
    //   data: ['20cm', '40cm', '60cm']
    // },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove', // click,
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
      },
      enterable: true, // 鼠标是否可进入提示框浮层中
      backgroundColor: 'rgb(20,49,87)',
      borderColor: 'rgba(0, 255, 186, 0.6)',
      textStyle: {
        color: '#fff'
      },
      hideDelay: 50, // 浮层隐藏的延迟
      confine: true,
      formatter: function (res) {
          if (res && res.length > 0) {
              if (res[0].data.type === 0){
                  return `<div style="display: flex; align-items: flex-start; flex-direction: column;">
                            <div>时间：${res[0].data.name}</div>
                            <div>${res[0].data.jczb}：${res[0].data.value === 0? "正常": res[0].data.value === 1 ? "异常" :"无数据"}</div>
                          </div>`
              } else {
                  return `<div style="display: flex; align-items: flex-start; flex-direction: column;">
                            <div>时间：${res[0].data.name}</div>
                            <div>${res[0].data.jczb}：${res[0].data.value}${res[0].data.unit}</div>
                          </div>`
              }
          } else {
              return `<div style="display: flex;justify-content: space-between;">请滑动鼠标</div>`
          }
      }
    // formatter: '{b} : {c}'
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.6)'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.5)'
          },
          formatter: (v) => {
              return moment(v).format("MM月DD日 HH:mm").replace(" ", "\n");
          },
          interval: 'auto', // 自动计算标签显示数量
          showMaxLabel: true, // 显示最后一个标签
          showMinLabel: true  // 显示第一个标签
        },
        axisTick: {
          show: true,
          alignWithLabel: true
        },
        show: true, // 确保 xAxis 始终显示
        data: datetime.length > 0 ? datetime : [] // 提供默认值
      }
    ],
    yAxis: [
      {
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.5)',
          fontSize: 13,
          fontFamily: 'Microsoft YaHei'
        },
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.1,
            color: 'rgba(255, 255, 255, 0.6)'
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.6)'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.5)',
            // padding: 16
            fontSize: 12
          },
          formatter: function (value) {
            return value
          }
        },
        axisTick: {
          show: true
        },
        show: true, // 确保 yAxis 始终显示
        min: yAxisMin, // 设置y轴最小值
        max: yAxisMax  // 设置y轴最大值
      }
    ],
    series: [
      {
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        itemStyle: {
          color: 'rgba(90, 228, 255, 1)'
        },
        tooltip: {
          show: true
        },
        areaStyle: {
          //区域填充样式
          normal: {
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是'true'，则该四个值是绝对像素位置。
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(237, 218, 56, 1)'
                },
                {
                  offset: 0.5,
                  color: 'rgba(237, 218, 56, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(237, 218, 56, 0)'
                }
              ],
              false
            )
          }
        },
        data: chartDta,
        // 添加警戒线配置
        markLine: {
          silent: true,
          symbol: 'none',
          lineStyle: {
            type: 'dashed'
          },
          data: [
            // 红色警戒线
            ...(props.redThreshold !== null ? [{
              yAxis: props.redThreshold,
              lineStyle: {
                color: '#ff0000',
                type: 'dashed',
                width: 2
              },
              label: {
                show: true,
                position: 'insideEndTop',
                formatter: props.redThreshold,
                // formatter: `红色警戒线: ${props.redThreshold}`,
                color: '#ff0000',
                fontSize: 12
              }
            }] : []),
            // 橙色警戒线
            ...(props.orangeThreshold !== null ? [{
              yAxis: props.orangeThreshold,
              lineStyle: {
                color: '#ff8c00',
                type: 'dashed',
                width: 2
              },
              label: {
                show: true,
                position: 'insideEndTop',
                formatter: props.orangeThreshold,
                color: '#ff8c00',
                fontSize: 12
              }
            }] : []),
            // 黄色警戒线
            ...(props.yellowThreshold !== null ? [{
              yAxis: props.yellowThreshold,
              lineStyle: {
                color: '#ffff00',
                type: 'dashed',
                width: 2
              },
              label: {
                show: true,
                position: 'insideEndTop',
                formatter: props.yellowThreshold,
                color: '#ffff00',
                fontSize: 12
              }
            }] : [])
          ]
        },
        markPoint: props.latestAlarm ? {
          symbol: 'arrow',
          symbolSize: 10,
          itemStyle: {
            color: '#ff0000',
            borderWidth: 2,
          },
          label: {
            show: true,
            position: 'top',
            align: 'center',
            lineHeight: 15,
            formatter: function() {
              if (props.latestAlarm?.type === 0) {
                return `当前报警\n${props.latestAlarm?.value === 0 ? "正常" : props.latestAlarm?.value === 1 ? "异常" : "无数据"}`
              } else {
                return `当前报警\n${props.latestAlarm?.value}${props.latestAlarm?.unit || ''}`
              }
            },
            backgroundColor: '#ff0000',
            color: '#fff',
            padding: [4, 4, 1, 4],
            borderRadius: 4
          },
          data: [
            {
              name: '最新报警',
              xAxis: props.latestAlarm?.name,
              yAxis: props.latestAlarm?.value
            }
          ]
        } : null
      }
    ]
  }
  chart.value && unwarp(chart.value).setOption(option)
}

const initChart = () => {
  nextTick(() => {
    const _doc = document.getElementById(props.id)
    if (!chart.value) {
      chart.value = echarts.init(_doc)
    } else {
      // _doc.removeAttribute('_echarts_instance_')
      chart.value.dispose()
      chart.value = echarts.init(_doc)
    }
    setOptions(props.value)
  })
}

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 100)
})

onUnmounted(() => {
  if (!chart.value) {
    return
  }
  chart.value.dispose()
  chart.value = null
})
watch(
  () => [props.value, props.redThreshold, props.orangeThreshold, props.yellowThreshold, props.latestAlarm],
  () => {
      setTimeout(() => {
          initChart()
      }, 100)
  },
  {
    deep: true,
    immediate: true
  }
)
</script>

<style lang="scss" scoped></style>
