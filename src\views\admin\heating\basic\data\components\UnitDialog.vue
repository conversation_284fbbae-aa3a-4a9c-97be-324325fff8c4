<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="unit-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="机组名称" prop="unitName">
            <el-input v-model="formData.unitName" placeholder="请输入机组名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机组编号" prop="unitCode">
            <el-input v-model="formData.unitCode" placeholder="请输入机组编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="机组类型" prop="unitType">
            <el-select v-model="formData.unitType" placeholder="请选择" class="w-full">
              <el-option v-for="item in UNIT_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采集编号" prop="collectCode">
            <el-input v-model="formData.collectCode" placeholder="请输入采集编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属换热站" prop="stationId">
            <el-select v-model="formData.stationId" placeholder="请选择" class="w-full" @change="handleHeatStationChange">
              <el-option v-for="item in heatStationOptions" :key="item.id" :label="item.stationName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机组热力分站区" prop="unitStationZone">
            <el-input v-model="formData.unitStationZone" placeholder="请输入机组热力分站区" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供热建筑面积" prop="heatBuildingArea">
            <el-input v-model="formData.heatBuildingArea" placeholder="请输入供热建筑面积(m²)" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供热使用面积" prop="heatUseArea">
            <el-input v-model="formData.heatUseArea" placeholder="请输入供热使用面积(m²)" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="投入使用时间" prop="investTime">
            <el-date-picker
              v-model="formData.investTime"
              type="date"
              placeholder="请选择投入使用时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnit">
            <el-select v-model="formData.ownershipUnit" placeholder="请选择" class="w-full" @change="handleOwnershipChange">
              <el-option v-for="item in enterpriseOptions" :key="item.id" :label="item.enterpriseName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="所属位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-full"
                @change="handleAreaChange"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="" prop="address" class="w-full">
            <el-input v-model="formData.address" placeholder="输入详细地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getAllEnterpriseList, getAllHeatStationList, saveUnit, updateUnit, getUnitDetail } from '@/api/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import moment from 'moment';
import { UNIT_TYPE_OPTIONS } from '@/constants/heating';
import { UNIT_TYPE_MAP } from '../../../../../../constants/heating';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增机组',
    edit: '编辑机组',
    view: '机组详情'
  };
  return titles[props.mode] || '机组信息';
});

// 企业选项
const enterpriseOptions = ref([]);
// 换热站选项
const heatStationOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  unitName: '',
  unitCode: '',
  unitType: '',
  unitTypeName: '',
  collectCode: '',
  stationId: '',
  stationName: '',
  unitStationZone: '',
  heatBuildingArea: '',
  heatUseArea: '',
  investTime: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  longitude: '',
  latitude: '',
  address: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  unitName: [{ required: true, message: '请输入机组名称', trigger: 'blur' }],
  unitCode: [{ required: true, message: '请输入机组编号', trigger: 'blur' }],
  unitType: [{ required: true, message: '请选择机组类型', trigger: 'change' }],
  stationId: [{ required: true, message: '请选择所属换热站', trigger: 'change' }],
  heatBuildingArea: [{ required: true, message: '请输入供热建筑面积', trigger: 'blur' }],
  ownershipUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  investTime: [{ required: true, message: '请选择投入使用时间', trigger: 'change' }]
};

// 获取企业列表
const fetchEnterpriseOptions = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.code === 200) {
      enterpriseOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取企业列表失败:', error);
    ElMessage.error('获取企业列表失败');
  }
};

// 获取换热站列表
const fetchHeatStationOptions = async () => {
  try {
    const res = await getAllHeatStationList();
    if (res && res.code === 200) {
      heatStationOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取换热站列表失败:', error);
    ElMessage.error('获取换热站列表失败');
  }
};

// 权属单位变更处理
const handleOwnershipChange = (value) => {
  if (value) {
    const selected = enterpriseOptions.value.find(item => item.id === value);
    if (selected) {
      formData.ownershipUnitName = selected.enterpriseName;
    }
  }
};

// 换热站变更处理
const handleHeatStationChange = (value) => {
  if (value) {
    const selected = heatStationOptions.value.find(item => item.id === value);
    if (selected) {
      formData.stationName = selected.stationName;
    }
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });

    if(newVal.unitType){
      const unitType = Number(newVal.unitType);
      formData.unitTypeName = UNIT_TYPE_MAP[unitType] || '';
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单
    resetForm();
  }
}, { immediate: true, deep: true });

watch(() => formData.unitType, (newVal) => {
  if(newVal){
    formData.unitTypeName = UNIT_TYPE_MAP[newVal] || '';
  }
});

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude || 0;
    formData.latitude = params.latitude || 0;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    submitData.unitTypeName = UNIT_TYPE_MAP[submitData.unitType] || '';

    // 确保经纬度是数字类型
    if (submitData.longitude) {
      submitData.longitude = Number(submitData.longitude);
    }
    if (submitData.latitude) {
      submitData.latitude = Number(submitData.latitude);
    }
    
    // 处理投入使用时间格式转换为 yyyy-MM-dd HH:mm:ss
    if (submitData.investTime) {
      submitData.investTime = moment(submitData.investTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveUnit(submitData);
    } else if (props.mode === 'edit') {
      res = await updateUnit(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时初始化
onMounted(async () => {
  await fetchEnterpriseOptions();
  await fetchHeatStationOptions();
});
</script>

<style scoped>
.unit-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 