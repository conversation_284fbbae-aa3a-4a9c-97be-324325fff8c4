<template>
  <el-dialog
    v-model="dialogVisible"
    title="桥梁安全评分详情"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="safety-score-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <!-- 基本信息 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">桥梁名称：</span>
              <span class="value">{{ scoreData.bridgeName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">评分时间：</span>
              <span class="value">{{ formatScoreTime(scoreData.scoreTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">风险等级：</span>
              <span 
                class="value risk-level" 
                :style="{ color: getRiskLevelColor(scoreData.riskLevel) }"
              >
                {{ getRiskLevelName(scoreData.riskLevel) }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 评分明细 -->
      <div class="score-section">
        <el-row :gutter="20">
          <!-- 雷达图 -->
          <el-col :span="12">
            <div class="chart-container">
              <h3 class="section-title">评分明细</h3>
              <div 
                ref="radarChart" 
                class="radar-chart"
                style="width: 100%; height: 350px;"
              ></div>
            </div>
          </el-col>
          
          <!-- 安全评分趋势 -->
          <el-col :span="12">
            <div class="chart-container">
              <h3 class="section-title">安全评分趋势</h3>
              <div class="time-selector">
                <span class="label">时间：</span>
                <el-select v-model="selectedTime" @change="handleTimeChange" class="time-select">
                  <el-option 
                    v-for="item in timeOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                  />
                </el-select>
              </div>
              <div 
                ref="trendChart" 
                class="trend-chart"
                style="width: 100%; height: 300px;"
              ></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import moment from 'moment'
import { getBridgeSafetyScoreDetail } from '@/api/bridge'
import { BRIDGE_RISK_LEVEL_MAP, BRIDGE_RISK_LEVEL_COLOR_MAP } from '@/constants/bridge'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  scoreId: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:visible'])

// 响应式数据
const loading = ref(false)
const scoreData = ref({})
const radarChart = ref(null)
const trendChart = ref(null)
const selectedTime = ref('最近一周')

// 图表实例
let radarChartInstance = null
let trendChartInstance = null

// 时间选项
const timeOptions = [
  { label: '最近一周', value: '最近一周' },
  { label: '最近一月', value: '最近一月' },
  { label: '最近三月', value: '最近三月' }
]

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 获取风险等级名称
const getRiskLevelName = (riskLevel) => {
  return BRIDGE_RISK_LEVEL_MAP[riskLevel] || '--'
}

// 获取风险等级颜色
const getRiskLevelColor = (riskLevel) => {
  return BRIDGE_RISK_LEVEL_COLOR_MAP[riskLevel] || '#333'
}

// 格式化评分时间
const formatScoreTime = (scoreTime) => {
  if (!scoreTime) return '--'
  
  if (typeof scoreTime === 'string') {
    return moment(scoreTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  if (scoreTime.year !== undefined) {
    // 处理Java Date对象格式
    const year = scoreTime.year + 1900
    const month = scoreTime.month
    const date = scoreTime.date
    const hours = scoreTime.hours || 0
    const minutes = scoreTime.minutes || 0
    const seconds = scoreTime.seconds || 0
    
    return moment({
      year, month, date, hours, minutes, seconds
    }).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return '--'
}

// 获取详情数据
const fetchScoreDetail = async () => {
  if (!props.scoreId) return
  
  loading.value = true
  try {
    const res = await getBridgeSafetyScoreDetail(props.scoreId)
    if (res && res.code === 200) {
      scoreData.value = res.data || {}
      
      // 等待DOM更新后初始化图表
      await nextTick()
      initRadarChart()
      initTrendChart()
    } else {
      ElMessage.error('获取评分详情失败')
    }
  } catch (error) {
    console.error('获取评分详情失败:', error)
    ElMessage.error('获取评分详情失败')
  } finally {
    loading.value = false
  }
}

// 初始化雷达图
const initRadarChart = () => {
  if (!radarChart.value) return
  
  // 销毁之前的图表实例
  if (radarChartInstance) {
    radarChartInstance.dispose()
  }
  
  radarChartInstance = echarts.init(radarChart.value)
  
  const option = {
    title: {
      text: `得分: ${scoreData.value.totalScore || 0}`,
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    radar: {
      center: ['50%', '55%'],
      radius: '70%',
      indicator: [
        { name: '设备运行分', max: 100 },
        { name: '环境监测分', max: 100 },
        { name: '隐患事件分', max: 100 },
        { name: '位移索力分', max: 100 },
        { name: '结构部件分', max: 100 }
      ],
      splitArea: {
        areaStyle: {
          color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.4)', 
                  'rgba(114, 172, 209, 0.6)', 'rgba(114, 172, 209, 0.8)', 
                  'rgba(114, 172, 209, 1.0)']
        }
      }
    },
    series: [{
      type: 'radar',
      data: [{
        value: [
          scoreData.value.deviceScore || 0,
          scoreData.value.environmentScore || 0,
          scoreData.value.hiddenScore || 0,
          scoreData.value.positionScore || 0,
          scoreData.value.structureScore || 0
        ],
        areaStyle: {
          color: 'rgba(70, 130, 180, 0.4)'
        },
        lineStyle: {
          color: '#4682B4',
          width: 2
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4682B4'
        }
      }]
    }]
  }
  
  radarChartInstance.setOption(option)
}

// 初始化趋势图
const initTrendChart = () => {
  if (!trendChart.value) return
  
  // 销毁之前的图表实例
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  
  trendChartInstance = echarts.init(trendChart.value)
  
  // 解析趋势数据
  let trendData = []
  try {
    if (scoreData.value.trendJson) {
      trendData = JSON.parse(scoreData.value.trendJson)
    }
  } catch (error) {
    console.error('解析趋势数据失败:', error)
  }
  
  const xData = trendData.map(item => moment(item.assessTime).format('MM-DD HH:mm'))
  const yData = trendData.map(item => parseFloat(item.assessScore))
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const param = params[0]
        return `时间: ${param.name}<br/>得分: ${param.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      max: 100,
      min: 0
    },
    series: [{
      data: yData,
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#1890ff',
        width: 3
      },
      itemStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(24, 144, 255, 0.4)' },
            { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
          ]
        }
      }
    }]
  }
  
  trendChartInstance.setOption(option)
}

// 处理时间选择变化
const handleTimeChange = (value) => {
  selectedTime.value = value
  // 这里可以根据选择的时间重新获取趋势数据
  // 暂时使用现有数据重新渲染
  initTrendChart()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  
  // 销毁图表实例
  if (radarChartInstance) {
    radarChartInstance.dispose()
    radarChartInstance = null
  }
  if (trendChartInstance) {
    trendChartInstance.dispose()
    trendChartInstance = null
  }
  
  // 重置数据
  scoreData.value = {}
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.scoreId) {
    fetchScoreDetail()
  }
})

// 监听窗口大小变化，重新调整图表大小
window.addEventListener('resize', () => {
  if (radarChartInstance) {
    radarChartInstance.resize()
  }
  if (trendChartInstance) {
    trendChartInstance.resize()
  }
})
</script>

<style scoped>
.safety-score-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.dialog-content {
  min-height: 500px;
}

.info-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #fafafa;
  border-radius: 4px;
}

.score-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #1890ff;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.info-item .label {
  font-weight: 500;
  color: #666;
  min-width: 100px;
}

.info-item .value {
  color: #333;
  flex: 1;
}

.info-item .risk-level {
  font-weight: 500;
}

.chart-container {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 20px;
  height: 450px;
}

.time-selector {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.time-selector .label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
}

.time-select {
  width: 120px;
}

.radar-chart,
.trend-chart {
  border-radius: 4px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}
</style> 