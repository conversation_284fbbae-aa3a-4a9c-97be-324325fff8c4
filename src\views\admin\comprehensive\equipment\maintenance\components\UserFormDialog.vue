<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="user-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input v-model="formData.userName" placeholder="请输入姓名" />
      </el-form-item>

      <el-form-item label="联系电话" prop="userPhone">
        <el-input v-model="formData.userPhone" placeholder="请输入联系电话" />
      </el-form-item>

      <el-form-item label="职务角色" prop="userRole">
        <el-input v-model="formData.userRole" placeholder="请输入职务角色" />
      </el-form-item>

      <el-form-item label="所属单位" prop="belongUnit">
        <el-tree-select
          v-model="formData.belongUnit"
          :data="deptOptions"
          :render-after-expand="false"
          :props="{
            value: 'id',
            label: 'name',
            children: 'children'
          }"
          placeholder="请选择所属单位"
          class="w-full"
          @change="handleDeptChange"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveDutyUser,
  updateDutyUser
} from '@/api/comprehensive';
import { getDeptTree } from '@/api/system';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
    validator: (value) => ['add', 'edit'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增值班人员' : '编辑值班人员';
});

// 部门选项
const deptOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  userName: '',
  userPhone: '',
  userRole: '',
  belongUnit: '',
  belongUnitName: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  userName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  userPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  userRole: [
    { required: true, message: '请输入职务角色', trigger: 'blur' }
  ],
  belongUnit: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 处理部门变化
const handleDeptChange = (value) => {
  const findDeptName = (depts, id) => {
    for (const dept of depts) {
      if (dept.id === id) {
        return dept.name;
      }
      if (dept.children && dept.children.length > 0) {
        const found = findDeptName(dept.children, id);
        if (found) return found;
      }
    }
    return '';
  };
  
  formData.belongUnitName = findDeptName(deptOptions.value, value);
};

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree();
    if (res && res.data) {
      deptOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取部门树失败', error);
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveDutyUser(submitData);
    } else if (props.mode === 'edit') {
      res = await updateDutyUser(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree();
});
</script>

<style scoped>
.user-form-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 