<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="drainage-flood-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="易涝点编码" prop="pointCode">
            <el-input v-model="formData.pointCode" placeholder="请输入易涝点编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="易涝点名称" prop="pointName">
            <el-input v-model="formData.pointName" placeholder="请输入易涝点名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="易涝点风险" prop="riskLevel">
            <el-select v-model="formData.riskLevel" placeholder="请选择" class="w-full">
              <el-option v-for="item in floodRiskOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最长积水时间(h)" prop="maxHydropsTime">
            <el-input-number 
              v-model="formData.maxHydropsTime" 
              :min="0" 
              :precision="2" 
              class="w-full" 
              placeholder="请输入最长积水时间" 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大积水面积(m²)" prop="maxHydropsArea">
            <el-input-number 
              v-model="formData.maxHydropsArea" 
              :min="0" 
              :precision="2" 
              class="w-full" 
              placeholder="请输入最大积水面积" 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="积水原因" prop="hydropsReason">
            <el-input
              v-model="formData.hydropsReason"
              type="textarea"
              :rows="3"
              placeholder="请输入积水原因"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="易涝点产生时间" prop="happenTime">
            <el-date-picker
              v-model="formData.happenTime"
              type="date"
              placeholder="请选择易涝点产生时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="contactUser">
            <el-input v-model="formData.contactUser" placeholder="请输入负责人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置" prop="location">
            <div class="flex items-center">
              <el-cascader
                v-model="areaValues"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="w-50"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="请输入详细地址" class="ml-2" style="width: calc(100% - 250px);" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>  

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标" prop="longitude" >
            <div class="flex items-center" >
              <el-input v-model="formData.longitude" placeholder="经度" style="width: 180px;" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="ml-2" style="width: 180px;" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="整改状态" prop="rectificationStatus">
            <el-select v-model="formData.rectificationStatus" placeholder="请选择" class="w-full" @change="handleRectificationStatusChange">
              <el-option v-for="item in rectificationStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 整改状态为"已整改"时显示的字段 -->
      <div v-if="formData.rectificationStatus === 3001901" class="rectification-section">
        <el-divider>
          <el-tag type="success">已整改状态信息</el-tag>
        </el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="整改时间" prop="rectificationTime">
              <el-date-picker
                v-model="formData.rectificationTime"
                type="date"
                placeholder="请选择整改时间"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改人" prop="rectificationUser">
              <el-input v-model="formData.rectificationUser" placeholder="请输入整改人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="整改措施" prop="rectificationMeasure">
              <el-input
                v-model="formData.rectificationMeasure"
                type="textarea"
                :rows="3"
                placeholder="请输入整改措施"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="附件">
              <template v-if="mode !== 'view'">
                <el-upload
                  class="upload-demo"
                  :http-request="handleFileUpload"
                  :file-list="fileList"
                  :on-error="handleUploadError"
                >
                  <el-button type="primary">上传附件</el-button>
                </el-upload>
              </template>
              <template v-else>
                <el-link
                  v-if="fileList.length > 0"
                  :href="fileList[0].url"
                  target="_blank"
                  type="primary"
                >
                  {{ fileList[0].name }}
                </el-link>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { saveFloodPoint, updateFloodPoint, getFloodPointDetail } from '@/api/drainage';
import { FLOOD_RISK_OPTIONS, RECTIFICATION_STATUS_OPTIONS } from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from '@/hooks/gishooks';
import bus from '@/utils/mitt';
import moment from 'moment';
import { uploadFile } from '@/api/upload';

// 过滤掉选项中的"全部"选项
const floodRiskOptions = FLOOD_RISK_OPTIONS.filter(item => item.value !== '');
const rectificationStatusOptions = RECTIFICATION_STATUS_OPTIONS.filter(item => item.value !== '');

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增易涝点',
    edit: '编辑易涝点',
    view: '易涝点详情'
  };
  return titles[props.mode] || '易涝点信息';
});

// 行政区划选项和选中值
const areaOptions = ref(AREA_OPTIONS);
const areaValues = ref([]);

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  pointCode: '',
  pointName: '',
  riskLevel: '',
  riskLevelName: '',
  roadName: '',
  maxHydropsTime: '',
  maxHydropsArea: '',
  hydropsReason: '',
  happenTime: null,
  contactUser: '',
  contactInfo: '',
  address: '',
  longitude: '',
  latitude: '',
  rectificationStatus: '',
  rectificationStatusName: '',
  rectificationTime: null,
  rectificationUser: '',
  rectificationMeasure: '',
  attachmentUrls: '',
  city: '',
  county: '371728', // 默认东明县
  countyName: '东明县',
  town: '',
  townName: '',
  remarks: ''
});

// 表单验证规则
const formRules = {
  pointCode: [{ required: true, message: '请输入易涝点编码', trigger: 'blur' }],
  pointName: [{ required: true, message: '请输入易涝点名称', trigger: 'blur' }],
  riskLevel: [{ required: true, message: '请选择易涝点风险', trigger: 'change' }],
  roadName: [{ required: true, message: '请输入所在道路', trigger: 'blur' }],
  maxHydropsTime: [{ required: true, message: '请输入最长积水时间', trigger: 'blur' }],
  maxHydropsArea: [{ required: true, message: '请输入最大积水面积', trigger: 'blur' }],
  happenTime: [{ required: true, message: '请选择易涝点产生时间', trigger: 'change' }],
  longitude: [{ required: true, message: '请选择易涝点位置', trigger: 'change' }],
  rectificationStatus: [{ required: true, message: '请选择整改状态', trigger: 'change' }],
  rectificationTime: [{ 
    required: true, 
    message: '请选择整改时间', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (formData.rectificationStatus === 3001901 && !value) {
        callback(new Error('请选择整改时间'));
      } else {
        callback();
      }
    }
  }],
  rectificationUser: [{ 
    required: true, 
    message: '请输入整改人', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (formData.rectificationStatus === 3001901 && !value) {
        callback(new Error('请输入整改人'));
      } else {
        callback();
      }
    }
  }],
  rectificationMeasure: [{ 
    required: true, 
    message: '请输入整改措施', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (formData.rectificationStatus === 3001901 && !value) {
        callback(new Error('请输入整改措施'));
      } else {
        callback();
      }
    }
  }]
};

// 处理整改状态变化
const handleRectificationStatusChange = (value) => {
  if (value === 3001901) {
    // 已整改状态，需要校验整改相关字段
    formData.rectificationStatusName = '已整改';
  } else {
    // 未整改状态，清空整改相关字段
    formData.rectificationStatusName = '未整改';
    formData.rectificationTime = null;
    formData.rectificationUser = '';
    formData.rectificationMeasure = '';
    formData.attachmentUrls = '';
    fileList.value = [];
  }
};

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  
  // 保留默认值
  formData.county = '371728';
  formData.countyName = '东明县';
  areaValues.value = [];
  fileList.value = [];
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 风险等级
  const selectedRiskLevel = floodRiskOptions.find(item => item.value === formData.riskLevel);
  if (selectedRiskLevel) {
    formData.riskLevelName = selectedRiskLevel.label;
  }
  
  // 整改状态
  const selectedRectificationStatus = rectificationStatusOptions.find(item => item.value === formData.rectificationStatus);
  if (selectedRectificationStatus) {
    formData.rectificationStatusName = selectedRectificationStatus.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理日期格式
    if (newVal.happenTime) {
      if (typeof newVal.happenTime === 'object' && newVal.happenTime.time) {
        formData.happenTime = new Date(newVal.happenTime.time);
      } else if (typeof newVal.happenTime === 'string') {
        formData.happenTime = new Date(newVal.happenTime);
      }
    }
    
    // 处理整改时间
    if (newVal.rectificationTime) {
      if (typeof newVal.rectificationTime === 'object' && newVal.rectificationTime.time) {
        formData.rectificationTime = new Date(newVal.rectificationTime.time);
      } else if (typeof newVal.rectificationTime === 'string') {
        formData.rectificationTime = new Date(newVal.rectificationTime);
      }
    }
    
    // 处理area级联选择器
    if (newVal.town) {
      // 在实际情况中，你可能需要反向查找完整的路径
      // 这里简化处理，只保存最终值
      areaValues.value = [newVal.town];
    }
    
    // 处理附件
    if (newVal.attachmentUrls) {
      fileList.value = [{ name: '附件', url: newVal.attachmentUrls }];
    } else {
      fileList.value = [];
    }
  } else if (props.mode === 'add') {
    // 新增模式清空表单，保留默认值
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听下拉框值变化，自动更新对应的名称
watch(() => formData.riskLevel, (val) => {
  if (val) {
    const selected = floodRiskOptions.find(item => item.value === val);
    if (selected) {
      formData.riskLevelName = selected.label;
    }
  }
});

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true; // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation);
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation);
};

// 文件上传处理
const handleFileUpload = async (options) => {
  try {
    const res = await uploadFile(options.file);
    if (res.status === 200) {
      formData.attachmentUrls = res.data.url;
      fileList.value = [{ name: options.file.name, url: res.data.url }];
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传附件失败:', error);
    ElMessage.error('上传失败');
  }
};

const handleUploadError = () => {
  ElMessage.error('上传失败');
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理日期
    if (submitData.happenTime) {
      submitData.happenTime = moment(submitData.happenTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    if (submitData.rectificationTime) {
      submitData.rectificationTime = moment(submitData.rectificationTime).format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 设置枚举值对应的名称
    updateNamesByValues();
    
    // 提交数据
    let res;
    if (props.mode === 'add') {
      res = await saveFloodPoint(submitData);
    } else if (props.mode === 'edit') {
      res = await updateFloodPoint(submitData);
    }
    
    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里添加其他初始化操作
});
</script>

<style scoped>
.drainage-flood-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.w-70 {
  width: 70%;
}

.w-25 {
  width: 25%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.position-container {
  display: flex;
  align-items: center;
}

.input-with-unit {
  display: flex;
  align-items: center;
}

.unit {
  margin-left: 8px;
  color: #606266;
}

.w-90 {
  width: 90%;
}

.w-50 {
  width: 50%;
}

.rectification-section {
  border: 1px dashed #f56c6c;
  padding: 0 20px 10px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 0 5px rgba(245, 108, 108, 0.2);
}

:deep(.el-divider__text) {
  background-color: transparent;
  padding: 0 20px;
}
</style> 