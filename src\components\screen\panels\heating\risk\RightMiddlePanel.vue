<template>
  <PanelBox title="隐患类型统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- Loading 效果 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!loading && chartData.length === 0" class="no-data-wrapper">
        <NoData />
      </div>

      <!-- 有数据时显示图表 -->
      <div v-else class="content-wrapper">
        <div class="chart-container">
          <PieChart3D :data="chartData" height="220px" :internal-diameter-ratio="0.6" />
          <!-- <div class="pie-bg"></div> -->
        </div>
        <div class="legend-container">
          <div class="legend-item" v-for="(item, index) in chartData" :key="index">
            <div class="legend-color" :style="{ backgroundColor: item.itemStyle.color }"></div>
            <div class="legend-text">
              <span class="legend-name">{{ item.name }}</span>
              <span class="legend-name">{{ item.value }}</span>
              <span class="legend-value">{{ item.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import PieChart3D from '@/components/screen/common/PieChart3D.vue'
import NoData from '@/components/common/NoData.vue'
import { getHiddenDangerTypeStatistics } from '@/api/heating'

// 加载状态
const loading = ref(false)

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到 dayIndex
const getDayIndex = (timeRange) => {
  switch (timeRange) {
    case 'week':
      return 7
    case 'month':
      return 30
    case 'year':
      return 365
    default:
      return 7
  }
}

// 预定义颜色配置
const colorPalette = [
  '#4D66F3', '#38C5FF', '#56DAD5', '#40E6C2', '#36F097',
  '#70FF8B', '#95FF76', '#E8FF54', '#FFD049', '#FF9D4D',
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
]

// 隐患类型统计数据
const riskTypeData = ref([])

// 当前选择的图表数据
const currentRiskTypeData = computed(() => {
  return riskTypeData.value
})

// 为ECharts 3D饼图准备数据
const chartData = computed(() => {
  return currentRiskTypeData.value.map(item => ({
    name: item.name,
    value: item.value,
    percentage: item.percentage,
    itemStyle: {
      color: item.color
    }
  }))
})

// 从接口获取隐患类型统计数据
const fetchHiddenDangerTypeData = async (timeRange) => {
  try {
    loading.value = true
    const dayIndex = getDayIndex(timeRange)
    const response = await getHiddenDangerTypeStatistics(dayIndex)

    if (response.code === 200 && response.data && Array.isArray(response.data)) {
      // 转换接口数据格式以匹配图表显示
      riskTypeData.value = response.data.map((item, index) => ({
        name: item.dangerTypeName || `类型${item.dangerType}`,
        value: item.count || 0,
        percentage: item.rate || 0,
        color: colorPalette[index % colorPalette.length]
      }))
    } else {
      riskTypeData.value = []
    }
  } catch (error) {
    console.error('获取隐患类型统计数据失败:', error)
    riskTypeData.value = []
  } finally {
    loading.value = false
  }
}

// 处理时间变更
const handleTimeChange = () => {
  console.log('时间范围变更为:', timeRange.value)
  fetchHiddenDangerTypeData(timeRange.value)
}

onMounted(() => {
  fetchHiddenDangerTypeData(timeRange.value) // 初始化数据
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
}

.com-select {
  margin-right: 20px;
}

.content-wrapper {
  display: flex;
  height: 100%;
  width: 100%;
  gap: 10px;
}

.chart-container {
  position: relative;
  width: 50%;
  height: 220px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pie-bg {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 238px;
  height: 119px;
  background-image: url('@/assets/images/screen/heating/pie_bg.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center bottom;
  z-index: 1;
  pointer-events: none;
}

.legend-container {
  display: flex;
  flex-direction: column;
  width: 50%;
  padding-left: 3rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 20px;
  margin-bottom: 2px;
}

.legend-color {
  width: 8px;
  height: 8px;
  flex-shrink: 0;
}

.legend-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.legend-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.legend-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .legend-item {
    height: 18px;
    margin-bottom: 1px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .legend-name {
    font-size: 11px;
  }
  
  .legend-value {
    font-size: 13px;
  }
  
  .legend-item {
    height: 16px;
    margin-bottom: 1px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .legend-name {
    font-size: 10px;
  }
  
  .legend-value {
    font-size: 12px;
  }
  
  .legend-item {
    height: 15px;
    margin-bottom: 1px;
  }
}

/* Loading 效果样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #3AA1FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-text {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* NoData样式 */
.no-data-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}
</style> 