<template>
  <div class="heating-device-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="传感器信息" name="sensor">
        <SensorInfo />
      </el-tab-pane>
      <!-- <el-tab-pane label="视频监控" name="video">
        <VideoSurveillance />
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SensorInfo from './components/SensorInfo.vue';
import VideoSurveillance from './components/VideoSurveillance.vue';

// 当前激活的选项卡
const activeTab = ref('sensor');
</script>

<style scoped>
.heating-device-container {
  padding: 20px;
  background-color: white;
  height: 100%;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__active-bar) {
  background-color: #0277FD;
}

:deep(.el-tabs__item.is-active) {
  color: #0277FD;
}
</style> 