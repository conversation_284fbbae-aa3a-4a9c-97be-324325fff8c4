<template>
  <PanelBox title="事件等级统计" class="left-middle-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item" v-for="(item, index) in legendItems" :key="index">
          <div class="legend-color" :style="{ background: item.color }"></div>
          <div class="legend-text">{{ item.name }}</div>
        </div>
      </div>
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'
import moment from 'moment'
import { getEmergencyEventLevelStatisticsScreen } from '@/api/comprehensive'

// 综合态势总览左中面板组件

// 管线类型选项
const pipeTypeOptions = [
  { value: 'week', label: '近一周' },
  { value: 'month', label: '近一月' },
  { value: 'year', label: '近一年' }
]

// 默认选择雨水管线
const selectedPipeType = ref('week')

// 图表DOM引用
const chartRef = ref(null)
// 图表实例
let chartInstance = null

// 图例数据
const legendItems = reactive([
  { name: '特别重大', color: '#DE6970' },
  { name: '重大', color: '#FE9150' },
  { name: '较大', color: '#D8F115' },
  { name: '一般', color: '#00E1B9' }
])

// 模拟数据
const mockData = reactive({
  week: {
    xAxisData: ['燃气', '排水', '供热', '桥梁'],
    series: [
      {
        name: '一般',
        data: [120, 150, 170, 35],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#00E1B9' },
            { offset: 1, color: 'rgba(0,225,185,0.01)' }
          ]
        }
      },
      {
        name: '较大',
        data: [60, 120, 30, 125],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#D8F115' },
            { offset: 1, color: 'rgba(216,241,21,0.01)' }
          ]
        }
      },
      {
        name: '重大',
        data: [95, 15, 40, 95],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#FE9150' },
            { offset: 1, color: 'rgba(254,145,80,0.01)' }
          ]
        }
      },
      {
        name: '特别重大',
        data: [35, 48, 75, 25],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#DE6970' },
            { offset: 1, color: 'rgba(222,105,112,0.01)' }
          ]
        }
      }
    ]
  },
  month: {
    xAxisData: ['燃气', '排水', '供热', '桥梁'],
    series: [
      {
        name: '一般',
        data: [95, 130, 150, 40],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#00E1B9' },
            { offset: 1, color: 'rgba(0,225,185,0.01)' }
          ]
        }
      },
      {
        name: '较大',
        data: [50, 90, 35, 110],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#D8F115' },
            { offset: 1, color: 'rgba(216,241,21,0.01)' }
          ]
        }
      },
      {
        name: '重大',
        data: [75, 25, 50, 85],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#FE9150' },
            { offset: 1, color: 'rgba(254,145,80,0.01)' }
          ]
        }
      },
      {
        name: '特别重大',
        data: [30, 40, 65, 35],
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#DE6970' },
            { offset: 1, color: 'rgba(222,105,112,0.01)' }
          ]
        }
      }
    ]
  }
})

// 处理管线类型变化
const handlePipeTypeChange = () => {
  console.log(`切换到${selectedPipeType.value}`)
  updateChart()
}

// 初始化图表
const initChart = async () => {
  await nextTick()
  if (!chartRef.value) {
    console.error('图表DOM引用不存在')
    return
  }

  console.log('初始化图表，容器大小:', chartRef.value.offsetWidth, chartRef.value.offsetHeight)

  try {
    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)

    // 设置图表配置项
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleResize)

    console.log('图表初始化完成')
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.error('图表实例不存在，无法更新')
    return
  }

  try {
    const currentData = mockData[selectedPipeType.value]
    const isDataEmpty = !currentData || currentData.series.flatMap(s => s.data).reduce((sum, val) => sum + (val || 0), 0) === 0;

    // 构建图表配置
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 16, 33, 0.8)',
        borderColor: 'rgba(0, 135, 255, 0.3)',
        borderWidth: 1,
        padding: [10, 15],
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12
        },
        confine: true,
        enterable: true,
      },
      grid: {
        top: '16%',
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentData.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#5F5F60'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        name: '单位（公里）',
        nameTextStyle: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14,
          padding: [0, -10, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14
        },
        min: isDataEmpty ? 0 : null,
        max: isDataEmpty ? 10 : null
      },
      series: currentData.series.map((item) => {
        return {
          name: item.name,
          type: 'bar',
          barWidth: '10px',
          barGap: '30%',
          emphasis: {
            focus: 'series'
          },
          data: item.data,
          itemStyle: {
            color: item.color
          }
        }
      })
    }

    // 设置图表配置
    console.log('更新图表配置')
    chartInstance.setOption(option)
    chartInstance.resize() // 强制重新计算大小
    console.log('图表配置已更新')
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}

// 计算时间范围
const getTimeRange = (type) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime = ''

  switch (type) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }

  return { startTime, endTime }
}

// 从后端获取数据
const fetchData = async (pipeType) => {
  try {
    const { startTime, endTime } = getTimeRange(pipeType)
    const response = await getEmergencyEventLevelStatisticsScreen({ startTime, endTime })

    if (response.code === 200 && response.data) {
      // 转换接口数据为图表数据格式
      const chartData = transformApiDataToChartData(response.data)

      // 更新对应时间段的数据
      mockData[pipeType] = chartData

      // 如果当前选择的时间段就是刚获取的数据，立即更新图表
      if (selectedPipeType.value === pipeType) {
        updateChart()
      }
    }
    console.log(`获取${pipeType}数据完成`)
  } catch (error) {
    console.error('获取数据失败:', error)
    // 发生错误时使用模拟数据
    console.log(`使用${pipeType}模拟数据`)
  }
}

// 转换API数据为图表数据格式
const transformApiDataToChartData = (apiData) => {
  // 构建x轴数据（专项名称）
  const xAxisData = apiData.map(item => item.relatedBusinessName)

  // 构建系列数据
  const series = [
    {
      name: '一般',
      data: apiData.map(item => item.minorCount || 0),
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#00E1B9' },
          { offset: 1, color: 'rgba(0,225,185,0.01)' }
        ]
      }
    },
    {
      name: '较大',
      data: apiData.map(item => item.seriousCount || 0),
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#D8F115' },
          { offset: 1, color: 'rgba(216,241,21,0.01)' }
        ]
      }
    },
    {
      name: '重大',
      data: apiData.map(item => item.criticalCount || 0),
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FE9150' },
          { offset: 1, color: 'rgba(254,145,80,0.01)' }
        ]
      }
    },
    {
      name: '特别重大',
      data: apiData.map(item => item.catastrophicCount || 0),
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#DE6970' },
          { offset: 1, color: 'rgba(222,105,112,0.01)' }
        ]
      }
    }
  ]

  return { xAxisData, series }
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchData(newValue)
})

// 初始化数据
const initData = async () => {
  try {
    // 并行获取所有时间段的数据
    await Promise.all([
      fetchData('week'),
      fetchData('month'),
      fetchData('year')
    ])
    console.log('所有时间段数据获取完成')
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 组件挂载时初始化图表和数据
onMounted(async () => {
  console.log('组件已挂载')
  await initChart()
  // 初始化数据
  await initData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  console.log('组件已卸载')
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.left-middle-panel {
  height: 310px;
}

.panel-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 35, 80, 0.5);
  border: 1px solid rgba(0, 242, 241, 0.2);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 242, 241, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  background: linear-gradient(90deg,
      rgba(0, 242, 241, 0.05) 0%,
      rgba(0, 242, 241, 0.2) 50%,
      rgba(0, 242, 241, 0.05) 100%);
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 242, 241, 0.2);
}

.panel-title {
  color: #00f2f1;
  font-size: 16px;
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: 'PingFangSC, PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.chart-container {
  flex: 1;
  min-height: 200px;
  width: 100%;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-middle-panel {
    height: 310px;
  }

  .chart-container {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .left-middle-panel {
    height: 310px;
  }

  .chart-container {
    min-height: 180px;
  }

  .chart-legend {
    gap: 10px;
  }

  .legend-text {
    font-size: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .left-middle-panel {
    height: 310px;
  }

  .chart-container {
    min-height: 220px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .left-middle-panel {
    height: 310px;
  }

  .panel-content {
    padding: 15px;
  }

  .chart-container {
    min-height: 220px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .left-middle-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .chart-container {
    min-height: 200px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 939px) {
  .left-middle-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
    gap: 5px;
  }

  .chart-legend {
    gap: 10px;
  }

  .legend-item {
    gap: 3px;
  }

  .legend-text {
    font-size: 12px;
  }

  .chart-container {
    min-height: 180px;
  }
}
</style>