<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="traffic-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称">
            <el-input v-model="formData.deviceName" placeholder="设备名称" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="过车时间">
            <el-input v-model="passTimeFormatted" placeholder="过车时间" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="车牌号码">
            <el-input v-model="formData.plateNumber" placeholder="车牌号码" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车辆类型">
            <el-input v-model="formData.carType" placeholder="车辆类型" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="车速">
            <div class="flex items-center">
              <el-input-number v-model="formData.speed" :precision="1" class="w-full-unit" readonly />
              <span class="unit-label">km/h</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="轴数">
            <el-input-number v-model="formData.axleCount" :min="0" class="w-full" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="车载重量">
            <div class="flex items-center">
              <el-input-number v-model="formData.weight" :precision="2" class="w-full-unit" readonly />
              <span class="unit-label">kg</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="限制重量">
            <div class="flex items-center">
              <el-input-number v-model="formData.limitWeight" :precision="2" class="w-full-unit" readonly />
              <span class="unit-label">kg</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="超限重量">
            <div class="flex items-center">
              <el-input-number v-model="overWeight" :precision="2" class="w-full-unit" readonly />
              <span class="unit-label">kg</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="超限率">
            <div class="flex items-center">
              <el-input-number v-model="overWeightRate" :precision="2" class="w-full-unit" readonly />
              <span class="unit-label">%</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="formData.picUrls">
        <el-col :span="24">
          <el-form-item label="过车图片">
            <div class="image-container">
              <el-image
                v-for="(url, index) in imageUrls"
                :key="index"
                :src="url"
                :preview-src-list="imageUrls"
                :initial-index="index"
                fit="cover"
                class="traffic-image"
                @error="handleImageError"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'view', // 只有查看模式
    validator: (value) => ['view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return '交通荷载监测详情';
});

// 表单数据
const formData = reactive({
  id: '',
  deviceName: '',
  passTime: null,
  plateNumber: '',
  carType: '',
  speed: 0,
  axleCount: 0,
  weight: 0,
  limitWeight: 0,
  picUrls: ''
});

// 格式化过车时间
const passTimeFormatted = computed(() => {
  if (!formData.passTime) return '';
  
  // 如果是时间对象格式，转换为时间戳
  if (typeof formData.passTime === 'object' && formData.passTime.time) {
    return moment(formData.passTime.time).format('YYYY-MM-DD HH:mm:ss');
  }
  
  // 如果是时间戳或字符串
  return moment(formData.passTime).format('YYYY-MM-DD HH:mm:ss');
});

// 计算超限重量
const overWeight = computed(() => {
  const weight = formData.weight || 0;
  const limitWeight = formData.limitWeight || 0;
  return weight - limitWeight;
});

// 计算超限率
const overWeightRate = computed(() => {
  const weight = formData.weight || 0;
  const limitWeight = formData.limitWeight || 0;
  
  if (limitWeight === 0) return 0;
  
  const overWeightValue = weight - limitWeight;
  const rate = (overWeightValue / limitWeight) * 100;
  return Math.max(0, rate); // 不显示负数
});

// 图片URL列表
const imageUrls = computed(() => {
  if (!formData.picUrls) return [];
  
  try {
    // 尝试解析JSON格式的图片URL
    const urls = JSON.parse(formData.picUrls);
    return Array.isArray(urls) ? urls : [urls];
  } catch (error) {
    // 如果解析失败，可能是单个URL或逗号分隔的URL
    return formData.picUrls.split(',').filter(url => url.trim());
  }
});

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'speed' || key === 'axleCount' || key === 'weight' || key === 'limitWeight') {
      formData[key] = 0;
    } else if (key === 'passTime') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理图片加载错误
const handleImageError = (e) => {
  console.warn('图片加载失败:', e);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};
</script>

<style scoped>
.traffic-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.w-full-unit {
  width: calc(100% - 60px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 55px;
  margin-left: 5px;
  color: #666;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.traffic-image {
  width: 120px;
  height: 80px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
}

.traffic-image:hover {
  border-color: #0277FD;
}
</style> 