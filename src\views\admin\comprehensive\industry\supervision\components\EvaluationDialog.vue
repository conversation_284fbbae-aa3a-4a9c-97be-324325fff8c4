<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="evaluation-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企业名称" prop="evaluationUnit">
            <el-select 
              v-model="formData.evaluationUnit" 
              placeholder="请选择企业" 
              class="w-full"
              @change="handleEnterpriseChange"
            >
              <el-option 
                v-for="item in enterpriseOptions" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select 
              v-model="formData.relatedBusiness" 
              placeholder="请选择所属行业" 
              class="w-full"
              @change="handleBusinessChange"
            >
              <el-option 
                v-for="item in relatedBusinessOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="考核周期" prop="evaluationPeriod">
            <el-select 
              v-model="formData.evaluationPeriod" 
              placeholder="请选择考核周期" 
              class="w-full"
              @change="handlePeriodChange"
            >
              <el-option 
                v-for="item in evaluationPeriodOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="评分" prop="score">
            <el-input-number 
              v-model="formData.score" 
              :min="0" 
              :max="100" 
              :precision="1" 
              placeholder="请输入评分"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="评价描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入评价描述"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="相关附件">
            <div class="file-upload-container">
              <!-- 文件上传按钮 -->
              <div class="upload-header">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :file-list="[]"
                  :disabled="mode === 'view' || fileList.length >= 5"
                  :show-file-list="false"
                  multiple
                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                >
                  <el-button 
                    type="primary" 
                    :disabled="mode === 'view' || fileList.length >= 5"
                  >
                    选择文件
                  </el-button>
                </el-upload>
                <span class="upload-tip">请上传doc/docx/pdf/md文档，大小10MB以内</span>
              </div>
              
              <!-- 文件列表表格 -->
              <div class="file-list-table" v-if="fileList.length > 0">
                <div class="table-header">
                  <div class="column file-name">文件名</div>
                  <div class="column file-size">大小</div>
                  <div class="column file-actions">操作</div>
                </div>
                <div 
                  class="table-row" 
                  v-for="(file, index) in fileList" 
                  :key="file.uid || index"
                >
                  <div class="column file-name">
                    <div class="file-info">
                      <el-icon class="file-icon">
                        <Document v-if="isDocumentFile(file.name)" />
                        <Picture v-else-if="isImageFile(file.name)" />
                        <Files v-else />
                      </el-icon>
                      <span class="file-name-text">{{ file.name }}</span>
                    </div>
                  </div>
                  <div class="column file-size">
                    {{ formatFileSize(file.size) }}
                  </div>
                  <div class="column file-actions">
                    <div class="action-buttons">
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileRemove(file, index)"
                        v-if="mode !== 'view'"
                      >
                        删除
                      </el-button>
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileDownload(file)"
                        v-if="file.url && file.status === 'success'"
                      >
                        下载
                      </el-button>
                      <div class="upload-status" v-if="file.status === 'uploading'">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>上传中</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files, Loading } from '@element-plus/icons-vue'
import {
  saveEvaluation,
  updateEvaluation,
  EVALUATION_PERIOD_OPTIONS,
  RELATED_BUSINESS_OPTIONS
} from '@/api/comprehensive'
import { getDeptListTree } from '@/api/system'
import { uploadFile } from '@/api/upload'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const uploadRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增企业考核评价',
    edit: '编辑企业考核评价',
    view: '企业考核评价详情'
  }
  return titles[props.mode] || '企业考核评价'
})

// 下拉选项数据
const enterpriseOptions = ref([])
const evaluationPeriodOptions = ref(EVALUATION_PERIOD_OPTIONS)
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)

// 文件列表
const fileList = ref([])

// 表单数据
const formData = reactive({
  id: '',
  description: '',
  evaluationPeriod: '',
  evaluationPeriodName: '',
  evaluationUnit: '',
  evaluationUnitName: '',
  fileUrls: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  remark: '',
  score: 0
})

// 表单验证规则
const formRules = {
  evaluationUnit: [{ required: true, message: '请选择企业名称', trigger: 'change' }],
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  evaluationPeriod: [{ required: true, message: '请选择考核周期', trigger: 'change' }],
  score: [{ required: true, message: '请输入评分', trigger: 'blur' }],
  description: [{ required: true, message: '请输入评价描述', trigger: 'blur' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'score') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx', '.md']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理文件显示
    if (newVal.fileUrls) {
      const urls = newVal.fileUrls.split(',').filter(url => url.trim())
      const existingFiles = urls.map((url, index) => ({
        name: `attachment_${index + 1}${getFileExtension(url)}`,
        url: url.trim(),
        uid: Date.now() + index,
        status: 'success',
        size: 0
      }))
      fileList.value = existingFiles
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 获取文件扩展名
const getFileExtension = (url) => {
  const match = url.match(/\.[^.]*$/)
  return match ? match[0] : '.doc'
}

// 处理企业选择变化
const handleEnterpriseChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.id === value)
  if (selected) {
    formData.evaluationUnitName = selected.name
  }
}

// 处理行业选择变化
const handleBusinessChange = (value) => {
  const selected = relatedBusinessOptions.value.find(item => item.value === value)
  if (selected) {
    formData.relatedBusinessName = selected.label
  }
}

// 处理周期选择变化
const handlePeriodChange = (value) => {
  const selected = evaluationPeriodOptions.value.find(item => item.value === value)
  if (selected) {
    formData.evaluationPeriodName = selected.label
  }
}

// 处理文件变化
const handleFileChange = async (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 5) {
    ElMessage.warning('最多只能上传5个文件')
    return false
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }

  // 添加到文件列表，状态为上传中
  const fileItem = {
    name: file.name,
    size: file.size,
    uid: file.uid || Date.now(),
    status: 'uploading',
    raw: file,
    url: ''
  }
  
  fileList.value.push(fileItem)

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response && response.status === 200) {
      // 更新文件状态为成功
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value[index].url = response.data.url
        fileList.value[index].status = 'success'
      }
      
      // 更新formData中的fileUrls
      updateFileUrls()
      
      ElMessage.success('文件上传成功')
    } else {
      // 上传失败，移除文件
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value.splice(index, 1)
      }
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 上传失败，移除文件
    const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
const handleFileRemove = (file, index) => {
  fileList.value.splice(index, 1)
  updateFileUrls()
  ElMessage.success('文件已移除')
}

// 处理文件下载
const handleFileDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 更新文件URL字符串
const updateFileUrls = () => {
  nextTick(() => {
    const urls = fileList.value
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    formData.fileUrls = urls.join(',')
  })
}

// 获取企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getDeptListTree({ code: '1' })
    if (res && res.status === 200) {
      enterpriseOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取企业列表失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveEvaluation(submitData)
    } else if (props.mode === 'edit') {
      res = await updateEvaluation(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises()
})
</script>

<style scoped>
.evaluation-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

/* 文件列表表格样式 */
.file-list-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f5f7fa;
}

.column {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.file-name {
  flex: 1;
  min-width: 0;
}

.file-size {
  width: 100px;
  justify-content: center;
}

.file-actions {
  width: 140px;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
}

.upload-status .el-icon {
  font-size: 14px;
}

.upload-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 