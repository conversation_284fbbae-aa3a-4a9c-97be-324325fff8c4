<template>
  <div class="drainage-alarm-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 日期筛选区域 -->
    <div class="filter-section">
      <div class="date-filter">
        <label class="filter-label">日期：</label>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange"
          class="date-picker" />
        <el-button type="primary" :class="{ active: quickDateType === 'recent7' }" @click="setQuickDate('recent7')"
          class="quick-btn">
          近7日
        </el-button>
        <el-button type="primary" :class="{ active: quickDateType === 'recent30' }" @click="setQuickDate('recent30')"
          class="quick-btn">
          最近30天
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <!-- 报警数量统计 -->
      <div class="stats-row">
        <div class="stats-card total-card">
          <div class="card-header">
            <h3>全部报警</h3>
          </div>
          <div class="card-content">
            <div class="main-number">{{ alarmStatistics.totalAlarms || 0 }}</div>
            <div class="trend-info">
              <span class="trend-label">同比</span>
              <span class="trend-down">↓ 10%</span>
              <span class="trend-label">环比</span>
              <span class="trend-up">↑ 30%</span>
            </div>
          </div>
        </div>

        <div class="stats-grid">
          <div class="stats-card pending-confirm">
            <div class="card-header">
              <h4>待确认</h4>
            </div>
            <div class="card-content">
              <div class="number">{{ alarmStatistics.pendingConfirm || 0 }}</div>
              <div class="percentage">{{ alarmStatistics.pendingConfirmRate || '0%' }}</div>
            </div>
          </div>

          <div class="stats-card pending-handle">
            <div class="card-header">
              <h4>待处置</h4>
            </div>
            <div class="card-content">
              <div class="number">{{ alarmStatistics.pendingHandle || 0 }}</div>
              <div class="percentage">{{ alarmStatistics.pendingHandleRate || '0%' }}</div>
            </div>
          </div>

          <div class="stats-card handling">
            <div class="card-header">
              <h4>处置中</h4>
            </div>
            <div class="card-content">
              <div class="number">{{ alarmStatistics.handling || 0 }}</div>
              <div class="percentage">{{ alarmStatistics.handlingRate || '0%' }}</div>
            </div>
          </div>

          <div class="stats-card handled">
            <div class="card-header">
              <h4>已处置</h4>
            </div>
            <div class="card-content">
              <div class="number">{{ alarmStatistics.handled || 0 }}</div>
              <div class="percentage">{{ alarmStatistics.handledRate || '0%' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 处置完成率和误报率 -->
      <div class="completion-section">
        <div class="completion-card">
          <div class="completion-chart">
            <div class="chart-container">
              <div class="progress-circle" :style="{ '--progress': completionProgress }">
                <span class="progress-text">{{ disposalSituation.completionRate || '20%' }}</span>
              </div>
            </div>
            <div class="chart-info">
              <h4>处置完成率</h4>
              <p class="trend-text">环比 {{ disposalSituation.completionAnalysisTrend || '下降20%' }}</p>
            </div>
          </div>
        </div>

        <div class="completion-card">
          <div class="completion-chart">
            <div class="chart-container">
              <div class="progress-circle" :style="{ '--progress': falseAlarmProgress }">
                <span class="progress-text">{{ disposalSituation.falseAlarmRate || '20%' }}</span>
              </div>
            </div>
            <div class="chart-info">
              <h4>误报率</h4>
              <p class="trend-text">环比 {{ disposalSituation.falseAlarmAnalysisTrend || '上升20%' }}</p>
            </div>
          </div>
        </div>

        <div class="avg-time-card">
          <div class="time-display">
            <div class="time-number">{{ formatDuration(disposalSituation.avgHandlingDuration) }}</div>
            <div class="time-label">平均处置时长</div>
            <div class="time-trend">环比 {{ disposalSituation.avgHandlingDurationAnalysisTrend || '上升20%' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 报警趋势图 -->
      <div class="chart-card trend-chart">
        <div class="chart-header">
          <h3>报警趋势</h3>
          <div class="chart-legend">
            <span class="legend-item">
              <span class="legend-color level1"></span>
              一级报警
            </span>
            <span class="legend-item">
              <span class="legend-color level2"></span>
              二级报警
            </span>
            <span class="legend-item">
              <span class="legend-color level3"></span>
              三级报警
            </span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="trendChartRef" class="chart-container"></div>
        </div>
      </div>

      <!-- 报警等级统计图 -->
      <div class="chart-card level-chart">
        <div class="chart-header">
          <h3>报警等级</h3>
          <div class="chart-legend">
            <span class="legend-item">
              <span class="legend-color total"></span>
              总数
            </span>
            <span class="legend-item">
              <span class="legend-color done"></span>
              已处置
            </span>
            <span class="legend-item">
              <span class="legend-color trend-line"></span>
              占比
            </span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="levelChartRef" class="chart-container"></div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="tables-section">
      <!-- 高发报警设备表格 -->
      <div class="table-card">
        <div class="table-header">
          <h3>高发报警设备 (top10)</h3>
        </div>
        <div class="table-content">
          <el-table :data="deviceTableData" class="device-table" v-loading="deviceLoading" :header-cell-style="headerCellStyle" :row-class-name="tableRowClassName">
            <el-table-column prop="index" label="排序" width="60" align="center">
              <template #default="{ $index }">
                <span class="rank-number">{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="200" show-overflow-tooltip />
            <el-table-column prop="alarmCount" label="报警总数" width="100" align="center" />
            <el-table-column prop="handledCount" label="已处置" width="100" align="center" />
            <el-table-column label="一级报警" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge level1">{{ row.level1Count || 0 }}</span>
                  <span class="level-rate">({{ row.level1HandleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="二级报警" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge level2">{{ row.level2Count || 0 }}</span>
                  <span class="level-rate">({{ row.level2HandleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="三级报警" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge level3">{{ row.level3Count || 0 }}</span>
                  <span class="level-rate">({{ row.level3HandleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="已处置" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge handled">{{ row.handledCount || 0 }}</span>
                  <span class="level-rate">({{ row.handleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 企业报警表格 -->
      <div class="table-card">
        <div class="table-header">
          <h3>企业报警</h3>
        </div>
        <div class="table-content">
          <el-table :data="enterpriseTableData" class="enterprise-table" v-loading="enterpriseLoading" :header-cell-style="headerCellStyle" :row-class-name="tableRowClassName">
            <el-table-column prop="index" label="排序" width="60" align="center">
              <template #default="{ $index }">
                <span class="rank-number">{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="enterpriseName" label="企业名称" min-width="200" show-overflow-tooltip />
            <el-table-column prop="alarmCount" label="报警总数" width="100" align="center" />
            <el-table-column prop="handledCount" label="已处置" width="100" align="center" />
            <el-table-column label="一级报警" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge level1">{{ row.level1Count || 0 }}</span>
                  <span class="level-rate">({{ row.level1HandleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="二级报警" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge level2">{{ row.level2Count || 0 }}</span>
                  <span class="level-rate">({{ row.level2HandleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="三级报警" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge level3">{{ row.level3Count || 0 }}</span>
                  <span class="level-rate">({{ row.level3HandleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="已处置" width="120" align="center">
              <template #default="{ row }">
                <div class="alarm-level-cell">
                  <span class="level-badge handled">{{ row.handledCount || 0 }}</span>
                  <span class="level-rate">({{ row.handleRate || 0 }}%)</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getDrainAlarmStatistics,
  getDrainAlarmDisposalSituation,
  getDrainAlarmTrendStatistics,
  getDrainAlarmLevelStatistics,
  getDrainAlarmHighFrequencyDevices,
  getDrainAlarmEnterpriseStatistics
} from '@/api/drainage'

// 响应式数据
const dateRange = ref([])
const quickDateType = ref('recent7')
const loading = ref(false)
const deviceLoading = ref(false)
const enterpriseLoading = ref(false)

// 统计数据
const alarmStatistics = reactive({
  totalAlarms: 0,
  pendingConfirm: 0,
  pendingConfirmRate: '0%',
  pendingHandle: 0,
  pendingHandleRate: '0%',
  handling: 0,
  handlingRate: '0%',
  handled: 0,
  handledRate: '0%'
})

const disposalSituation = reactive({
  completionRate: '20%',
  completionAnalysisTrend: '下降20%',
  falseAlarmRate: '20%',
  falseAlarmAnalysisTrend: '上升20%',
  avgHandlingDuration: '3小时5分钟',
  avgHandlingDurationAnalysisTrend: '上升20%'
})

const deviceTableData = ref([])
const enterpriseTableData = ref([])

// 图表引用
const trendChartRef = ref(null)
const levelChartRef = ref(null)
let trendChart = null
let levelChart = null

// 计算属性
const completionProgress = computed(() => {
  const rate = parseFloat(disposalSituation.completionRate) || 20
  return Math.min(rate, 100)
})

const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 方法
const formatDuration = (duration) => {
  if (!duration) return '3小时5分钟'
  return duration
}

const setQuickDate = (type) => {
  quickDateType.value = type
  const today = moment()

  if (type === 'recent7') {
    dateRange.value = [
      today.clone().subtract(6, 'days').startOf('day').format('YYYY-MM-DD'),
      today.clone().endOf('day').format('YYYY-MM-DD')
    ]
  } else if (type === 'recent30') {
    dateRange.value = [
      today.clone().subtract(29, 'days').startOf('day').format('YYYY-MM-DD'),
      today.clone().endOf('day').format('YYYY-MM-DD')
    ]
  }

  loadAllData()
}

const handleDateChange = () => {
  quickDateType.value = ''
  loadAllData()
}

const getDateParams = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return {}
  }
  return {
    startDate: moment(dateRange.value[0]).format('YYYY-MM-DD HH:mm:ss'),
    endDate: moment(dateRange.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
}

// 加载报警统计数据
const loadAlarmStatistics = async () => {
  try {
    const params = getDateParams()
    const response = await getDrainAlarmStatistics(params)
    if (response.code === 200) {
      Object.assign(alarmStatistics, response.data)
    }
  } catch (error) {
    console.error('加载报警统计数据失败:', error)
    ElMessage.error('加载报警统计数据失败')
  }
}

// 加载处置情况数据
const loadDisposalSituation = async () => {
  try {
    const params = getDateParams()
    const response = await getDrainAlarmDisposalSituation(params)
    if (response.code === 200) {
      Object.assign(disposalSituation, response.data)
    }
  } catch (error) {
    console.error('加载处置情况数据失败:', error)
    ElMessage.error('加载处置情况数据失败')
  }
}

// 加载趋势图表数据
const loadTrendChart = async () => {
  try {
    const params = getDateParams()
    const response = await getDrainAlarmTrendStatistics(params)
    if (response.code === 200 && response.data.statistics) {
      renderTrendChart(response.data.statistics)
    }
  } catch (error) {
    console.error('加载趋势图表数据失败:', error)
    ElMessage.error('加载趋势图表数据失败')
  }
}

// 加载等级图表数据
const loadLevelChart = async () => {
  try {
    const params = getDateParams()
    const response = await getDrainAlarmLevelStatistics(params)
    if (response.code === 200 && response.data.statistics) {
      renderLevelChart(response.data.statistics)
    }
  } catch (error) {
    console.error('加载等级图表数据失败:', error)
    ElMessage.error('加载等级图表数据失败')
  }
}

// 加载设备表格数据
const loadDeviceTable = async () => {
  try {
    deviceLoading.value = true
    const params = getDateParams()
    const response = await getDrainAlarmHighFrequencyDevices(params)
    if (response.code === 200 && response.data.records) {
      deviceTableData.value = response.data.records.slice(0, 10) // 只取前10条
    }
  } catch (error) {
    console.error('加载设备表格数据失败:', error)
    ElMessage.error('加载设备表格数据失败')
  } finally {
    deviceLoading.value = false
  }
}

// 加载企业表格数据
const loadEnterpriseTable = async () => {
  try {
    enterpriseLoading.value = true
    const params = getDateParams()
    const response = await getDrainAlarmEnterpriseStatistics(1, 10, params)
    if (response.code === 200 && response.data.records) {
      enterpriseTableData.value = response.data.records
    }
  } catch (error) {
    console.error('加载企业表格数据失败:', error)
    ElMessage.error('加载企业表格数据失败')
  } finally {
    enterpriseLoading.value = false
  }
}

// 渲染趋势图表
const renderTrendChart = (data) => {
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }

  const dates = data.map(item => moment(item.date).format('MM/DD'))
  const level1Data = data.map(item => item.level1Count || 0)
  const level2Data = data.map(item => item.level2Count || 0)
  const level3Data = data.map(item => item.level3Count || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: [
      {
        name: '一级报警',
        type: 'line',
        data: level1Data,
        smooth: true,
        lineStyle: {
          color: '#ff4757'
        },
        itemStyle: {
          color: '#ff4757'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 71, 87, 0.3)' },
              { offset: 1, color: 'rgba(255, 71, 87, 0.1)' }
            ]
          }
        }
      },
      {
        name: '二级报警',
        type: 'line',
        data: level2Data,
        smooth: true,
        lineStyle: {
          color: '#ff9f43'
        },
        itemStyle: {
          color: '#ff9f43'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 159, 67, 0.3)' },
              { offset: 1, color: 'rgba(255, 159, 67, 0.1)' }
            ]
          }
        }
      },
      {
        name: '三级报警',
        type: 'line',
        data: level3Data,
        smooth: true,
        lineStyle: {
          color: '#5dade2'
        },
        itemStyle: {
          color: '#5dade2'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(93, 173, 226, 0.3)' },
              { offset: 1, color: 'rgba(93, 173, 226, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 渲染等级图表
const renderLevelChart = (data) => {
  if (!levelChart) {
    levelChart = echarts.init(levelChartRef.value)
  }

  const categories = data.map(item => item.alarmLevelName || '未知等级')
  const totalData = data.map(item => item.totalCount || 0)
  const handledData = data.map(item => item.handledCount || 0)
  const percentData = data.map(item => parseFloat(item.percent) || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: '#e6e6e6'
          }
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5'
          }
        }
      },
      {
        type: 'value',
        name: '占比(%)',
        position: 'right',
        axisLine: {
          lineStyle: {
            color: '#e6e6e6'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '总数',
        type: 'bar',
        data: totalData,
        itemStyle: {
          color: '#5dade2'
        },
        barWidth: '20%'
      },
      {
        name: '已处置',
        type: 'bar',
        data: handledData,
        itemStyle: {
          color: '#26de81'
        },
        barWidth: '20%'
      },
      {
        name: '占比',
        type: 'line',
        yAxisIndex: 1,
        data: percentData,
        lineStyle: {
          color: '#ff6b6b'
        },
        itemStyle: {
          color: '#ff6b6b'
        }
      }
    ]
  }

  levelChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }
  if (levelChartRef.value) {
    levelChart = echarts.init(levelChartRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    levelChart?.resize()
  })
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadAlarmStatistics(),
      loadDisposalSituation(),
      loadTrendChart(),
      loadLevelChart(),
      loadDeviceTable(),
      loadEnterpriseTable()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 设置默认日期为近7天
  setQuickDate('recent7')

  // 初始化图表
  await initCharts()

  // 加载数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (levelChart) {
    levelChart.dispose()
    levelChart = null
  }
  window.removeEventListener('resize', () => {
    trendChart?.resize()
    levelChart?.resize()
  })
})
</script>

<style scoped>
.drainage-alarm-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.date-picker {
  width: 300px;
}

.quick-btn {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.quick-btn.active {
  background: #409eff;
  color: white;
}

/* 统计卡片区域 */
.statistics-section {
  margin-bottom: 20px;
}

.stats-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.total-card {
  flex: 0 0 300px;
  padding: 20px;
}

.total-card .card-header h3 {
  font-size: 16px;
  color: #606266;
  margin: 0 0 15px 0;
}

.total-card .main-number {
  font-size: 48px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.trend-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.trend-label {
  color: #909399;
}

.trend-down {
  color: #67c23a;
}

.trend-up {
  color: #f56c6c;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 1;
  background: #e4e7ed;
}

.stats-grid .stats-card {
  padding: 20px;
  box-shadow: none;
  border-radius: 0;
}

.pending-confirm {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.pending-handle {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.handling {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
}

.handled {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.stats-grid .card-header h4 {
  font-size: 14px;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.stats-grid .number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stats-grid .percentage {
  font-size: 14px;
  opacity: 0.8;
}

/* 完成率区域 */
.completion-section {
  display: flex;
  gap: 20px;
}

.completion-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.completion-chart {
  display: flex;
  align-items: center;
  gap: 20px;
}

.chart-container {
  flex-shrink: 0;
}

.progress-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(#409eff 0deg, #409eff calc(var(--progress) * 3.6deg), #e4e7ed calc(var(--progress) * 3.6deg), #e4e7ed 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.progress-text {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  position: relative;
  z-index: 1;
}

.chart-info h4 {
  font-size: 16px;
  color: #303133;
  margin: 0 0 8px 0;
}

.trend-text {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.avg-time-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-display {
  text-align: center;
}

.time-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.time-label {
  font-size: 16px;
  color: #303133;
  margin-bottom: 5px;
}

.time-trend {
  font-size: 14px;
  color: #909399;
}

/* 图表区域 */
.charts-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.level1 {
  background: #ff4757;
}

.legend-color.level2 {
  background: #ff9f43;
}

.legend-color.level3 {
  background: #5dade2;
}

.legend-color.trend-line {
  background: #ff6b6b;
}

.legend-color.total {
  background: #5dade2;
}

.legend-color.done {
  background: #26de81;
}

.chart-content .chart-container {
  height: 300px;
  min-height: 250px;
}

/* 表格区域 */
.tables-section {
  display: flex;
  gap: 20px;
}

.table-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 20px 20px 0 20px;
  flex-shrink: 0;
}

.table-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.table-content {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

.rank-number {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background: #f0f2f5;
  color: #606266;
  font-weight: bold;
}

.alarm-level-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.level-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  min-width: 20px;
  text-align: center;
}

.level-badge.level1 {
  background: #ff4757;
}

.level-badge.level2 {
  background: #ff9f43;
}

.level-badge.level3 {
  background: #5dade2;
}

.level-badge.handled {
  background: #26de81;
}

.level-rate {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .charts-section {
    flex-direction: column;
  }

  .chart-content .chart-container {
    height: 280px;
  }
}

@media (max-width: 1200px) {
  .tables-section {
    flex-direction: column;
  }

  .chart-content .chart-container {
    height: 260px;
  }
}

@media (max-height: 1050px) {
  .drainage-alarm-analysis-container {
    padding: 15px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 200px);
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .chart-content .chart-container {
    height: 240px;
  }

  .completion-chart .progress-circle {
    width: 60px;
    height: 60px;
  }

  .completion-chart .progress-circle::before {
    width: 45px;
    height: 45px;
  }

  .progress-text {
    font-size: 14px;
  }
}

@media (max-height: 800px) {
  .drainage-alarm-analysis-container {
    padding: 10px;
  }

  .chart-content .chart-container {
    height: 220px;
  }

  .stats-card {
    padding: 15px;
  }

  .total-card {
    padding: 15px;
  }

  .completion-card {
    padding: 15px;
  }

  .avg-time-card {
    padding: 15px;
  }

  .table-card {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .stats-row {
    flex-direction: column;
  }

  .completion-section {
    flex-direction: column;
  }

  .date-filter {
    flex-wrap: wrap;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .chart-legend {
    flex-direction: column;
    gap: 10px;
  }

  .chart-content .chart-container {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .drainage-alarm-analysis-container {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .total-card .main-number {
    font-size: 36px;
  }

  .stats-grid .number {
    font-size: 24px;
  }

  .date-picker {
    width: 100%;
  }

  .quick-btn {
    flex: 1;
  }

  .chart-content .chart-container {
    height: 180px;
  }
}

@media (max-height: 700px) {
  .drainage-alarm-analysis-container {
    padding: 8px;
  }

  .page-header {
    margin-bottom: 15px;
  }

  .filter-section {
    padding: 15px;
    margin-bottom: 15px;
  }

  .statistics-section {
    margin-bottom: 15px;
  }

  .stats-row {
    margin-bottom: 15px;
  }

  .charts-section {
    margin-bottom: 15px;
  }

  .chart-content .chart-container {
    height: 200px;
  }

  .table-card {
    max-height: 300px;
  }

  .completion-chart .progress-circle {
    width: 50px;
    height: 50px;
  }

  .completion-chart .progress-circle::before {
    width: 38px;
    height: 38px;
  }

  .progress-text {
    font-size: 12px;
  }

  .time-number {
    font-size: 20px;
  }

  .time-label {
    font-size: 14px;
  }
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 图表加载状态 */
.chart-loading {
  position: relative;
}

.chart-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 5;
}

/* 表格优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>