<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="emergency-event-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件来源" prop="eventSourceName">
            <el-input v-model="formData.eventSourceName" placeholder="请输入事件来源" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件标题" prop="eventTitle">
            <el-input v-model="formData.eventTitle" placeholder="请输入事件标题" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事件描述" prop="eventDesc">
            <el-input v-model="formData.eventDesc" type="textarea" :rows="2" placeholder="请输入事件描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件分类" prop="eventType">
            <el-select v-model="formData.eventType" placeholder="请选择" class="w-full" @change="handleEventTypeChange">
              <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件分级" prop="eventLevel">
            <el-select v-model="formData.eventLevel" placeholder="请选择" class="w-full">
              <el-option v-for="item in eventLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发生时间" prop="eventTime">
            <el-date-picker
              v-model="formData.eventTime"
              type="datetime"
              placeholder="请选择发生时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件位置" prop="ownershipUnit">
            <el-input v-model="formData.ownershipUnit" placeholder="请输入事件位置" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="当前是否人员伤亡" prop="isCasualty">
        <el-radio-group v-model="formData.isCasualty">
          <el-radio label="0">否</el-radio>
          <el-radio label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-row :gutter="20" v-if="formData.isCasualty === '1'">
        <el-col :span="12">
          <el-form-item label="死亡人数">
            <el-input-number v-model="formData.deathNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="受伤人数">
            <el-input-number v-model="formData.injuredNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="上报人员联系方式">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="接收时间">
            <el-date-picker
              v-model="formData.receiveTime"
              type="datetime"
              placeholder="请选择接收时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理完成时间">
            <el-date-picker
              v-model="formData.handleTime"
              type="datetime"
              placeholder="请选择处理完成时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联处置方案" prop="responsePlan">
            <el-select v-model="formData.responsePlan" placeholder="请选择" class="w-full" disabled>
              <el-option v-for="item in responsePlanOptions" :key="item.id" :label="item.schemeName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件处置状态" prop="eventStatus">
            <el-radio-group v-model="formData.eventStatus">
              <el-radio :label="3004201">未处置</el-radio>
              <el-radio :label="3004202">处置中</el-radio>
              <el-radio :label="3004203">已处置</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveEmergencyEvent,
  updateEmergencyEvent,
  getFloodEmergencySchemeList
} from '@/api/drainage';
import { 
  EVENT_TYPE_OPTIONS, 
  EVENT_LEVEL_OPTIONS,
  EVENT_STATUS_OPTIONS,
  IS_CASUALTY_OPTIONS 
} from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增事件',
    edit: '编辑事件',
    view: '事件详情'
  };
  return titles[props.mode] || '事件信息';
});

// 下拉选项数据
const eventTypeOptions = EVENT_TYPE_OPTIONS;
const eventLevelOptions = EVENT_LEVEL_OPTIONS;
const eventStatusOptions = EVENT_STATUS_OPTIONS;
const isCasualtyOptions = IS_CASUALTY_OPTIONS;
const areaOptions = ref(AREA_OPTIONS);
const responsePlanOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  eventSourceName: '',
  eventTitle: '',
  eventDesc: '',
  eventType: '',
  eventTypeName: '',
  eventLevel: '',
  eventLevelName: '',
  eventTime: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  address: '',
  longitude: '',
  latitude: '',
  isCasualty: '0',
  deathNum: 0,
  injuredNum: 0,
  contactInfo: '',
  receiveTime: '',
  handleTime: '',
  responsePlan: '',
  eventStatus: 3004201,
  eventStatusName: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  eventSourceName: [{ required: true, message: '请输入事件来源', trigger: 'blur' }],
  eventTitle: [{ required: true, message: '请输入事件标题', trigger: 'blur' }],
  eventDesc: [{ required: true, message: '请输入事件描述', trigger: 'blur' }],
  eventType: [{ required: true, message: '请选择事件分类', trigger: 'change' }],
  eventLevel: [{ required: true, message: '请选择事件分级', trigger: 'change' }],
  eventTime: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
  isCasualty: [{ required: true, message: '请选择是否人员伤亡', trigger: 'change' }],
  eventStatus: [{ required: true, message: '请选择事件处置状态', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'eventStatus') {
      formData[key] = 3004201;
    } else if (key === 'isCasualty') {
      formData[key] = '0';
    } else if (key === 'deathNum' || key === 'injuredNum') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理事件分类变化，获取对应的处置方案
const handleEventTypeChange = async (value) => {
  if (value) {
    const selected = eventTypeOptions.find(item => item.value === value);
    if (selected) {
      formData.eventTypeName = selected.label;
    }
    
    // 获取对应的处置方案
    try {
      const res = await getFloodEmergencySchemeList({ eventType: value });
      if (res && res.data) {
        responsePlanOptions.value = res.data;
        // 如果只有一个方案，自动选中
        if (res.data.length === 1) {
          formData.responsePlan = res.data[0].id;
        }
      }
    } catch (error) {
      console.error('获取处置方案失败', error);
    }
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 事件分类
  const selectedEventType = eventTypeOptions.find(item => item.value === formData.eventType);
  if (selectedEventType) {
    formData.eventTypeName = selectedEventType.label;
  }

  // 事件分级
  const selectedEventLevel = eventLevelOptions.find(item => item.value === formData.eventLevel);
  if (selectedEventLevel) {
    formData.eventLevelName = selectedEventLevel.label;
  }

  // 事件状态
  const selectedEventStatus = eventStatusOptions.find(item => item.value === formData.eventStatus);
  if (selectedEventStatus) {
    formData.eventStatusName = selectedEventStatus.label;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveEmergencyEvent(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEmergencyEvent(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 初始化处置方案
const initResponsePlans = async () => {
  if (formData.eventType) {
    try {
      const res = await getFloodEmergencySchemeList({ eventType: formData.eventType });
      if (res && res.data) {
        responsePlanOptions.value = res.data;
      }
    } catch (error) {
      console.error('获取处置方案失败', error);
    }
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  initResponsePlans();
});
</script>

<style scoped>
.emergency-event-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  border-color: #ffffff;
}

:deep(.el-button--primary:hover) {
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 