<template>
  <div class="bridge-device-info-container">
    <div class="bg-white rounded-lg shadow">
      <el-tabs v-model="activeTab" class="device-tabs">
        <el-tab-pane label="监测点信息" name="monitoring">
          <MonitoringPoints />
        </el-tab-pane>
        <!-- <el-tab-pane label="视频监控" name="video">
          <VideoMonitoring />
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import MonitoringPoints from './components/MonitoringPoints.vue'
import VideoMonitoring from './components/VideoMonitoring.vue'

// 当前激活的选项卡
const activeTab = ref('monitoring')

onMounted(() => {
  console.log('桥梁设备信息组件已挂载')
})
</script>

<style scoped>
.bridge-device-info-container {
  padding: 20px;
}

.device-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.mb-4 {
  margin-bottom: 1rem;
}

.bg-white {
  background-color: white;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
</style> 