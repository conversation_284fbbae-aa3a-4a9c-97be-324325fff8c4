<template>
  <div class="expert-apply-container">
    <!-- 搜索区域 -->
    <div class="expert-apply-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">申请状态:</span>
          <el-select v-model="formData.applyStatus" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in applyStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.expertName" class="form-input" placeholder="输入专家姓名" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" empty-text="暂无数据"
      v-loading="loading" :fit="true">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="expertName" label="专家名称" min-width="120" />
      <el-table-column prop="applyUser" label="申请人" min-width="100" />
      <el-table-column prop="applyContact" label="申请人联系电话" min-width="140" />
      <el-table-column prop="applyTime" label="申请时间" min-width="140">
        <template #default="{ row }">
          {{ formatDateTime(row.applyTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="applyStatusName" label="申请状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.applyStatus)">
            {{ row.applyStatusName || '未回复' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="replyTime" label="回复时间" min-width="140">
        <template #default="{ row }">
          {{ formatDateTime(row.replyTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleView(row)">查看</span>
              <span class="operation-divider" v-if="row.applyStatus === 7001001">|</span>
              <span class="operation-btn-text" v-if="row.applyStatus === 7001001" @click.stop="handleReply(row)">回复</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <ExpertApplyDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElTag } from 'element-plus'
import { 
  getExpertApplyPage, 
  deleteExpertApply, 
  getExpertApplyDetail,
} from '@/api/comprehensive'
import ExpertApplyDialog from './ExpertApplyDialog.vue'
import moment from 'moment'
import { APPLY_STATUS_OPTIONS } from '@/constants/comprehensive'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(500)

// 状态选项
const applyStatusOptions = ref(APPLY_STATUS_OPTIONS)

// 表单数据
const formData = ref({
  applyStatus: '',
  expertName: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view' | 'reply'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '/'
  return moment(dateTime).format('YYYY-MM-DD HH:mm')
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    7001001: 'warning', // 未回复
    7001002: 'success', // 同意申请
    7001003: 'danger'   // 驳回申请
  }
  return statusMap[status] || 'info'
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchExpertApplyData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    applyStatus: '',
    expertName: ''
  }
  currentPage.value = 1
  fetchExpertApplyData()
}

// 获取专家申请分页数据
const fetchExpertApplyData = async () => {
  loading.value = true;
  try {
    const params = {
      applyStatus: formData.value.applyStatus,
      expertName: formData.value.expertName
    }
    
    const res = await getExpertApplyPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取专家申请数据失败:', error)
    ElMessage.error('获取专家申请数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchExpertApplyData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchExpertApplyData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理查看
const handleView = async (row) => {
  try {
    const res = await getExpertApplyDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取专家申请详情失败')
    }
  } catch (error) {
    console.error('获取专家申请详情失败:', error)
    ElMessage.error('获取专家申请详情失败')
  }
}

// 处理回复
const handleReply = async (row) => {
  try {
    const res = await getExpertApplyDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'reply'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取专家申请详情失败')
    }
  } catch (error) {
    console.error('获取专家申请详情失败:', error)
    ElMessage.error('获取专家申请详情失败')
  }
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchExpertApplyData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.expert-apply-container');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;
    const searchSection = container.querySelector('.expert-apply-search');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;
    const tableHeader = container.querySelector('.table-header');
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 48;
    const paginationReservedHeight = 60;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - searchHeight - tableHeaderHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

// 在组件挂载后获取数据
onMounted(() => {
  fetchExpertApplyData()
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleResize);
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.expert-apply-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.expert-apply-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>